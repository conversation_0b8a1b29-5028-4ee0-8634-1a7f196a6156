# 心跳包超时重连消息重复问题修复

## 问题描述

在心跳包超时触发重连时，出现消息重复的问题。根本原因是：

1. **透明重连时连接清理不彻底** - 旧连接的回调仍然可能被触发
2. **缺少连接状态检查** - 旧连接的延迟回调在新连接建立后仍然被执行

## 修复方案

### 1. 修复透明重连时的连接清理问题

**文件**: `src/tina/services/chat-service-manager.ts`

**修改**: 在 `performTransparentReconnect` 方法中，关闭旧连接前先更新连接状态：

```typescript
// 断开当前连接 - 关键修复：先更新状态再关闭连接
if (this.connection) {
  console.log('🔌 [ChatServiceManager] 正在关闭旧连接')
  
  // 先更新状态为断开，这样旧连接的回调就不会继续处理消息
  this.updateConnectionStatus('disconnected')
  
  // 关闭连接
  this.connection.close()
  this.connection = null
  
  console.log('✅ [ChatServiceManager] 旧连接已关闭')
}
```

### 2. 添加连接状态检查

**文件**: `src/pages/wechat/conversation/context.tsx`

**修改**: 在 `handleUserMessage` 和 `handleLLMResponse` 中添加连接状态检查：

```typescript
// 如果连接已断开，不处理消息（可能是旧连接的延迟回调）
if (connectionStatus === 'disconnected') {
  console.log('🚫 [handleUserMessage] 连接已断开，跳过消息处理:', message.message_id)
  return
}
```

## 修复效果

1. **消息不重复** - 旧连接的延迟回调被正确忽略
2. **重连更稳定** - 连接状态立即更新，避免状态混乱
3. **调试更容易** - 增加了详细的日志记录

## 测试方法

### 方法1: 临时缩短心跳超时时间

```typescript
// 在 chat-service-manager.ts 中临时修改
private readonly heartbeatTimeoutMs: number = 5 * 1000 // 改为5秒，便于测试
```

### 方法2: 使用测试工具

```typescript
// 在浏览器控制台中运行
import('./src/tina/services/chat-service-manager.reconnect-fix-test.ts')
  .then(() => window.testReconnectFix.runAll())
```

### 方法3: 手动测试

1. 打开聊天页面
2. 发送消息
3. 等待心跳超时（默认3分钟）
4. 观察日志，确认：
   - 透明重连被触发
   - 旧连接状态立即更新为 `disconnected`
   - 任何延迟回调被跳过
   - 没有重复消息出现

## 关键日志标识

修复后，在重连过程中应该看到以下日志：

```
🔄 [ChatServiceManager] 开始透明重连
🔌 [ChatServiceManager] 正在关闭旧连接
✅ [ChatServiceManager] 旧连接已关闭
🚫 [handleUserMessage] 连接已断开，跳过消息处理: xxx
🚫 [handleLLMResponse] 连接已断开，跳过消息处理: xxx
✅ [ChatServiceManager] 透明重连成功
```

## 注意事项

1. **保持原有逻辑** - 修复只针对核心问题，不改变其他重连逻辑
2. **向后兼容** - 修改不影响现有功能
3. **最小化修改** - 只修改必要的部分，避免引入新问题

## 恢复正常配置

测试完成后，记得将心跳超时时间恢复为正常值：

```typescript
private readonly heartbeatTimeoutMs: number = 3 * 60 * 1000 // 3分钟
```
