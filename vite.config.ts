import { resolve } from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import svgr from 'vite-plugin-svgr'

// 🧠 根据构建模式决定 base 路径
export default defineConfig(({ mode }) => {
  const isRelease = process.env.BUILD_ENV === 'release' // 👈 只关心这一个变量
  const basePath = isRelease ? './' : '/new_mvp/' // 👈 默认路径是 /new_mvp/

  console.log(
    `🛠️ 构建中... base = "${basePath}"，BUILD_ENV=${process.env.BUILD_ENV}`,
  )

  return {
    base: basePath,
    plugins: [
      react({
        jsxImportSource: '@emotion/react',
        plugins: [['@swc/plugin-emotion', {}]],
      }),
      svgr({
        include: '**/*.svg?react',
        svgrOptions: {
          exportType: 'default',
        },
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },
    optimizeDeps: {
      include: [
        '@ionic/react',
        '@ionic/react-router',
        'react',
        'react-dom',
        'react-router-dom',
        'prop-types',
      ],
    },

    build: {
      chunkSizeWarningLimit: 1024,
      rollupOptions: {
        output: {
          manualChunks: {
            react: ['react', 'react-dom', 'react-router-dom'],
            antd: ['antd', '@ant-design/icons', 'dayjs'],
            slate: ['slate', 'slate-history', 'slate-react'],
            faker: ['@faker-js/faker'],
            i18n: [
              'i18next',
              'i18next-browser-languagedetector',
              'i18next-http-backend',
              'react-i18next',
            ],
          },
        },
      },
    },
    server: {
      allowedHosts: ['wt.tina-dev.tech'], // 添加这一行
    },

  }
})
