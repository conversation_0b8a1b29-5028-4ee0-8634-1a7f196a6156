// 简单的内存数据库模拟
export interface IDBImage {
  id: string
  url: string
  name: string
  size: number
}

class MockDB {
  private images: Map<string, IDBImage> = new Map()

  // 保存图片信息
  saveImage(image: IDBImage): void {
    this.images.set(image.id, image)
  }

  // 获取图片信息
  getImage(id: string): IDBImage | undefined {
    return this.images.get(id)
  }

  // 获取所有图片
  getAllImages(): IDBImage[] {
    return Array.from(this.images.values())
  }

  // 删除图片
  deleteImage(id: string): boolean {
    return this.images.delete(id)
  }
}

export const db = new MockDB()

// 图片缓存存储
export const IMAGES_CACHE = new Map<string, string>()

// 初始化图片缓存存储
export function initDBImagesCacheStore(): void {
  // 初始化图片缓存存储的逻辑
  console.log('图片缓存存储已初始化')
}
