/**
 * 工具名称映射配置
 * 统一管理后端技术名词与前端显示名词的映射关系
 */

// 工具名称映射类型定义
export interface ToolNameMapping {
  /** 后端专用名词(Technical Term) */
  technicalTerm: string
  /** 前端显示名词(Show to User) */
  displayName: string
  /** 工具图标 */
  icon: string
  /** 通知文本模板 */
  notificationText: string
}

// 工具名称映射配置
export const TOOL_NAME_MAPPINGS: ToolNameMapping[] = [
  {
    technicalTerm: 'bilibili_search',
    displayName: '视频搜索',
    icon: 'fa-solid fa-video',
    notificationText: '视频搜索工具返回结果..',
  },
  {
    technicalTerm: 'calculator',
    displayName: '计算器',
    icon: 'fa-solid fa-calculator',
    notificationText: '计算工具返回结果..',
  },
  {
    technicalTerm: 'fact_check',
    displayName: '事实核查工具',
    icon: 'fa-solid fa-shield-check',
    notificationText: '事实核查工具返回结果..',
  },
  {
    technicalTerm: 'hot_news',
    displayName: '热点新闻',
    icon: 'fa-solid fa-newspaper',
    notificationText: '热点新闻工具返回结果..',
  },
  {
    technicalTerm: 'newsNow_hotspot_query',
    displayName: '热点新闻',
    icon: 'fa-solid fa-newspaper',
    notificationText: '热点工具返回结果..',
  },
  {
    technicalTerm: 'image_edit',
    displayName: '图片编辑工具',
    icon: 'fa-solid fa-image',
    notificationText: '图片编辑工具返回结果..',
  },
  {
    technicalTerm: 'image_get',
    displayName: '图片获取工具',
    icon: 'fa-solid fa-image',
    notificationText: '图片获取工具返回结果..',
  },
  {
    technicalTerm: 'img_generate',
    displayName: '图片生成工具',
    icon: 'fa-solid fa-image',
    notificationText: '图片生成工具返回结果..',
  },
  {
    technicalTerm: 'image_search',
    displayName: '图片搜索工具',
    icon: 'fa-solid fa-image',
    notificationText: '图片搜索工具返回结果..',
  },
  {
    technicalTerm: 'memory_query',
    displayName: '记忆搜索工具',
    icon: 'fa-solid fa-brain',
    notificationText: '记忆搜索工具返回结果..',
  },
  {
    technicalTerm: 'smzdm_search',
    displayName: '电商搜索',
    icon: 'fa-solid fa-shopping-cart',
    notificationText: '电商搜索工具返回结果..',
  },
  {
    technicalTerm: 'tina_task_create_plan',
    displayName: 'Tina task 计划创建工具',
    icon: 'fa-solid fa-pencil',
    notificationText: '调用 TinaTask 创建计划..',
  },
  {
    technicalTerm: 'tina_task_dispatch_command',
    displayName: 'Tina task 命令工具',
    icon: 'fa-solid fa-cog',
    notificationText: '调用 TinaTask 发送命令..',
  },
  {
    technicalTerm: 'tina_task_query_detail',
    displayName: 'Tina task 状态查询工具',
    icon: 'fa-solid fa-question',
    notificationText: '调用 TinaTask 查询状态..',
  },
  {
    technicalTerm: 'weather_query',
    displayName: '天气工具',
    icon: 'fa-solid fa-temperature-half',
    notificationText: '天气工具返回结果..',
  },
  {
    technicalTerm: 'web_search',
    displayName: '联网搜索',
    icon: 'fa-solid fa-magnifying-glass',
    notificationText: '搜索工具返回结果..',
  },
]

// 创建快速查找的Map
export const toolNameMap = new Map<string, ToolNameMapping>(
  TOOL_NAME_MAPPINGS.map((mapping) => [mapping.technicalTerm, mapping]),
)

/**
 * 根据工具名称模糊匹配获取工具配置
 * 支持部分匹配，用于处理包含工具名称的字符串
 * @param toolName 工具名称（可能包含其他字符）
 * @returns 匹配的工具配置，如果未找到则返回默认配置
 */
export function getToolConfigByPartialMatch(toolName: string): {
  displayName: string
  icon: string
  notificationText: string
} {
  // 首先尝试精确匹配
  const exactMatch = toolNameMap.get(toolName)
  if (exactMatch) {
    return {
      displayName: exactMatch.displayName,
      icon: exactMatch.icon,
      notificationText: exactMatch.notificationText,
    }
  }

  // 尝试部分匹配
  for (const [technicalTerm, mapping] of toolNameMap) {
    if (toolName.includes(technicalTerm)) {
      return {
        displayName: mapping.displayName,
        icon: mapping.icon,
        notificationText: mapping.notificationText,
      }
    }
  }

  // 特殊处理一些常见的模糊匹配情况
  if (toolName.includes('newsNow') || toolName.includes('hotspot')) {
    return {
      displayName: '热点新闻',
      icon: 'fa-solid fa-newspaper',
      notificationText: '热点工具返回结果..',
    }
  }

  if (toolName.includes('search')) {
    return {
      displayName: '搜索工具',
      icon: 'fa-solid fa-magnifying-glass',
      notificationText: '搜索工具返回结果..',
    }
  }

  if (toolName.includes('image') || toolName.includes('img')) {
    return {
      displayName: '图片工具',
      icon: 'fa-solid fa-image',
      notificationText: '图片工具返回结果..',
    }
  }

  if (toolName.includes('tina_task')) {
    return {
      displayName: 'Tina Task 工具',
      icon: 'fa-solid fa-cog',
      notificationText: '调用 TinaTask 工具..',
    }
  }

  // 默认配置
  return {
    displayName: '未知工具',
    icon: 'fa-solid fa-screwdriver-wrench',
    notificationText: '未知工具返回结果..',
  }
}
