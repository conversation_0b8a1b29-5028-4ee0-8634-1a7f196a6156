import {
  EConversationType,
  TConversationItem,
  TConversationRole,
} from '@/stateV2/conversation'
import type { Descendant } from 'slate'
import { StreamOutput } from '@/utils/TextStreamParser.ts'
import { getToolConfigByPartialMatch } from './tool-name-mapping'

/**
 * 将文本中的英文标签替换为中文标签
 * @param text 输入文本
 * @returns 替换后的中文标签文本
 */
function translateTags(text: string): string {
  // 中英文标签映射表
  const tagMap: { [key: string]: string } = {
    // 方括号标签
    '[breath]': ' ',
    '[noise]': ' ',
    '[laughter]': ' ',
    '[cough]': ' ',
    '[clucking]': ' 呵呵 ',
    '[accent]': ' ',
    '[quick_breath]': ' ',
    '[hissing]': '嘶',
    '[sigh]': ' 哎 ',
    '[vocalized-noise]': ' ',
    '[lipsmack]': ' 啧 ',
    '[mn]': ' 嗯呢 ',

    // HTML 风格标签
    '<strong>': '',
    '</strong>': '',
    '<laughter>': '',
    '</laughter>': '',

    // 特殊对话标记
    '<|im_start|>': '',
    '<|im_end|>': '',
    '<|endofprompt|>': '',
  }

  // 匹配所有方括号标签、尖括号标签和特殊标记
  const regex = /(\[.*?\]|<.*?>)/g

  return text.replace(regex, (match) => {
    // 优先使用精确匹配，若未找到则尝试大小写不敏感匹配
    return tagMap[match] || tagMap[match.toLowerCase()] || match
  })
}

/**
 * 根据文本长度估算朗读时间（支持中英文混合）
 * @param text 要朗读的文本
 * @param wordsPerMinute 语速 (中文等价字数/分钟，默认180)
 * @param englishRatio 英文单词换算比率 (1个英文单词 = X个中文字符，默认2)
 * @returns 预估的朗读时间（秒，四舍五入到整数）
 */
function estimateSpeechTime(
  text: string,
  wordsPerMinute: number = 180,
  englishRatio: number = 2,
): number {
  // 需要过滤的特殊标签和符号
  const FILTER_PATTERNS = [
    '\\[breath\\]',
    '<strong>',
    '</strong>',
    '\\[noise\\]',
    '\\[laughter\\]',
    '\\[cough\\]',
    '\\[clucking\\]',
    '\\[accent\\]',
    '\\[quick_breath\\]',
    '<\\|im_start\\|>',
    '<\\|im_end\\|>',
    '<\\|endofprompt\\|>',
    '<laughter>',
    '</laughter>',
    '\\[hissing\\]',
    '\\[sigh\\]',
    '\\[vocalized-noise\\]',
    '\\[lipsmack\\]',
    '\\[mn\\]',
  ]

  // 清洗步骤：1. 移除所有特殊标签
  const cleaned = text.replace(new RegExp(FILTER_PATTERNS.join('|'), 'gi'), '')

  // 2. 分割中英文字符
  const chineseChars = cleaned.replace(/[^\p{Script=Han}]/gu, '')
  const englishPart = cleaned.replace(/[\p{Script=Han}]/gu, '')

  // 3. 统计英文单词数量 (按空格分割)
  const englishWords = englishPart
    .split(/\s+/)
    .filter((word) => word.length > 0).length

  // 计算总等效字数
  const totalChars = chineseChars.length + englishWords * englishRatio

  // 计算持续时间（秒）
  const seconds = (totalChars / wordsPerMinute) * 60

  return Math.round(Math.max(1, seconds)) // 至少1秒
}

/**
 * 根据标点符号分割第一句话
 * @param text 输入文本
 * @returns [第一句话, 剩余内容]
 */
function splitFirstSentence(text: string): [string, string] {
  // 常见的句子结束标点
  const sentenceEnders = /[。！？；.!?;]/

  const match = text.match(sentenceEnders)
  if (match && match.index !== undefined) {
    const firstSentence = text.substring(0, match.index + 1).trim()
    const remaining = text.substring(match.index + 1).trim()
    return [firstSentence, remaining]
  }

  // 如果没有找到标点，返回整个文本作为第一句
  return [text.trim(), '']
}

/**
 * 工具通知配置
 */
export interface ToolNotificationConfig {
  text: string
  icon: string
  hideAfter?: number
}

/**
 * 获取工具通知配置
 */
export function getToolNotificationConfig(
  toolName: string,
): ToolNotificationConfig {
  // 使用统一的工具配置映射
  const toolConfig = getToolConfigByPartialMatch(toolName)

  return {
    text: toolConfig.notificationText,
    icon: toolConfig.icon,
    hideAfter: -1,
  }
}

/**
 * 处理XML卡片内容
 */
export function processXmlCard(
  content: string,
  messageIdType: string,
): TConversationItem | null {
  const cardRegex =
    /<card\s*((?:\s+\w+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^>\s]*))?)*)\s*>([^<]*(?:<(?!\/card>)[^<]*)*)<\/card>/i
  const cardMatch = content.match(cardRegex)

  if (!cardMatch) return null

  const attributesStr = cardMatch[1] || ''
  const cardContent = cardMatch[2] || ''

  // 解析属性
  const attrs: Record<string, string> = {}
  const attrRegex = /(\w+)(?:\s*=\s*(?:"([^"]*)"|'([^']*)'|([^>\s]*)))?/g
  let match: RegExpExecArray | null

  while ((match = attrRegex.exec(attributesStr)) !== null) {
    const attrName = match[1]
    // 属性值可以是双引号、单引号中的内容，或者无引号的内容
    const attrValue = match[2] || match[3] || match[4] || ''
    attrs[attrName] = attrValue
  }

  const { type, subtype, title, author, img, url, size, stt } = attrs

  // 处理语音卡片
  if (type === 'voice') {
    if (!stt) {
      console.warn('语音卡片缺少stt属性')
      return null
    }

    // 处理语音内容，移除特殊标签并翻译
    const translatedContent = translateTags(stt)

    // 分割第一句话
    const [firstSentence, remainingContent] =
      splitFirstSentence(translatedContent)

    // 估算总时长
    const totalDuration = estimateSpeechTime(translatedContent)

    return {
      id: messageIdType,
      type: EConversationType.voice,
      role: 'friend' as TConversationRole,
      duration: totalDuration,
      isRead: false,
      showStt: true,
      stt: translatedContent,
      originalStt: translatedContent,
      streamState: 'complete', // 完整语音卡片直接标记为完成状态
      firstSegment: firstSentence,
      fullContent: translatedContent,
      remainingContent: remainingContent || undefined,
    }
  }

  // 处理视频卡片
  if (type === 'media' && subtype === 'video') {
    return {
      id: messageIdType,
      type: EConversationType.video,
      role: 'friend' as TConversationRole,
      videoInfo: img, // 使用封面图片作为视频信息
      videoUrl: url, // 添加视频URL
    }
  }

  // 处理图片卡片
  if ((type === 'media' && subtype === 'image') || type === 'image') {
    const imageUrl = img || url // 优先使用img，如果没有则使用url
    return {
      id: messageIdType,
      type: EConversationType.image,
      role: 'friend' as TConversationRole,
      imageInfo: imageUrl,
    }
  }

  // 处理文件卡片
  if (type === 'file') {
    const description = size ? `文件大小：${size}` : '点击下载文件'
    return {
      id: messageIdType,
      type: EConversationType.news,
      role: 'friend' as TConversationRole,
      title: title || '文件',
      description: description,
      imageUrl: img || '',
      source: author || '文件分享',
      url: url || '',
    }
  }

  // 处理链接卡片
  if (type === 'link') {
    return {
      id: messageIdType,
      type: EConversationType.news,
      role: 'friend' as TConversationRole,
      title: title || '链接',
      description: cardContent || '点击查看详情',
      imageUrl: img || '',
      source: author || '链接分享',
      url: url || '',
    }
  }

  // 处理思维导图卡片
  if (type === 'markmap') {
    // 预处理内容：去除开头的 ``` 或 ```markdown 和结尾的 ```，以及两头的空行
    let processedContent = cardContent || ''
    processedContent = processedContent.trim() // 去除两头空行

    // 去除开头的代码块标记
    if (processedContent.startsWith('```markdown')) {
      processedContent = processedContent.substring(11).trim()
    } else if (processedContent.startsWith('```')) {
      processedContent = processedContent.substring(3).trim()
    }

    // 去除结尾的代码块标记
    if (processedContent.endsWith('```')) {
      processedContent = processedContent
        .substring(0, processedContent.length - 3)
        .trim()
    }

    return {
      id: messageIdType,
      type: EConversationType.markmap,
      role: 'friend' as TConversationRole,
      title: title || '思维导图',
      content: processedContent,
    }
  }

  // 处理新闻卡片
  if (type === 'news') {
    return {
      id: messageIdType,
      type: EConversationType.news,
      role: 'friend' as TConversationRole,
      title: title || '新闻标题',
      description: cardContent,
      imageUrl: img || '',
      source: author || '未知来源',
      url: url || '',
    }
  }

  return null
}

/**
 * 处理用户可能说的建议
 */
export function processUserMaybeSay(
  content: string,
  messageIdType: string,
): TConversationItem | null {
  const xmlMatch = content.match(/<user_maybe_say>([\s\S]*?)<\/user_maybe_say>/)
  if (!xmlMatch) return null

  const suggestionsText = xmlMatch[1].trim()
  if (!suggestionsText) return null

  const suggestions = suggestionsText
    .split('\n')
    .map((line: string) => line.trim())
    .filter((line: string) => line.length > 0)

  if (suggestions.length === 0) return null

  return {
    id: messageIdType,
    type: EConversationType.userMaybeSay,
    role: 'friend' as TConversationRole,
    suggestions,
  }
}

/**
 * 创建文本消息
 */
export function createTextMessage(
  messageIdType: string,
  content: string,
  role: TConversationRole = 'friend',
): TConversationItem {
  return {
    id: messageIdType,
    type: EConversationType.text,
    role,
    textContent: [{ text: content }] as Descendant[],
  }
}

/**
 * 创建Markdown消息
 */
export function createMarkdownMessage(
  messageIdType: string,
  content: string,
): TConversationItem {
  return {
    id: messageIdType,
    type: EConversationType.markdown,
    role: 'friend' as TConversationRole,
    markdownContent: content,
  }
}

/**
 * 创建Voice消息
 */
export function createVoiceMessage(
  messageIdType: string,
  content: string,
): TConversationItem {
  // 检查content是否以```结尾，如果是则移除并标记为完整
  const isComplete = content.trim().endsWith('```')
  const cleanContent = isComplete
    ? content.replace(/```\s*$/, '').trim()
    : content

  // 处理语音内容，移除特殊标签并翻译
  const translatedContent = translateTags(cleanContent)

  // 分割第一句话
  const [firstSentence, remainingContent] =
    splitFirstSentence(translatedContent)

  // 估算总时长
  const totalDuration = estimateSpeechTime(translatedContent)

  // 确定初始状态
  let streamState: 'creating' | 'first-buffered' | 'complete' = 'creating'

  if (firstSentence) {
    streamState = 'first-buffered'
  }

  if (isComplete) {
    streamState = 'complete'
  }

  const result = {
    id: messageIdType,
    type: EConversationType.voice as const,
    role: 'friend' as TConversationRole,
    duration: totalDuration,
    isRead: false,
    showStt: true,
    stt: translatedContent,
    originalStt: cleanContent,
    streamState: streamState,
    firstSegment: firstSentence,
    fullContent: translatedContent,
    remainingContent: remainingContent || undefined,
  }

  return result
}

/**
 * 更新现有消息的内容
 */
export function updateMessageContent(
  existingItem: TConversationItem,
  newContent: string,
): TConversationItem {
  if (existingItem.type === EConversationType.text) {
    return {
      ...existingItem,
      textContent: [{ text: newContent }] as Descendant[],
    }
  } else if (existingItem.type === EConversationType.markdown) {
    return {
      ...existingItem,
      markdownContent: newContent,
    }
  } else if (existingItem.type === EConversationType.voice) {
    // 如果voice消息已经完成，不再接收更新
    if (existingItem.streamState === 'complete') {
      return existingItem
    }

    // 检查新内容是否以```结尾，如果是则移除并标记为完整
    const isComplete = newContent.trim().endsWith('```')
    const cleanContent = isComplete
      ? newContent.replace(/```\s*$/, '').trim()
      : newContent

    // 处理语音内容的流式更新
    const translatedContent = translateTags(cleanContent)
    const [firstSentence, remainingContent] =
      splitFirstSentence(translatedContent)
    const totalDuration = estimateSpeechTime(translatedContent)

    // 确定新的流式状态
    let newStreamState: 'creating' | 'first-buffered' | 'complete' =
      existingItem.streamState || 'creating'

    // 如果有第一句话且之前是创建状态，更新为已缓冲第一句
    if (firstSentence && newStreamState === 'creating') {
      newStreamState = 'first-buffered'
    }

    // 如果检测到```结束标记，状态为完整
    if (isComplete) {
      newStreamState = 'complete'
    }

    const result = {
      ...existingItem,
      duration: totalDuration,
      stt: translatedContent,
      originalStt: cleanContent,
      streamState: newStreamState,
      firstSegment: firstSentence,
      fullContent: translatedContent,
      remainingContent: remainingContent || undefined,
    }

    return result
  } else if (existingItem.type === EConversationType.markmap) {
    // 处理思维导图内容
    let processedContent = newContent
    if (processedContent.startsWith('```markdown')) {
      processedContent = processedContent
        .replace(/^```markdown\s*/, '')
        .replace(/\s*```$/, '')
    }

    return {
      ...existingItem,
      content: processedContent,
    }
  }

  return existingItem
}

/**
 * 过滤内容，移除特定的XML标签
 */
export function filterContent(content: string): string {
  return content
    .replace(/<user_maybe_say>[\s\S]*?<\/user_maybe_say>/g, '')
    .replace(/<tina_task[\s\S]*?<\/tina_task>/g, '')
    .trim()
}

/**
 * 主要的流式消息转换函数
 */
export function convertStreamOutputToConversationItem(
  output: StreamOutput,
): TConversationItem | null {
  const { message_id_type, content } = output
  const contentType = message_id_type.split(':')[1] // 获取类型部分，如 "text", "xml", "markdown", "voice"

  // 处理XML类型
  if (contentType === 'xml') {
    // 尝试处理XML卡片
    const cardItem = processXmlCard(content, message_id_type)
    if (cardItem) {
      return cardItem
    }

    // 尝试处理用户可能说的建议
    const userMaybeSayItem = processUserMaybeSay(content, message_id_type)
    if (userMaybeSayItem) {
      return userMaybeSayItem
    }

    return null
  }

  // 处理Markdown类型
  if (contentType === 'markdown') {
    return createMarkdownMessage(message_id_type, content)
  }

  // 处理Voice类型
  if (contentType === 'voice') {
    return createVoiceMessage(message_id_type, content)
  }

  // 处理文本类型（默认）
  // 先过滤掉XML标签
  const filteredContent = filterContent(content)

  // 检查内容是否为空或只包含空白字符
  const trimmedContent = filteredContent.trim()
  if (!trimmedContent) {
    return null
  }

  // 过滤掉开头和结尾的空行
  const lines = filteredContent.split('\n')
  let startIndex = 0
  let endIndex = lines.length - 1

  // 找到第一个非空行
  while (startIndex <= endIndex && lines[startIndex].trim() === '') {
    startIndex++
  }

  // 找到最后一个非空行
  while (endIndex >= startIndex && lines[endIndex].trim() === '') {
    endIndex--
  }

  // 如果所有行都是空行，跳过
  if (startIndex > endIndex) {
    return null
  }

  // 重新构建过滤后的内容
  const finalContent = lines.slice(startIndex, endIndex + 1).join('\n')

  return createTextMessage(message_id_type, finalContent)
}
