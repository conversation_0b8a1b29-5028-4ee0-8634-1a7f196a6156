/**
 * XMLContentParser.ts
 * 用于处理流式数据中的XML标签，过滤掉tina_task XML标签，只输出纯文本内容到界面
 */

// 解析器状态枚举
export enum ParserState {
  SEARCHING_XML, // 搜索XML状态
  POTENTIAL_XML_START, // 可能的XML开始状态 (检测到'<')
  IN_XML_TAG, // 正在XML标签内状态
}

/**
 * 解析结果接口
 */
export interface ParseResult {
  textContent: string // 过滤后的文本内容（用于UI显示）
  xmlContent?: string // 识别出的XML内容（如果有）
}

export class XMLContentParser {
  private state: ParserState = ParserState.SEARCHING_XML
  private xmlBuffer: string = '' // 存储XML内容
  private textBuffer: string = '' // 存储普通文本内容
  private potentialXmlBuffer: string = '' // 存储潜在的XML开始部分
  private xmlStartTag: string = '<tina_task>' // XML开始标签
  private xmlEndTag: string = '</tina_task>' // XML结束标签
  private xmlCount: number = 0 // 跟踪已处理的XML数量
  private debugMode: boolean = false // 是否启用详细调试

  /**
   * 构造函数
   * @param debug 是否启用详细日志，默认关闭
   */
  constructor(debug: boolean = true) {
    this.debugMode = debug
  }

  /**
   * 处理流式数据中的内容
   * @param content 收到的内容片段
   * @returns 解析结果，包含过滤后的文本内容和识别到的XML内容（如果有）
   */
  processContent(content: string): ParseResult {
    if (!content) return { textContent: '' }

    if (this.debugMode) {
      console.log(
        `[XMLParser] 处理内容(${content.length}): ${content.length > 30 ? content.substring(0, 30) + '...' : content}`,
      )
    }

    let uiContent = ''
    let xmlContent: string | undefined = undefined

    // 逐字符处理内容
    for (let i = 0; i < content.length; i++) {
      const char = content[i]

      switch (this.state) {
        case ParserState.SEARCHING_XML:
          // 搜索XML开始标记
          if (char === '<') {
            // 发现可能的XML开始，进入潜在XML开始状态
            this.state = ParserState.POTENTIAL_XML_START
            this.potentialXmlBuffer = '<'
            // 不输出到UI，暂时保存在潜在XML缓冲区
            if (this.debugMode)
              console.log(`[XMLParser] 发现可能的XML开始标签: <`)
          } else {
            // 普通文本，直接添加到UI输出
            uiContent += char
          }
          break

        case ParserState.POTENTIAL_XML_START:
          // 累积潜在XML开始标签
          this.potentialXmlBuffer += char

          // 检查是否匹配完整的XML开始标签
          if (this.xmlStartTag === this.potentialXmlBuffer) {
            // 匹配到完整的开始标签，进入XML标签内状态
            this.state = ParserState.IN_XML_TAG
            this.xmlBuffer = this.potentialXmlBuffer
            this.potentialXmlBuffer = ''
            if (this.debugMode)
              console.log(`[XMLParser] 确认XML开始标签: ${this.xmlStartTag}`)
          }
          // 检查是否仍可能匹配XML开始标签
          else if (!this.xmlStartTag.startsWith(this.potentialXmlBuffer)) {
            // 不再可能匹配开始标签，回到搜索状态，将缓冲的内容输出
            if (this.debugMode)
              console.log(
                `[XMLParser] 不匹配XML标签，输出缓冲区: ${this.potentialXmlBuffer}`,
              )
            uiContent += this.potentialXmlBuffer
            this.state = ParserState.SEARCHING_XML
            this.potentialXmlBuffer = ''
          }
          // 否则继续积累潜在XML开始标签
          break

        case ParserState.IN_XML_TAG:
          // 已经在XML标签内，累积XML内容
          this.xmlBuffer += char

          // 检查是否到达XML结束标签
          if (this.xmlBuffer.endsWith(this.xmlEndTag)) {
            // XML标签结束，记录完整XML并输出调试信息
            this.xmlCount++

            // 保存识别到的完整XML内容
            xmlContent = this.xmlBuffer

            // 始终输出最基本的XML过滤信息，但详细内容仅在调试模式下显示
            if (this.debugMode) {
              const xmlPreview =
                this.xmlBuffer.length > 100
                  ? this.xmlBuffer.substring(0, 100) +
                    `...(总${this.xmlBuffer.length}字符)`
                  : this.xmlBuffer
              console.log(
                `[XMLContentParser] 过滤XML #${this.xmlCount}: ${xmlPreview}`,
              )
            } else {
              // console.log(`[XMLContentParser] 过滤XML #${this.xmlCount} (${this.xmlBuffer.length}字符)`);
            }

            // 重置状态，回到搜索XML状态
            this.state = ParserState.SEARCHING_XML
            this.xmlBuffer = ''
          }
          break
      }
    }

    // 处理UI内容
    if (uiContent.length > 0 && this.debugMode) {
      console.log(
        `[XMLParser] 输出到UI(${uiContent.length}): ${uiContent.length > 30 ? uiContent.substring(0, 30) + '...' : uiContent}`,
      )
    }

    return {
      textContent: uiContent,
      xmlContent: xmlContent,
    }
  }

  /**
   * 获取当前解析器状态的调试信息
   */
  getDebugInfo(): string {
    return `状态: ${ParserState[this.state]}, XML缓冲区长度: ${this.xmlBuffer.length}, 潜在XML缓冲区: "${this.potentialXmlBuffer}"`
  }

  /**
   * 获取已处理的XML数量
   */
  getXmlCount(): number {
    return this.xmlCount
  }

  /**
   * 设置调试模式
   * @param debug 是否启用调试模式
   */
  setDebugMode(debug: boolean): void {
    this.debugMode = debug
  }

  /**
   * 获取当前调试模式状态
   */
  getDebugMode(): boolean {
    return this.debugMode
  }

  /**
   * 完成处理并返回任何未处理完的内容
   * 用于处理流结束的情况，确保剩余缓冲区内容被正确处理
   */
  finalize(): ParseResult {
    let finalContent = ''
    let incompleteXml: string | undefined = undefined

    // 处理未完成的潜在XML开始部分
    if (
      this.state === ParserState.POTENTIAL_XML_START &&
      this.potentialXmlBuffer.length > 0
    ) {
      finalContent += this.potentialXmlBuffer
      this.potentialXmlBuffer = ''
    }

    // 如果在XML标签内但未完成，记录并警告
    if (this.state === ParserState.IN_XML_TAG && this.xmlBuffer.length > 0) {
      incompleteXml = this.xmlBuffer

      if (this.debugMode) {
        console.warn(
          `[XMLParser] 警告: XML未正常结束，丢弃不完整的XML: ${
            this.xmlBuffer.length > 50
              ? this.xmlBuffer.substring(0, 50) + '...'
              : this.xmlBuffer
          }`,
        )
      } else {
        console.warn(
          `[XMLParser] 警告: XML未正常结束，丢弃不完整的XML (${this.xmlBuffer.length}字符)`,
        )
      }
    }

    // 重置状态
    this.reset()

    return {
      textContent: finalContent,
      xmlContent: incompleteXml,
    }
  }

  /**
   * 重置解析器状态
   */
  reset(): void {
    this.state = ParserState.SEARCHING_XML
    this.xmlBuffer = ''
    this.textBuffer = ''
    this.potentialXmlBuffer = ''
  }
}
