import { XMLParser } from 'fast-xml-parser'

export interface TinaTaskStep {
  index: number
  title: string
  tools_description: string
  tools: string
  content: string
  status?: 'pending' | 'running' | 'success'
  // tool call block content 独立区域
  toolCallBlocks?: string
}

export interface ToolsCallData {
  step: number
  tool_name: string
  parameters: Record<string, string>
}

export interface TinaTaskData {
  id: string
  type: string // 类型字段：'plan' | 'tools_call' | 'task_completed' 等
  content: string
  steps: TinaTaskStep[]
  tools_call?: ToolsCallData // tools_call 字段
  completed?: boolean // 任务是否完成
  user_maybe_say?: string[]
}

// 预处理XML字符串，处理特殊字符等
function preprocessXmlString(xmlStr: string): string {
  // 替换XML中的转义引号等
  return xmlStr
    .replace(/&(?!amp;|lt;|gt;|quot;|apos;)/g, '&amp;') // 处理未转义的&符号
    .replace(/\\"/g, '&quot;') // 替换转义的双引号
}

export function isXmlComplete(xmlContent: string): boolean {
  // 预处理XML内容
  const processedXml = preprocessXmlString(xmlContent)

  // 最简单直接的检查：确保包含开始和结束标签
  if (
    !processedXml.includes('<tina_task') ||
    !processedXml.includes('</tina_task>')
  ) {
    //console.log('XML不完整: 缺少开始或结束标签');
    return false
  }

  // 对于tina_task XML，我们已知其结构，如果包含了结束标签，视为完整
  if (processedXml.includes('</tina_task>')) {
    return true
  }

  return false
}

export function parseXmlTinaTask(xmlContent: string): TinaTaskData | null {
  try {
    // 预处理XML内容，替换转义引号
    const preprocessedXml = preprocessXmlString(xmlContent)

    // 确保内容是XML格式
    if (!preprocessedXml.includes('<tina_task')) {
      //console.log('不是tina_task XML');
      return null
    }

    // 先检查XML是否完整
    if (!isXmlComplete(preprocessedXml)) {
      //console.log('XML解析失败: XML尚未完整接收');
      return null
    }

    //console.log('开始解析完整的tina_task XML');

    // 特殊处理：移除XML声明，因为有些XML解析器对它处理有问题
    let processedXml = preprocessedXml
    if (processedXml.includes('<?xml')) {
      const xmlDeclEnd = processedXml.indexOf('?>') + 2
      if (xmlDeclEnd > 0) {
        processedXml = processedXml.substring(xmlDeclEnd).trim()
      }
    }

    // 创建解析器
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '',
      isArray: (name) => name === 'step' || name === 'param',
      parseAttributeValue: true,
      trimValues: true,
    })

    // 解析XML
    const result = parser.parse(processedXml)

    //console.log('XML解析结果:', JSON.stringify(result));

    if (!result.tina_task) {
      //console.log('结果中没有tina_task节点');
      return null
    }

    const tinaTask = result.tina_task
    //console.log('TinaTask对象:', JSON.stringify(tinaTask).substring(0, 200) + '...');

    // 创建TinaTaskData对象并设置默认值
    const taskData: TinaTaskData = {
      id: '',
      type: '',
      content: '',
      steps: [],
    }

    // 处理 meta_data 或 meta_data 标签（支持两种拼写）
    if (tinaTask.meta_data) {
      //console.log('找到meta_data:', JSON.stringify(tinaTask.meta_data));
      taskData.id = tinaTask.meta_data.id || ''
      taskData.type = tinaTask.meta_data.type || ''

      // 如果type为空，尝试将plan作为类型
      if (!taskData.type && tinaTask.plan) {
        taskData.type = 'plan'
      }
    } else if (tinaTask.meta_data) {
      //console.log('找到meta_data:', JSON.stringify(tinaTask.meta_data));
      taskData.id = tinaTask.meta_data.id || ''
      taskData.type = tinaTask.meta_data.type || ''

      // 如果type为空，尝试将plan作为类型
      if (!taskData.type && tinaTask.plan) {
        taskData.type = 'plan'
      }
    } else {
      // 向后兼容，如果没有 meta_data/meta_data 标签，尝试从顶层获取
      taskData.id = tinaTask.id || ''
      taskData.type = tinaTask.type || ''

      // 如果没有明确的类型，尝试根据存在的节点推断类型
      if (!taskData.type) {
        if (tinaTask.plan) {
          taskData.type = 'plan'
          //console.log('根据plan节点推断类型为plan');
        } else if (tinaTask.tools && !tinaTask.completed) {
          taskData.type = 'tools_call'
          //console.log('根据tools节点推断类型为tools_call');
        } else if (
          tinaTask.completed ||
          (tinaTask.tools && !tinaTask.tools.tool)
        ) {
          taskData.type = 'task_completed'
          //console.log('推断类型为task_completed');
        }
      }
    }

    // 从顶层获取内容（如果有）
    taskData.content = tinaTask.content || ''

    //console.log('解析后的tina_task ID:', taskData.id);
    //console.log('解析后的tina_task type:', taskData.type);
    //console.log('解析后的tina_task content:', taskData.content ? taskData.content.substring(0, 50) + '...' : 'none');

    // 处理plan类型
    if ((taskData.type === 'plan' || tinaTask.plan) && tinaTask.plan) {
      //console.log('检测到plan节点，尝试解析步骤');

      // 确保类型为plan
      taskData.type = 'plan'

      if (tinaTask.plan.step) {
        if (Array.isArray(tinaTask.plan.step)) {
          //console.log('计划步骤数量:', tinaTask.plan.step.length);
          taskData.steps = tinaTask.plan.step.map((step: any) => {
            const stepData = {
              index: parseInt(step.index, 10) || 0,
              title: step.title || '',
              tools_description: step.tools_description || '',
              tools: step.tools || '',
              content: step['#text'] || '',
            }
            //console.log(`计划步骤 ${stepData.index}:`, stepData.title);
            return stepData
          })
        } else {
          // 处理单个步骤的情况（不是数组）
          //console.log('单个计划步骤');
          const step = tinaTask.plan.step
          const stepData = {
            index: parseInt(step.index, 10) || 0,
            title: step.title || '',
            tools_description: step.tools_description || '',
            tools: step.tools || '',
            content: step['#text'] || '',
          }
          taskData.steps = [stepData]
        }
      }
      // 解析 quick_suggest/user_maybe_say 标签
      let quickSuggestRaw = ''
      if (tinaTask.quick_suggest) {
        quickSuggestRaw = tinaTask.quick_suggest
      } else if (tinaTask.user_maybe_say) {
        quickSuggestRaw = tinaTask.user_maybe_say
      }
      if (typeof quickSuggestRaw === 'string' && quickSuggestRaw.trim()) {
        // 按行分割，去除空行和首尾空白
        taskData.user_maybe_say = quickSuggestRaw
          .split(/\r?\n/)
          .map((line) => line.trim())
          .filter((line) => line.length > 0)
        //console.log('解析 user_maybe_say:', taskData.user_maybe_say);
        // 如果 user_maybe_say 不为空，则在前面加入  ‘GO!’
        if (taskData.user_maybe_say.length > 0) {
          taskData.user_maybe_say = ['GO!'].concat(taskData.user_maybe_say)
        }
      }
    }
    // 解析 tools_call 类型
    else if (
      (taskData.type === 'tools_call' ||
        (tinaTask.tools && tinaTask.tools.tool)) &&
      tinaTask.tools
    ) {
      //console.log('检测到tools节点，尝试解析工具调用');
      //console.log('tools节点内容:', JSON.stringify(tinaTask.tools));

      // 确保类型为tools_call
      taskData.type = 'tools_call'

      // 解析步骤信息 - 支持单个步骤和步骤数组
      let stepNumber = 0
      if (tinaTask.tools.step) {
        if (Array.isArray(tinaTask.tools.step)) {
          // 如果step是数组，取第一个元素作为当前步骤
          //console.log('步骤是数组，长度:', tinaTask.tools.step.length);
          const firstStep = tinaTask.tools.step[0]
          // 处理不同格式的步骤值
          if (typeof firstStep === 'object') {
            stepNumber = parseInt(firstStep.index || firstStep, 10)
          } else if (typeof firstStep === 'string') {
            stepNumber = parseInt(firstStep, 10)
          } else if (typeof firstStep === 'number') {
            stepNumber = firstStep
          }
          //console.log('从数组解析步骤号:', stepNumber, '原始值:', JSON.stringify(firstStep));
        } else {
          // 如果step是单个元素，直接解析
          const rawStep = tinaTask.tools.step
          if (typeof rawStep === 'number') {
            stepNumber = rawStep
          } else if (typeof rawStep === 'string') {
            stepNumber = parseInt(rawStep, 10)
          } else if (typeof rawStep === 'object') {
            stepNumber = parseInt(rawStep.index || '0', 10)
          }
          //console.log('解析单个步骤号:', stepNumber, '原始值:', JSON.stringify(rawStep));
        }
      }

      // 确保步骤号是有效的数字，默认为1
      if (isNaN(stepNumber) || stepNumber <= 0) {
        //console.warn('步骤号无效，使用默认值 1');
        stepNumber = 1
      }

      //console.log('最终解析的步骤号:', stepNumber);

      // 解析工具信息 - 支持单个工具和工具数组
      let toolsArray: any[] = []
      if (tinaTask.tools.tool) {
        if (Array.isArray(tinaTask.tools.tool)) {
          toolsArray = tinaTask.tools.tool
          //console.log(`工具调用是数组，包含 ${toolsArray.length} 个工具`);
        } else {
          toolsArray = [tinaTask.tools.tool]
          //console.log('单个工具调用');
        }
      }

      // 使用第一个工具作为主要工具
      if (toolsArray.length > 0) {
        const mainTool = toolsArray[0]
        const toolName = mainTool.name || ''
        let params: Record<string, string> = {}

        // 解析参数
        if (mainTool.parameters && mainTool.parameters.param) {
          const parameters = mainTool.parameters.param
          if (Array.isArray(parameters)) {
            parameters.forEach((param: any) => {
              if (param.name && param['#text'] !== undefined) {
                params[param.name] = param['#text']
              }
            })
          } else if (parameters.name) {
            params[parameters.name] = parameters['#text'] || ''
          }
        }

        taskData.tools_call = {
          step: stepNumber,
          tool_name: toolName,
          parameters: params,
        }

        //console.log(`解析到tools_call, 步骤: ${stepNumber}, 工具: ${toolName}, 参数数量: ${Object.keys(params).length}`);
      } else {
        //console.log('没有找到工具信息');
        taskData.tools_call = {
          step: stepNumber,
          tool_name: '',
          parameters: {},
        }
      }
    }
    // 处理 task_completed 类型
    else if (taskData.type === 'task_completed' || tinaTask.completed) {
      //console.log('检测到task_completed类型');

      // 确保类型为task_completed
      taskData.type = 'task_completed'
      taskData.completed = true

      // 如果有 tools 标签，解析步骤信息
      if (tinaTask.tools && tinaTask.tools.step) {
        let stepNumber = 0
        if (Array.isArray(tinaTask.tools.step)) {
          // 如果是数组，取最后一个作为完成的步骤
          const lastStep = tinaTask.tools.step[tinaTask.tools.step.length - 1]
          // 处理不同格式的步骤值
          if (typeof lastStep === 'object') {
            stepNumber = parseInt(lastStep.index || lastStep, 10)
          } else if (typeof lastStep === 'string') {
            stepNumber = parseInt(lastStep, 10)
          } else if (typeof lastStep === 'number') {
            stepNumber = lastStep
          }
          //console.log('从数组解析完成步骤号:', stepNumber, '原始值:', JSON.stringify(lastStep));
        } else {
          // 如果step是单个元素，直接解析
          const rawStep = tinaTask.tools.step
          if (typeof rawStep === 'number') {
            stepNumber = rawStep
          } else if (typeof rawStep === 'string') {
            stepNumber = parseInt(rawStep, 10)
          } else if (typeof rawStep === 'object') {
            stepNumber = parseInt(rawStep.index || '0', 10)
          }
          //console.log('解析完成步骤号:', stepNumber, '原始值:', JSON.stringify(rawStep));
        }

        // 确保步骤号是有效的数字，默认为1
        if (isNaN(stepNumber) || stepNumber <= 0) {
          //console.warn('完成步骤号无效，使用默认值 1');
          stepNumber = 1
        }

        //console.log(`任务完成，最终步骤号: ${stepNumber}`);

        // 可以添加到 tools_call 中以保留步骤信息
        taskData.tools_call = {
          step: stepNumber,
          tool_name: '',
          parameters: {},
        }
      }
    }
    // 处理传统的step格式 (向后兼容)
    else if (tinaTask.step) {
      //console.log('使用传统step格式');

      if (Array.isArray(tinaTask.step)) {
        //console.log('步骤数量:', tinaTask.step.length);
        taskData.steps = tinaTask.step.map((step: any) => {
          const stepData = {
            index: parseInt(step.index, 10) || 0,
            title: step.title || '',
            tools_description: step.tools_description || '',
            tools: step.tools || '',
            content: step['#text'] || '',
          }
          //console.log(`步骤 ${stepData.index}:`, stepData.title);
          return stepData
        })
      } else {
        // 处理单个步骤的情况（不是数组）
        //console.log('单个步骤');
        const step = tinaTask.step
        const stepData = {
          index: parseInt(step.index, 10) || 0,
          title: step.title || '',
          tools_description: step.tools_description || '',
          tools: step.tools || '',
          content: step['#text'] || '',
        }
        taskData.steps = [stepData]
      }

      // 如果没有明确类型，默认为plan
      if (!taskData.type) {
        taskData.type = 'plan'
        //console.log('根据步骤信息，默认设置类型为plan');
      }
    }

    // 如果没有设置ID，生成一个随机ID
    if (!taskData.id) {
      taskData.id = `auto-id-${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 9)}`
      //console.log('自动生成ID:', taskData.id);
    }

    // 最终检查 - 如果steps为空且tools_call为空，则可能解析有问题
    if (
      taskData.steps.length === 0 &&
      !taskData.tools_call &&
      !taskData.completed
    ) {
      //console.warn('解析后的数据可能不完整: 步骤为空且没有工具调用');
    }

    // console.log('最终解析结果:', JSON.stringify({
    //   id: taskData.id,
    //   type: taskData.type,
    //   stepsCount: taskData.steps.length,
    //   hasToolsCall: !!taskData.tools_call,
    //   completed: !!taskData.completed
    // }));

    return taskData
  } catch (error) {
    console.error('解析XML失败:', error)
    return null
  }
}
