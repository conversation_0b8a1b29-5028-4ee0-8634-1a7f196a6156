import { useAuthStore } from '@/tina/stores/authStore'
import { processUserMaybeSay } from '@/tina/utils/stream-message-converter'
import { performUnifiedLogout, shouldPerformUnifiedLogout } from './auth'
import { GATEWAY_URL } from './casdoor'
import { fetchPost } from './fetch'

/**
 * 生成用户可能说的建议的 API 响应接口
 */
export interface GenerateUserMaybeSayResponse {
  code: number
  message: string
  data: {
    user_maybe_say: string
    generated: boolean
  }
}

/**
 * 生成用户可能说的建议的请求参数
 */
export interface GenerateUserMaybeSayRequest {
  user_id: string
}

/**
 * 调用 API 生成用户可能说的建议
 * @param userId 用户ID
 * @returns Promise<GenerateUserMaybeSayResponse>
 */
export async function generateUserMaybeSay(
  userId: string,
): Promise<GenerateUserMaybeSayResponse> {
  const url = `${GATEWAY_URL}/emotionmind/api/v1/message/generate-user-maybe-say`

  // 获取认证token
  const auth = useAuthStore.getState().auth
  const token = auth.token

  if (!token) {
    throw new Error('用户未登录，无法获取 user maybe say')
  }

  const requestData: GenerateUserMaybeSayRequest = {
    user_id: userId,
  }

  try {
    const response = await fetchPost<GenerateUserMaybeSayResponse>(
      url,
      requestData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )

    // 检查响应状态
    if (response.code !== 200) {
      const errorMessage = response.message || '获取 user maybe say 失败'

      // 检查是否需要执行统一登出
      if (
        shouldPerformUnifiedLogout(response.code, { message: errorMessage })
      ) {
        console.log(
          '🚪 [generateUserMaybeSay] 检测到需要登出的错误:',
          response.code,
        )
        // 异步执行登出，不阻塞当前错误抛出
        performUnifiedLogout(
          `User Maybe Say API ${response.code}: ${errorMessage}`,
        ).catch((logoutError) => {
          console.error(
            '🚪 [generateUserMaybeSay] 统一登出执行失败:',
            logoutError,
          )
        })
      }

      throw new Error(errorMessage)
    }

    return response
  } catch (error) {
    console.error('🚨 [generateUserMaybeSay] API 调用失败:', error)

    // 检查网络错误或其他错误是否需要登出
    if (shouldPerformUnifiedLogout(undefined, error)) {
      console.log('🚪 [generateUserMaybeSay] 检测到需要登出的网络错误')
      // 异步执行登出，不阻塞当前错误抛出
      performUnifiedLogout(
        `User Maybe Say Network Error: ${error.message}`,
      ).catch((logoutError) => {
        console.error(
          '🚪 [generateUserMaybeSay] 统一登出执行失败:',
          logoutError,
        )
      })
    }

    throw error
  }
}

/**
 * 解析 user_maybe_say 内容为建议数组
 * @param userMaybeSayContent user_maybe_say XML 内容
 * @returns 建议数组
 */
export function parseUserMaybeSayContent(
  userMaybeSayContent: string,
): string[] {
  // 复用现有的 processUserMaybeSay 函数进行解析
  const conversationItem = processUserMaybeSay(userMaybeSayContent, 'temp-id')

  if (!conversationItem || !conversationItem.suggestions) {
    console.warn(
      '🚨 [parseUserMaybeSayContent] 无法解析 user_maybe_say XML 内容:',
      userMaybeSayContent,
    )
    return []
  }

  return conversationItem.suggestions
}
