import { chatServiceManager } from '@/tina/services/chat-service-manager'
import { useAuthStore } from '@/tina/stores/authStore'
import { AuthUser } from '@/tina/stores/authStore.ts'
import {
  casdoorGetCode,
  casdoorExchangeToken,
  casdoorUserInfo,
  casdoorSignup,
  casdoorGetUser,
} from './casdoor'

/**
 * 通用登录流程：通过用户名和密码获取 token 和用户信息
 * @param username 用户名
 * @param password 密码
 * @returns { token, userInfo }
 * @throws Error
 */
export async function loginWithUsernameAndPassword(
  username: string,
  password: string,
) {
  // 1. 获取 code
  const codeResp = await casdoorGetCode({ username, password })
  const code = codeResp.data
  if (!code) throw new Error(codeResp.msg || '登录失败，未获取到 code')

  // 2. code 换 token
  const tokenResp = await casdoorExchangeToken(code)
  if (!tokenResp.token) throw new Error('登录失败，未获取到 token')
  const token = tokenResp.token

  // 3. 获取用户信息
  const userInfo = await casdoorUserInfo(token)
  const userId = userInfo.sub
  const user = (await casdoorGetUser(token, userId)).data as AuthUser
  return { token, userId, user }
}

/**
 * 统一登出逻辑
 * 当遇到以下情况时调用：
 * - Token为空或无效
 * - 用户未登录状态
 * - HTTP响应状态码为400（Bad Request）
 * - HTTP响应状态码为429（Too Many Requests）
 * - HTTP响应状态码为401（Unauthorized）
 * - HTTP响应状态码为403（Forbidden）
 */
export async function performUnifiedLogout(reason?: string) {
  console.log(
    '🚪 [UnifiedLogout] 开始统一登出流程',
    reason ? `原因: ${reason}` : '',
  )

  try {
    // 1. 断开聊天服务连接
    console.log('🚪 [UnifiedLogout] 断开聊天服务连接')
    await chatServiceManager.disconnect()

    // 2. 重置聊天服务管理器
    console.log('🚪 [UnifiedLogout] 重置聊天服务管理器')
    await chatServiceManager.reset()

    // 3. 清除认证状态
    console.log('🚪 [UnifiedLogout] 清除认证状态')
    const auth = useAuthStore.getState().auth
    auth.reset()

    // 4. 等待一小段时间确保操作完成
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 5. 跳转到登录页面
    console.log('🚪 [UnifiedLogout] 跳转到登录页面')
    window.location.href = '/new_mvp/'
  } catch (error) {
    console.error('🚪 [UnifiedLogout] 登出过程中发生错误:', error)

    // 即使出错也要确保认证状态被清除
    try {
      const auth = useAuthStore.getState().auth
      auth.reset()
    } catch (resetError) {
      console.error('🚪 [UnifiedLogout] 重置认证状态失败:', resetError)
    }

    // 强制跳转到登录页面
    await new Promise((resolve) => setTimeout(resolve, 500))
    window.location.href = '/new_mvp/'
  }
}

/**
 * 检查是否需要执行统一登出
 * @param statusCode HTTP状态码
 * @param error 错误对象
 * @returns 是否需要登出
 */
export function shouldPerformUnifiedLogout(
  statusCode?: number,
  error?: any,
): boolean {
  // 检查HTTP状态码
  if (statusCode) {
    const logoutStatusCodes = [400, 401, 403, 429]
    if (logoutStatusCodes.includes(statusCode)) {
      return true
    }
  }

  // 检查错误消息中是否包含认证相关关键词
  if (error) {
    const errorMessage = error.message || error.toString() || ''
    const authErrorKeywords = [
      'unauthorized',
      'token',
      'authentication',
      'forbidden',
      'invalid',
      '未登录',
      '认证失败',
      '令牌',
      '权限',
    ]

    const hasAuthError = authErrorKeywords.some((keyword) =>
      errorMessage.toLowerCase().includes(keyword.toLowerCase()),
    )

    if (hasAuthError) {
      return true
    }
  }

  // 检查当前认证状态
  try {
    const auth = useAuthStore.getState().auth
    if (!auth.isLoggedIn()) {
      return true
    }
  } catch (storeError) {
    console.error('🚪 [UnifiedLogout] 检查认证状态失败:', storeError)
    return true
  }

  return false
}

/**
 * 注册并自动登录
 */
export async function signupAndLogin(username: string, password: string) {
  //clear auth

  // 1. 注册
  const res = await casdoorSignup({ username, password })
  if (res.status !== 'ok') throw new Error(res.msg || '注册失败')

  // 2. 复用登录流程
  return await loginWithUsernameAndPassword(username, password)
}
