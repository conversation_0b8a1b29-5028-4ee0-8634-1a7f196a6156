/**
 * Android 原生 FunASR SDK
 * 在 Capacitor Android 环境下调用原生录音接口
 */

interface AndroidFunASRConfig {
  serverUrl?: string
  sampleRate?: number
  bitRate?: number
  enableITN?: boolean
  hotwords?: string
  onResult?: (data: any) => void
  onError?: (error: string) => void
  onStatusChange?: (status: string) => void
  onAudioData?: (audioData: number[]) => void // Android版本传递音频级别数组
}

// 扩展 Window 接口，添加 Android 注入的方法
declare global {
  interface Window {
    AndroidFunASR?: {
      connect: (serverUrl: string, callbackName: string) => void
      startRecording: (hotwords: string, callbackName: string) => void
      stopRecording: (callbackName: string) => void
      disconnect: (callbackName: string) => void
      getStatus: (callbackName: string) => void
    }
    androidFunASRResultCallback?: (data: any) => void
    androidFunASRErrorCallback?: (error: string) => void
    androidFunASRStatusCallback?: (status: string) => void
    androidFunASRAudioDataCallback?: (audioLevels: number[]) => void
  }
}

export class AndroidFunASRSDK {
  private config: Required<AndroidFunASRConfig>
  private callbackCounter: number = 0
  private isInitialized: boolean = false

  constructor(config: AndroidFunASRConfig = {}) {
    this.config = {
      serverUrl: config.serverUrl || 'ws://tina-test.bfbdata.com:9999/api/image-gen/ws/asr',
      sampleRate: 16000,
      bitRate: 16,
      enableITN: false,
      hotwords: config.hotwords || '',
      onResult: config.onResult || function() {},
      onError: config.onError || function() {},
      onStatusChange: config.onStatusChange || function() {},
      onAudioData: config.onAudioData || function() {},
    }
    
    this.initializeCallbacks()
  }

  // 检查Android原生支持
  static isSupported(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).Capacitor && 
           window.AndroidFunASR !== undefined
  }

  // 初始化回调函数
  private initializeCallbacks(): void {
    if (this.isInitialized) return
    
    // 设置全局回调函数
    window.androidFunASRResultCallback = (data: any) => {
      try {
        // 确保data是对象格式
        const result = typeof data === 'string' ? JSON.parse(data) : data
        this.config.onResult(result)
      } catch (error) {
        console.error('解析识别结果失败:', error)
        this.config.onError('解析识别结果失败: ' + (error as Error).message)
      }
    }

    window.androidFunASRErrorCallback = (error: string) => {
      this.config.onError(error)
    }

    window.androidFunASRStatusCallback = (status: string) => {
      this.config.onStatusChange(status)
    }

    window.androidFunASRAudioDataCallback = (audioLevels: number[]) => {
      this.config.onAudioData(audioLevels)
    }
    
    this.isInitialized = true
  }

  // 创建回调函数
  private createCallback<T>(resolve: (value: T) => void, reject: (error: Error) => void): string {
    this.callbackCounter++
    const callbackName = `androidFunASRCallback_${this.callbackCounter}`
    
    // 在 window 上注册回调函数
    const windowAny = window as any
    windowAny[callbackName] = (result: T) => {
      // 清理回调函数
      delete windowAny[callbackName]
      resolve(result)
    }
    
    // 设置超时清理
    setTimeout(() => {
      if (windowAny[callbackName]) {
        delete windowAny[callbackName]
        reject(new Error('操作超时'))
      }
    }, 15000) // 15秒超时
    
    return callbackName
  }

  // 检查麦克风权限（只检查，不申请）
  async checkMicrophonePermission(): Promise<boolean> {
    // 使用现有的权限管理
    try {
      const { checkMicrophonePermission } = await import('../../utils/permissions')
      return await checkMicrophonePermission()
    } catch (error) {
      console.log('权限检查失败: ' + (error as Error).message)
      return false
    }
  }

  // 请求麦克风权限
  async requestMicrophonePermission(): Promise<boolean> {
    // 使用现有的权限管理
    try {
      const { ensureMicrophonePermission } = await import('../../utils/permissions')
      return await ensureMicrophonePermission()
    } catch (error) {
      this.config.onError('权限检查失败: ' + (error as Error).message)
      return false
    }
  }

  // 连接到服务器
  async connect(): Promise<boolean> {
    if (!AndroidFunASRSDK.isSupported()) {
      this.config.onError('当前环境不支持Android原生FunASR')
      return false
    }

    return new Promise((resolve, reject) => {
      const callbackName = this.createCallback<{success: boolean, message: string}>(
        (result) => {
          if (result.success) {
            resolve(true)
          } else {
            this.config.onError('连接失败: ' + result.message)
            resolve(false)
          }
        },
        reject
      )
      
      window.AndroidFunASR!.connect(this.config.serverUrl, callbackName)
    })
  }

  // 开始录音
  async startRecording(): Promise<boolean> {
    if (!AndroidFunASRSDK.isSupported()) {
      this.config.onError('当前环境不支持Android原生FunASR')
      return false
    }

    // 1. 检查麦克风权限
    const hasPermission = await this.requestMicrophonePermission()
    if (!hasPermission) {
      return false
    }

    // 2. 连接服务器
    const connected = await this.connect()
    if (!connected) {
      return false
    }

    // 3. 开始录音
    return new Promise((resolve, reject) => {
      const callbackName = this.createCallback<{success: boolean, message: string}>(
        (result) => {
          if (result.success) {
            resolve(true)
          } else {
            this.config.onError('开始录音失败: ' + result.message)
            resolve(false)
          }
        },
        reject
      )
      
      window.AndroidFunASR!.startRecording(this.config.hotwords, callbackName)
    })
  }

  // 停止录音
  async stopRecording(): Promise<boolean> {
    if (!AndroidFunASRSDK.isSupported()) {
      this.config.onError('当前环境不支持Android原生FunASR')
      return false
    }

    return new Promise((resolve, reject) => {
      const callbackName = this.createCallback<{success: boolean, message: string}>(
        (result) => {
          if (result.success) {
            resolve(true)
          } else {
            this.config.onError('停止录音失败: ' + result.message)
            resolve(false)
          }
        },
        reject
      )
      
      window.AndroidFunASR!.stopRecording(callbackName)
    })
  }

  // 断开连接
  disconnect(): void {
    if (!AndroidFunASRSDK.isSupported()) {
      return
    }

    const callbackName = this.createCallback<{success: boolean, message: string}>(
      (result) => {
        if (!result.success) {
          console.error('断开连接失败:', result.message)
        }
      },
      (error) => {
        console.error('断开连接出错:', error)
      }
    )
    
    window.AndroidFunASR!.disconnect(callbackName)
  }

  // 获取状态
  getStatus(): Promise<{ isRecording: boolean; isConnected: boolean; hasPermission: boolean }> {
    if (!AndroidFunASRSDK.isSupported()) {
      return Promise.resolve({
        isRecording: false,
        isConnected: false,
        hasPermission: false
      })
    }

    return new Promise((resolve, reject) => {
      const callbackName = this.createCallback<{ isRecording: boolean; isConnected: boolean; hasPermission: boolean }>(
        resolve,
        reject
      )
      
      window.AndroidFunASR!.getStatus(callbackName)
    })
  }

  // 设置配置
  setConfig(newConfig: Partial<AndroidFunASRConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 重新初始化回调函数
    this.initializeCallbacks()
  }
} 