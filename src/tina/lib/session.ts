import axios from 'axios'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { performUnifiedLogout, shouldPerformUnifiedLogout } from './auth'
import { GATEWAY_URL } from './casdoor'
import { XMLContentParser } from './xml/XMLContentParser'
import type { TinaTaskData } from './xml/xmlParser'

// Session 相关 API 基础配置

export const SESSION_ENDPOINT = GATEWAY_URL + '/rationalmind'

const sessionAxios = axios.create({
  baseURL: SESSION_ENDPOINT,
  timeout: 10000,
})

// 添加响应拦截器处理认证错误
sessionAxios.interceptors.response.use(
  (response) => {
    // 成功响应直接返回
    return response
  },
  (error) => {
    console.error('🚪 [sessionAxios] 请求失败:', error)

    // 检查是否需要执行统一登出
    const status = error.response?.status
    if (shouldPerformUnifiedLogout(status, error)) {
      console.log('🚪 [sessionAxios] 检测到需要登出的错误:', status)
      // 异步执行登出，不阻塞当前错误抛出
      performUnifiedLogout(
        `Session API ${status ? `HTTP ${status}` : 'Error'}: ${error.message}`,
      ).catch((logoutError) => {
        console.error('🚪 [sessionAxios] 统一登出执行失败:', logoutError)
      })
    }

    // 继续抛出原始错误
    return Promise.reject(error)
  },
)

export interface Resp<D = unknown> {
  success: boolean
  data: D
}

export interface SessionInfo {
  content: string
  created_at: string
  session_id: string
  updated_at: string
  // 新增字段用于任务中心
  status?: 'init' | 'plan' | 'tools_call' | 'task_completed' // 任务状态
  step?: number // 当前步骤
  max_step?: number // 最大步骤数
  plan_content: string // 描述任务 tina task, 可能为空
  // 解析后的任务数据
  tinaTask?: TinaTaskData // 从plan_content解析得到的任务数据
}

export interface GetUserSessionsResponse {
  sessions: SessionInfo[]
}

export interface ServerMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp?: number
  xml_content?: string
}

// 获取指定会话内容（支持 SSE，普通请求返回历史）
export function getSessionContentUrl(id: string, since?: string) {
  const base = `${SESSION_ENDPOINT}/api/v1/session/${id}/content`
  const params = new URLSearchParams()
  if (since) params.append('since', since)
  return `${base}?${params.toString()}`
}

// 获取当前用户的所有会话列表
export async function getUserSessions(
  token: string,
  status?: SessionInfo['status'],
) {
  return sessionAxios.get<Resp<GetUserSessionsResponse>>(
    '/api/v1/user/sessions',
    {
      headers: { Authorization: `Bearer ${token}` },
      params: status ? { status } : undefined,
    },
  )
}

// 获取当前用户的执行中任务
export async function getUserSessionsRunning(token: string) {
  return getUserSessions(token, 'tools_call')
}

export function fetchSessionSSE(
  sessionId: string,
  token: string,
  onMessage: (msg: ServerMessage) => void,
) {
  const parser = new XMLContentParser(false)
  const url = getSessionContentUrl(sessionId)
  const controller = new AbortController()

  fetchEventSource(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    signal: controller.signal,
    onmessage: (event) => {
      const raw = event.data.trim()
      if (!raw) return
      try {
        // 尝试解析为 JSON
        const serverMessage = JSON.parse(raw)
        if (serverMessage.content) {
          const parseResult = parser.processContent(serverMessage.content)
          const filteredMessage: ServerMessage = {
            ...serverMessage,
            content: parseResult.textContent,
          }
          if (parseResult.xmlContent) {
            filteredMessage.xml_content = parseResult.xmlContent
          }
          if (parseResult.textContent.length > 0 || parseResult.xmlContent) {
            onMessage(filteredMessage)
          }
        }
      } catch {
        // fallback: 非 JSON 纯文本处理
        const result = parser.processContent(raw)
        if (result.textContent.length > 0 || result.xmlContent) {
          onMessage({
            role: 'assistant',
            content: result.textContent,
            xml_content: result.xmlContent || '',
          })
        }
      }
    },
  })

  return () => controller.abort()
}
