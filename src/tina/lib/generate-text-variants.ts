import { useAuthStore } from '@/tina/stores/authStore'
import { GATEWAY_URL } from './casdoor'

export interface GenerateTextVariantsParams {
  text: string
  onData: (variant: string, index: number) => void
  onError?: (err: any) => void
  onDone?: () => void
}

/**
 * 调用 AI 生成多种文本处理结果（流式）
 * @param params { text, onData, onError, onDone }
 */
export async function generateTextVariants({ text, onData, onError, onDone }: GenerateTextVariantsParams) {
  const auth = useAuthStore.getState().auth
  const token = auth.token
  const userId = auth.userId
  if (!token || !userId) {
    onError?.('用户未登录，无法获取 AI 处理文本')
    return
  }

  const url = `${GATEWAY_URL}/emotionmind/api/v1/message/generate-text`
  const body = {
    cmd: 'tina_ext_agent_fix_tts',
    text,
    stream: true,
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(body),
    })
    if (!response.body) throw new Error('无响应流')
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''
    let aiIndex = 0
    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      buffer += decoder.decode(value, { stream: true })
      // 按行分割
      let lines = buffer.split('\n')
      buffer = lines.pop() || ''
      for (const line of lines) {
        if (!line.trim()) continue
        if (line.startsWith('data:')) {
          try {
            const json = JSON.parse(line.replace(/^data: ?/, ''))
            const content = json.choices?.[0]?.delta?.content
            if (typeof content === 'string' && content.length > 0) {
              onData(content, aiIndex)
              aiIndex++
            }
          } catch (err) {
            // 忽略解析失败
          }
        }
      }
    }
    onDone?.()
  } catch (err) {
    onError?.(err)
  }
} 