export interface HistoryMessage {
  content: string
  id: string
  metadata: MessageMetadata
  role: string
  timestamp: string
  tool_calls: ToolCall[]
  [property: string]: any
}

export interface MessageMetadata {
  name: string
  optimistic_id?: string
  source?: string
  timestamp?: string
  tool_call_id: string
  tool_metadata?: ToolMetadata
  tool_name: string
  tool_parameters: ToolParameters
  tool_result: ToolResult
  tool_success: boolean
  user_maybe_say: string
  [property: string]: any
}

export interface ToolMetadata {
  session_id: string
  timestamp: number
  xml_type: string
  [property: string]: any
}

export interface ToolParameters {
  city?: string
  command: string
  height: number
  keyword?: string
  limit?: number
  limit_per_source?: number
  max_results?: number
  prompt: string
  query?: string
  sources?: string[]
  task_id: string
  width: number
  [property: string]: any
}

export interface ToolResult {
  city?: string
  command: string
  conversation_id?: string
  count?: number
  country?: string
  description?: string
  feels_like?: number
  humidity?: number
  image_url: string
  limit_per_source?: number
  log_id?: string
  message: string
  metadata?: ToolResultMetadata
  platform?: string
  pressure?: number
  provider?: string
  query?: string
  query_time?: string
  results?: Result[]
  sources?: Source[]
  sources_count?: number
  sunrise?: string
  sunset?: string
  task_id: string
  temperature?: number
  total_count?: number
  update_time?: string
  updated_at: string
  uv_index?: number
  visibility?: number
  wind_speed?: number
  [property: string]: any
}

export interface ToolResultMetadata {
  query: string
  time: string
  total_results: number
  [property: string]: any
}

export interface Result {
  author: string
  count: number
  description: string
  id: string
  items: Item[]
  pic: string
  platform: string
  snippet: string
  title: string
  type: string
  url: string
  [property: string]: any
}

export interface Item {
  link: string
  platform: string
  rank: number
  title: string
  [property: string]: any
}

export interface Source {
  content: string
  content_type: string
  type: string
  [property: string]: any
}

export interface ToolCall {
  id: string
  name: string
  parameters: Parameters
  [property: string]: any
}

export interface Parameters {
  city?: string
  command: string
  height: number
  keyword?: string
  limit?: number
  limit_per_source?: number
  max_results?: number
  prompt: string
  query?: string
  sources?: string[]
  task_id: string[] | string
  width: number
  [property: string]: any
}
