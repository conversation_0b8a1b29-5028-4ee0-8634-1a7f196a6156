import { describe, it, expect, vi, beforeEach } from 'vitest'
import { shouldPerformUnifiedLogout, performUnifiedLogout } from '../auth'

// Mock dependencies
const mockAuthReset = vi.fn()
const mockAuth = {
  isLoggedIn: vi.fn(() => true),
  reset: mockAuthReset,
}

vi.mock('@/tina/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      auth: mockAuth,
    })),
  },
}))

vi.mock('@/tina/services/chat-service-manager', () => ({
  chatServiceManager: {
    disconnect: vi.fn(),
    reset: vi.fn(),
  },
}))

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
})

describe('统一登出逻辑测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockAuthReset.mockClear()
    window.location.href = ''
  })

  describe('shouldPerformUnifiedLogout', () => {
    it('应该对HTTP 400状态码返回true', () => {
      expect(shouldPerformUnifiedLogout(400)).toBe(true)
    })

    it('应该对HTTP 401状态码返回true', () => {
      expect(shouldPerformUnifiedLogout(401)).toBe(true)
    })

    it('应该对HTTP 403状态码返回true', () => {
      expect(shouldPerformUnifiedLogout(403)).toBe(true)
    })

    it('应该对HTTP 429状态码返回true', () => {
      expect(shouldPerformUnifiedLogout(429)).toBe(true)
    })

    it('应该对HTTP 200状态码返回false', () => {
      expect(shouldPerformUnifiedLogout(200)).toBe(false)
    })

    it('应该对包含"unauthorized"的错误返回true', () => {
      const error = new Error('Unauthorized access')
      expect(shouldPerformUnifiedLogout(undefined, error)).toBe(true)
    })

    it('应该对包含"token"的错误返回true', () => {
      const error = new Error('Invalid token')
      expect(shouldPerformUnifiedLogout(undefined, error)).toBe(true)
    })

    it('应该对包含"未登录"的错误返回true', () => {
      const error = new Error('用户未登录')
      expect(shouldPerformUnifiedLogout(undefined, error)).toBe(true)
    })

    it('应该对普通错误返回false', () => {
      const error = new Error('Network error')
      expect(shouldPerformUnifiedLogout(undefined, error)).toBe(false)
    })
  })

  describe('performUnifiedLogout', () => {
    it('应该执行完整的登出流程', async () => {
      const { chatServiceManager } = await import(
        '@/tina/services/chat-service-manager'
      )
      const { useAuthStore } = await import('@/tina/stores/authStore')

      await performUnifiedLogout('测试登出')

      // 验证聊天服务管理器被调用
      expect(chatServiceManager.disconnect).toHaveBeenCalled()
      expect(chatServiceManager.reset).toHaveBeenCalled()

      // 验证认证状态被重置
      expect(mockAuthReset).toHaveBeenCalled()

      // 验证页面跳转
      expect(window.location.href).toBe('/new_mvp/')
    })

    it('即使出错也应该清除认证状态并跳转', async () => {
      const { chatServiceManager } = await import(
        '@/tina/services/chat-service-manager'
      )
      const { useAuthStore } = await import('@/tina/stores/authStore')

      // 模拟disconnect失败
      chatServiceManager.disconnect.mockRejectedValue(
        new Error('Disconnect failed'),
      )

      await performUnifiedLogout('测试登出失败')

      // 验证即使disconnect失败，认证状态仍被重置
      expect(mockAuthReset).toHaveBeenCalled()

      // 验证页面仍然跳转
      expect(window.location.href).toBe('/new_mvp/')
    })
  })
})
