import { useAuthStore } from '@/tina/stores/authStore'
import { processUserMaybeSay } from '@/tina/utils/stream-message-converter'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  generateUserMaybeSay,
  parseUserMaybeSayContent,
} from '../user-maybe-say'

// Mock fetch
global.fetch = vi.fn()

// Mock useAuthStore
vi.mock('@/tina/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(),
  },
}))

// Mock processUserMaybeSay function
vi.mock('@/tina/utils/stream-message-converter', () => ({
  processUserMaybeSay: vi.fn(),
}))

describe('user-maybe-say API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('parseUserMaybeSayContent', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('应该正确解析 user_maybe_say XML 内容', () => {
      const xmlContent = `<user_maybe_say>
继续这个话题
还有其他相关的吗？
今天天气怎么样？
</user_maybe_say>`

      // Mock processUserMaybeSay 返回值
      const mockConversationItem = {
        id: 'temp-id',
        type: 'userMaybeSay',
        suggestions: ['继续这个话题', '还有其他相关的吗？', '今天天气怎么样？'],
      }
      vi.mocked(processUserMaybeSay).mockReturnValue(mockConversationItem)

      const result = parseUserMaybeSayContent(xmlContent)

      expect(processUserMaybeSay).toHaveBeenCalledWith(xmlContent, 'temp-id')
      expect(result).toEqual([
        '继续这个话题',
        '还有其他相关的吗？',
        '今天天气怎么样？',
      ])
    })

    it('应该过滤空行', () => {
      const xmlContent = `<user_maybe_say>
继续这个话题

还有其他相关的吗？

今天天气怎么样？

</user_maybe_say>`

      // Mock processUserMaybeSay 返回值
      const mockConversationItem = {
        id: 'temp-id',
        type: 'userMaybeSay',
        suggestions: ['继续这个话题', '还有其他相关的吗？', '今天天气怎么样？'],
      }
      vi.mocked(processUserMaybeSay).mockReturnValue(mockConversationItem)

      const result = parseUserMaybeSayContent(xmlContent)

      expect(result).toEqual([
        '继续这个话题',
        '还有其他相关的吗？',
        '今天天气怎么样？',
      ])
    })

    it('应该处理无效的 XML 内容', () => {
      const invalidContent = '这不是有效的 XML 内容'

      // Mock processUserMaybeSay 返回 null
      vi.mocked(processUserMaybeSay).mockReturnValue(null)

      const result = parseUserMaybeSayContent(invalidContent)
      expect(result).toEqual([])
    })

    it('应该处理空的 user_maybe_say 标签', () => {
      const emptyContent = '<user_maybe_say></user_maybe_say>'

      // Mock processUserMaybeSay 返回空建议
      const mockConversationItem = {
        id: 'temp-id',
        type: 'userMaybeSay',
        suggestions: [],
      }
      vi.mocked(processUserMaybeSay).mockReturnValue(mockConversationItem)

      const result = parseUserMaybeSayContent(emptyContent)
      expect(result).toEqual([])
    })
  })

  describe('generateUserMaybeSay', () => {
    it('应该成功调用 API 并返回结果', async () => {
      // Mock auth store
      const mockAuth = {
        token: 'test-token',
        userId: 'test-user-id',
      }
      vi.mocked(useAuthStore.getState).mockReturnValue({ auth: mockAuth })

      // Mock fetch response
      const mockResponse = {
        code: 200,
        message: 'success',
        data: {
          user_maybe_say:
            '<user_maybe_say>\n继续这个话题\n还有其他相关的吗？\n</user_maybe_say>',
          generated: true,
        },
      }

      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      const result = await generateUserMaybeSay('test-user-id')

      expect(result).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(
          '/emotionmind/api/v1/message/generate-user-maybe-say',
        ),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            Authorization: 'Bearer test-token',
          }),
          body: JSON.stringify({ user_id: 'test-user-id' }),
        }),
      )
    })

    it('应该在用户未登录时抛出错误', async () => {
      // Mock auth store without token
      const mockAuth = {
        token: null,
        userId: null,
      }
      vi.mocked(useAuthStore.getState).mockReturnValue({ auth: mockAuth })

      await expect(generateUserMaybeSay('test-user-id')).rejects.toThrow(
        '用户未登录，无法获取 user maybe say',
      )
    })

    it('应该在 API 返回错误时抛出错误', async () => {
      // Mock auth store
      const mockAuth = {
        token: 'test-token',
        userId: 'test-user-id',
      }
      vi.mocked(useAuthStore.getState).mockReturnValue({ auth: mockAuth })

      // Mock fetch response with error
      const mockResponse = {
        code: 400,
        message: 'Bad Request',
        data: null,
      }

      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      await expect(generateUserMaybeSay('test-user-id')).rejects.toThrow(
        'Bad Request',
      )
    })
  })
})
