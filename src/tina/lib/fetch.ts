import { performUnifiedLogout, shouldPerformUnifiedLogout } from './auth'

// 基础 fetch 函数 - GET请求
export async function fetchGet<T>(
  url: string,
  options: RequestInit = {},
): Promise<T> {
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      ...options.headers,
    },
    credentials: 'omit', // 默认不发送cookie
    ...options,
  })

  return handleResponse<T>(response)
}

// 基础 fetch 函数 - POST请求
export async function fetchPost<T>(
  url: string,
  data: any,
  options: RequestInit = {},
): Promise<T> {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(data),
    credentials: 'omit', // 默认不发送cookie
    ...options,
  })

  return handleResponse<T>(response)
}

// 处理 fetch 响应
export async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const httpError = new HttpError(
      response.status,
      `HTTP错误 ${response.status}: ${response.statusText}`,
    )

    // 检查是否需要执行统一登出
    if (shouldPerformUnifiedLogout(response.status, httpError)) {
      console.log('🚪 [fetch] 检测到需要登出的HTTP错误:', response.status)
      // 异步执行登出，不阻塞当前错误抛出
      performUnifiedLogout(
        `HTTP ${response.status}: ${response.statusText}`,
      ).catch((logoutError) => {
        console.error('🚪 [fetch] 统一登出执行失败:', logoutError)
      })
    }

    throw httpError
  }

  return (await response.json()) as T
}

// HTTP错误处理
export class HttpError extends Error {
  status: number

  constructor(status: number, message: string) {
    super(message)
    this.status = status
    this.name = 'HttpError'
  }
}
