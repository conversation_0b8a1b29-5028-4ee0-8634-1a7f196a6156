/**
 * FunASR SDK - 整合版语音识别客户端
 * 集成了 recorder-core.js, pcm.js, wav.js, wsconnecter.js 的核心功能
 * 提供简化的录音和识别接口
 * 在 Capacitor Android 环境下自动使用原生实现
 */

interface FunASRConfig {
  serverUrl?: string
  sampleRate?: number
  bitRate?: number
  enableITN?: boolean
  hotwords?: string
  onResult?: (data: any) => void
  onError?: (error: string) => void
  onStatusChange?: (status: string) => void
  onAudioData?: (audioData: Float32Array | number[]) => void // 兼容原生和Web版本
}

interface RecorderInstance {
  processor: ScriptProcessorNode
  source: MediaStreamAudioSourceNode
}

export class FunASRSDK {
  private config: Required<FunASRConfig>
  private isRecording: boolean = false
  private isConnected: boolean = false
  private audioContext: AudioContext | null = null
  private mediaStream: MediaStream | null = null
  private websocket: WebSocket | null = null
  private recorder: RecorderInstance | null = null
  private sampleBuffer: Int16Array = new Int16Array()
  
  // Android原生SDK实例
  private androidSDK: any = null
  private initializationPromise: Promise<void> | null = null
  private isInitialized: boolean = false

  constructor(config: FunASRConfig = {}) {
    this.config = {
      serverUrl: config.serverUrl || 'wss://tina-test.bfbdata.com/api/image-gen/ws/asr',
      sampleRate: 16000,
      bitRate: 16,
      enableITN: false,
      hotwords: '',
      onResult: config.onResult || function() {},
      onError: config.onError || function() {},
      onStatusChange: config.onStatusChange || function() {},
      onAudioData: config.onAudioData || function() {},
    }
    
    // 开始异步初始化，但不等待
    this.initializationPromise = this.initializeSDK()
  }

  // 确保SDK已初始化
  public async ensureInitialized(): Promise<void> {
    if (this.isInitialized) {
      return
    }
    
    if (this.initializationPromise) {
      await this.initializationPromise
    }
  }

  // 初始化SDK（自动选择Web或Android原生版本）
  private async initializeSDK(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    if (this.isCapacitorAndroid()) {
      try {
        // 动态导入Android原生SDK
        const { AndroidFunASRSDK } = await import('./android-funasr-sdk')
        
        if (AndroidFunASRSDK.isSupported()) {
          // 创建Android原生SDK实例
          this.androidSDK = new AndroidFunASRSDK({
            serverUrl: this.config.serverUrl,
            hotwords: this.config.hotwords,
            onResult: this.config.onResult,
            onError: this.config.onError,
            onStatusChange: this.config.onStatusChange,
            onAudioData: (audioLevels: number[]) => {
              // 将Android的音频级别数组转换为兼容的格式
              this.config.onAudioData(audioLevels as any)
            }
          })
          console.log('🎤 使用Android原生FunASR SDK')
          this.isInitialized = true
          return
        }
      } catch (error) {
        console.warn('Android原生SDK初始化失败，将使用Web版本:', error)
      }
    }
    
    // 使用Web版本的初始化
    this.initAudioContext()
    console.log('🎤 使用Web版本FunASR SDK')
    this.isInitialized = true
  }

  // 检查是否是Capacitor Android环境
  private isCapacitorAndroid(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).Capacitor && 
           (window as any).Capacitor.getPlatform() === 'android'
  }

  // 初始化音频上下文（Web版本）
  private initAudioContext(): void {
    const AudioContext = window.AudioContext || (window as any).webkitAudioContext
    if (!AudioContext) {
      this.config.onError('浏览器不支持音频录制')
      return
    }
    
    this.audioContext = new AudioContext()
  }

  // 检查浏览器支持
  static isSupported(): boolean {
    // 如果是 Capacitor 应用（Android/iOS），检查原生支持
    if (typeof window !== 'undefined' && (window as any).Capacitor) {
      // 对于Android，检查原生接口是否可用
      if ((window as any).Capacitor.getPlatform() === 'android') {
        return window.AndroidFunASR !== undefined || 
               !!(navigator.mediaDevices && 
                  navigator.mediaDevices.getUserMedia && 
                  window.WebSocket &&
                  (window.AudioContext || (window as any).webkitAudioContext))
      }
      // 对于iOS，暂时返回true
      return true
    }

    // 对于普通 web 环境，进行标准检测
    return !!(navigator.mediaDevices && 
             navigator.mediaDevices.getUserMedia && 
             window.WebSocket &&
             (window.AudioContext || (window as any).webkitAudioContext))
  }

  // 检查麦克风权限（只检查，不申请）
  async checkMicrophonePermission(): Promise<boolean> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    try {
      // 如果有Android原生SDK，使用原生权限处理
      if (this.androidSDK) {
        return await this.androidSDK.checkMicrophonePermission()
      }

      // 在 Capacitor 应用中，使用我们的权限工具处理权限
      if (typeof window !== 'undefined' && (window as any).Capacitor) {
        // 动态导入避免循环依赖
        const { checkMicrophonePermission } = await import('../../utils/permissions')
        const result = await checkMicrophonePermission()
        return result
      }

      // Web 环境下的权限检查
      try {
        if (navigator.permissions && navigator.permissions.query) {
          const result = await navigator.permissions.query({ name: 'microphone' as PermissionName })
          return result.state === 'granted'
        }
        // 如果不支持权限查询，返回false
        return false
      } catch (error) {
        console.log('Web环境权限检查失败:', error)
        return false
      }
    } catch (error) {
      console.log('麦克风权限检查失败: ' + (error as Error).message)
      return false
    }
  }

  // 请求麦克风权限
  async requestMicrophonePermission(): Promise<boolean> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    try {
      // 如果有Android原生SDK，使用原生权限处理
      if (this.androidSDK) {
        return await this.androidSDK.requestMicrophonePermission()
      }

      // 在 Capacitor 应用中，使用我们的权限工具处理权限
      if (typeof window !== 'undefined' && (window as any).Capacitor) {
        // 动态导入避免循环依赖
        const { ensureMicrophonePermission } = await import('../../utils/permissions')
        return await ensureMicrophonePermission()
      }

      // Web 环境下的标准权限获取
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.audioContext!.sampleRate,
          channelCount: 1,
          echoCancellation: false,
          noiseSuppression: false
        }
      })
      return true
    } catch (error) {
      this.config.onError('麦克风权限获取失败: ' + (error as Error).message)
      return false
    }
  }

  // 连接到服务器
  async connect(): Promise<boolean> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    if (this.isConnected) {
      return true
    }

    // 如果有Android原生SDK，使用原生连接
    if (this.androidSDK) {
      const result = await this.androidSDK.connect()
      this.isConnected = result
      return result
    }

    // Web版本的连接逻辑
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.config.serverUrl)
        
        this.websocket.onopen = () => {
          this.isConnected = true
          this.config.onStatusChange('connected')
          resolve(true)
        }
        
        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.config.onResult(data)
          } catch (error) {
            console.log('接收到非JSON消息:', event.data)
          }
        }
        
        this.websocket.onerror = (error) => {
          this.config.onError('WebSocket 连接错误: ' + error)
          reject(error)
        }
        
        this.websocket.onclose = () => {
          this.isConnected = false
          this.config.onStatusChange('disconnected')
        }
        
      } catch (error) {
        this.config.onError('连接失败: ' + (error as Error).message)
        reject(error)
      }
    })
  }

  // 开始录音
  async startRecording(): Promise<boolean> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    if (this.isRecording) {
      return false
    }

    // 如果有Android原生SDK，使用原生录音
    if (this.androidSDK) {
      const result = await this.androidSDK.startRecording()
      this.isRecording = result
      return result
    }

    // Web版本的录音逻辑
    try {
      // 1. 检查权限并获取媒体流
      if (!this.mediaStream) {
        const hasPermission = await this.requestMicrophonePermission()
        if (!hasPermission) {
          return false
        }
        
        // 在 Capacitor 环境下，需要手动获取媒体流
        if (typeof window !== 'undefined' && (window as any).Capacitor && !this.mediaStream) {
          try {
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: this.audioContext!.sampleRate || 44100,
                channelCount: 1,
                echoCancellation: false,
                noiseSuppression: false
              }
            })
          } catch (error) {
            this.config.onError('获取音频流失败: ' + (error as Error).message)
            return false
          }
        }
      }

      // 2. 连接服务器
      if (!this.isConnected) {
        await this.connect()
      }

      // 3. 发送开始命令
      const startCommand = {
        command: 'start',
        hotwords: this.config.hotwords
      }
      this.websocket!.send(JSON.stringify(startCommand))

      // 4. 初始化音频处理
      await this.initAudioProcessing()

      this.isRecording = true
      this.config.onStatusChange('recording')
      
      return true
    } catch (error) {
      this.config.onError('开始录音失败: ' + (error as Error).message)
      return false
    }
  }

  // 初始化音频处理（Web版本）
  private async initAudioProcessing(): Promise<void> {
    if (this.audioContext!.state === 'suspended') {
      await this.audioContext!.resume()
    }

    const source = this.audioContext!.createMediaStreamSource(this.mediaStream!)
    
    // 创建音频处理节点
    const processor = this.audioContext!.createScriptProcessor(4096, 1, 1)
    
    processor.onaudioprocess = (event) => {
      if (!this.isRecording) return
      
      const inputBuffer = event.inputBuffer.getChannelData(0)
      this.processAudioData(inputBuffer)
    }

    source.connect(processor)
    processor.connect(this.audioContext!.destination)
    
    this.recorder = { processor, source }
  }

  // 处理音频数据（Web版本）
  private processAudioData(float32Data: Float32Array): void {
    // 发送音频数据给回调函数用于波形显示
    if (this.config.onAudioData) {
      this.config.onAudioData(float32Data)
    }
    
    // 重采样到 16kHz
    const resampledData = this.resampleAudio(float32Data, this.audioContext!.sampleRate, 16000)
    
    // 转换为 Int16
    const int16Data = this.floatTo16BitPCM(resampledData)
    
    // 合并到缓冲区
    const newBuffer = new Int16Array(this.sampleBuffer.length + int16Data.length)
    newBuffer.set(this.sampleBuffer)
    newBuffer.set(int16Data, this.sampleBuffer.length)
    this.sampleBuffer = newBuffer
    
    // 发送音频数据
    this.sendAudioData()
  }

  // 发送音频数据（Web版本）
  private sendAudioData(): void {
    const chunkSize = 960 // 对应 [5,10,5] 配置
    
    while (this.sampleBuffer.length >= chunkSize) {
      const chunk = this.sampleBuffer.slice(0, chunkSize)
      this.sampleBuffer = this.sampleBuffer.slice(chunkSize)
      
      // 转换为二进制数据
      const buffer = new ArrayBuffer(chunk.length * 2)
      const view = new DataView(buffer)
      for (let i = 0; i < chunk.length; i++) {
        view.setInt16(i * 2, chunk[i], true)
      }
      
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(buffer)
      }
    }
  }

  // 停止录音
  async stopRecording(): Promise<boolean> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    if (!this.isRecording) {
      return false
    }

    // 如果有Android原生SDK，使用原生停止
    if (this.androidSDK) {
      const result = await this.androidSDK.stopRecording()
      this.isRecording = !result
      return result
    }

    // Web版本的停止逻辑
    try {
      this.isRecording = false
      
      // 发送剩余音频数据
      if (this.sampleBuffer.length > 0) {
        const buffer = new ArrayBuffer(this.sampleBuffer.length * 2)
        const view = new DataView(buffer)
        for (let i = 0; i < this.sampleBuffer.length; i++) {
          view.setInt16(i * 2, this.sampleBuffer[i], true)
        }
        this.websocket!.send(buffer)
        this.sampleBuffer = new Int16Array()
      }

      // 发送停止命令
      const stopCommand = {
        command: 'stop'
      }
      this.websocket!.send(JSON.stringify(stopCommand))

      // 清理音频处理
      if (this.recorder) {
        this.recorder.processor.disconnect()
        this.recorder.source.disconnect()
        this.recorder = null
      }

      this.config.onStatusChange('stopped')
      return true
    } catch (error) {
      this.config.onError('停止录音失败: ' + (error as Error).message)
      return false
    }
  }

  // 断开连接
  disconnect(): void {
    if (this.isRecording) {
      this.stopRecording()
    }
    
    // 如果有Android原生SDK，使用原生断开
    if (this.androidSDK) {
      this.androidSDK.disconnect()
      return
    }

    // Web版本的断开逻辑
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop())
      this.mediaStream = null
    }
    
    this.isConnected = false
    this.config.onStatusChange('disconnected')
  }

  // 工具方法：音频重采样（Web版本）
  private resampleAudio(inputData: Float32Array, inputSampleRate: number, outputSampleRate: number): Float32Array {
    if (inputSampleRate === outputSampleRate) {
      return inputData
    }
    
    const ratio = inputSampleRate / outputSampleRate
    const outputLength = Math.floor(inputData.length / ratio)
    const outputData = new Float32Array(outputLength)
    
    for (let i = 0; i < outputLength; i++) {
      const index = i * ratio
      const before = Math.floor(index)
      const after = Math.ceil(index)
      const fraction = index - before
      
      if (after >= inputData.length) {
        outputData[i] = inputData[before]
      } else {
        outputData[i] = inputData[before] * (1 - fraction) + inputData[after] * fraction
      }
    }
    
    return outputData
  }

  // 工具方法：Float32 转 Int16 PCM（Web版本）
  private floatTo16BitPCM(float32Array: Float32Array): Int16Array {
    const int16Array = new Int16Array(float32Array.length)
    for (let i = 0; i < float32Array.length; i++) {
      const s = Math.max(-1, Math.min(1, float32Array[i]))
      int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF
    }
    return int16Array
  }

  // 获取状态
  getStatus(): { isRecording: boolean; isConnected: boolean; hasPermission: boolean } {
    return {
      isRecording: this.isRecording,
      isConnected: this.isConnected,
      hasPermission: !!this.mediaStream || !!this.androidSDK
    }
  }

  // 异步获取状态（支持Android原生）
  async getStatusAsync(): Promise<{ isRecording: boolean; isConnected: boolean; hasPermission: boolean }> {
    // 确保SDK已初始化
    await this.ensureInitialized()

    // 如果有Android原生SDK，使用原生获取状态
    if (this.androidSDK) {
      return await this.androidSDK.getStatus()
    }

    return this.getStatus()
  }

  // 设置配置
  setConfig(newConfig: Partial<FunASRConfig>): void {
    Object.assign(this.config, newConfig)
    
    // 如果有Android原生SDK，同时更新原生SDK配置
    if (this.androidSDK) {
      this.androidSDK.setConfig(newConfig)
    }
  }
} 