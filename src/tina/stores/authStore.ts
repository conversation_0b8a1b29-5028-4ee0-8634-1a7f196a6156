import { create } from 'zustand'

const ACCESS_TOKEN = 'casdoor_token'
const USER_INFO = 'casdoor_user'
const USER_ID = 'casdoor_user_id'

export interface AuthUser {
  id: string
  owner: string
  name: string
  email: string
  displayName: string
  avatar: string
  phone?: string
  language: string
  bio: string
  birthday: string
  homepage: string
  [key: string]: unknown
}

interface AuthState {
  auth: {
    token: string | null
    setToken: (token: string | null) => void
    userId: string | null
    setUserId: (userId: string | null) => void
    user: AuthUser | null
    setUser: (user: AuthUser | null) => void
    reset: () => void
    isLoggedIn: () => boolean
  }
}

export const useAuthStore = create<AuthState>()((set, get) => {
  // 用 localStorage 替换 cookie
  const getLocal = (key: string) => {
    const value = localStorage.getItem(key)
    try {
      return value ? JSON.parse(value) : null
    } catch {
      return null
    }
  }
  const setLocal = (key: string, value: any) => {
    localStorage.setItem(key, JSON.stringify(value))
  }
  const removeLocal = (key: string) => {
    localStorage.removeItem(key)
  }
  const initToken = getLocal(ACCESS_TOKEN)
  const initUser = getLocal(USER_INFO)
  const initUserId = localStorage.getItem(USER_ID) ?? null
  return {
    auth: {
      token: initToken,
      setToken: (token) => {
        if (token) {
          setLocal(ACCESS_TOKEN, token)
        } else {
          removeLocal(ACCESS_TOKEN)
        }
        set((state) => ({ ...state, auth: { ...state.auth, token } }))
      },
      userId: initUserId,
      setUserId: (userId) => {
        if (userId) {
          localStorage.setItem(USER_ID, userId)
        } else {
          localStorage.removeItem(USER_ID)
        }
        set((state) => ({ ...state, auth: { ...state.auth, userId } }))
      },
      user: initUser,
      setUser: (user) => {
        if (user) {
          setLocal(USER_INFO, user)
        } else {
          removeLocal(USER_INFO)
        }
        set((state) => ({ ...state, auth: { ...state.auth, user } }))
      },
      reset: () => {
        removeLocal(ACCESS_TOKEN)
        removeLocal(USER_INFO)
        localStorage.removeItem(USER_ID)
        // todo remove user storage
        set((state) => ({
          ...state,
          auth: { ...state.auth, token: null, user: null, userId: null },
        }))
      },
      isLoggedIn: () => {
        const { token, user } = get().auth
        return !!token && !!user
      },
    },
  }
})

const useAuth = () => useAuthStore((state) => state.auth)
export default useAuth
