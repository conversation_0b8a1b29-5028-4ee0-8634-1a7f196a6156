import {
  EConversationType,
  TConversationItem,
  TConversationRole,
} from '@/stateV2/conversation'
import { HistoryMessage } from '@/tina/lib/types.ts'
import { convertStreamOutputToConversationItem } from '@/tina/utils/stream-message-converter.ts'
import { getToolConfigByPartialMatch } from '@/tina/utils/tool-name-mapping'
import type { Descendant } from 'slate'
import { TextStreamParser } from '@/utils/TextStreamParser.ts'
import { chatServiceManager } from './chat-service-manager'

/**
 * 从历史消息的content中提取<send_time>标签的时间
 * TODO: 如果需要使用历史消息中的发送时间，可以使用此函数
 */
function extractSendTimeFromContent(content: string): string | null {
  const sendTimeMatch = content.match(/<send_time>([^<]+)<\/send_time>/)
  return sendTimeMatch ? sendTimeMatch[1] : null
}

/**
 * 清理历史消息content，移除<send_time>标签
 */
function cleanHistoryMessageContent(content: string): string {
  return content.replace(/<send_time>[^<]*<\/send_time>/g, '').replace(/<voice_message>([^<]*)<\/voice_message>/g, '$1').trim()
}

/**
 * 将服务器历史消息转换为TConversationItem格式
 */
export function convertHistoryMessageToTConversationItem(
  data: HistoryMessage,
  index: number,
): TConversationItem | TConversationItem[] | null {
  // 确定消息角色
  const role: TConversationRole = data.role === 'user' ? 'mine' : 'friend'

  // 创建基础消息对象，确保历史消息ID不与实时消息冲突
  const baseMessage = {
    id: data.id ? `hist-${data.id}` : `history-${data.timestamp}-${index}`,
    role,
    timestamp: data.timestamp,
    upperText: undefined, // 将在加载后统一处理时间显示
  }

  // 处理用户消息
  if (data.role === 'user') {
    // 应该不会触发
    if (data.content?.includes('user_online')) {
      return {
        ...baseMessage,
        type: EConversationType.notification,
        notificationId: data.id || `tool-call-${data.timestamp}`,
        text: `用户上线了`,
        icon: 'fa-solid fa-screwdriver-wrench',
      } as TConversationItem
    }


    // 检查是否是图片卡片格式
    const imageCardRegex = /<card\s+type="image"\s+url="([^"]+)"><\/card>/
    const imageCardMatch = data.content?.match(imageCardRegex)

    console.log('🖼️ [convertHistoryMessage] 检查图片卡片:', {
      content: data.content,
      hasMatch: !!imageCardMatch,
      url: imageCardMatch?.[1],
    })

    if (imageCardMatch) {
      // 处理图片卡片消息
      const imageUrl = imageCardMatch[1]

      console.log('🖼️ [convertHistoryMessage] 创建图片消息:', {
        imageUrl,
        messageId: data.id,
        timestamp: data.timestamp,
      })

      return {
        ...baseMessage,
        type: EConversationType.image,
        imageInfo: imageUrl,
        sendStatus: 'sent' as const,
      } as TConversationItem
    }

    // 清理content中的<send_time>标签
    const cleanContent = cleanHistoryMessageContent(data.content || '')
    const textContent: Descendant[] = [{ text: cleanContent }]

    return {
      ...baseMessage,
      type: EConversationType.text,
      textContent,
    } as TConversationItem
  }

  // 处理助手消息
  if (data.role === 'assistant') {
    if (data.content) {
      // 使用历史消息前缀确保ID不冲突
      const historyId = data.id
        ? `hist-${data.id}`
        : `history-${data.timestamp}-${index}`
      const parser = new TextStreamParser(historyId)
      const outputs = parser.processText(data.content, true)

      // 普通的助手文本消息
      let items: TConversationItem[] = outputs
        .map((output) => convertStreamOutputToConversationItem(output))
        .filter((item) => item)
        .map((item) => {
          return {
            ...item,
            timestamp: data.timestamp,
          }
        })
      const uniqueItems = items.reduce(
        (acc, item) => {
          acc[item.id] = item // 相同 id 会被覆盖，最终保留最后一条
          return acc
        },
        {} as Record<string, TConversationItem>,
      )

      items = Object.values(uniqueItems)
      // 检查metadata中是否有user_maybe_say
      if (data.metadata?.user_maybe_say) {
        // 处理用户可能说的建议
        const suggestions = data.metadata.user_maybe_say
          .split('\n')
          .map((line: string) => line.trim())
          .filter((line: string) => line.length > 0)

        if (suggestions.length > 0) {
          items.push({
            ...baseMessage,
            id: `${baseMessage.id}-user-maybe-say`,
            type: EConversationType.userMaybeSay,
            suggestions,
          })
        }
      }
      if (items.length > 0) {
        return items
      }
    }

    // 检查是否有tool_calls
    if (data.tool_calls && data.tool_calls.length > 0) {
      console.warn('assistant 空消息 tool_calls，忽略:', data)
      return null
    }
  }

  // 处理工具消息
  if (data.role === 'tool') {
    // 特殊处理：task_completed 类型的消息使用文本类型展示
    if (data.metadata?.tool_metadata?.xml_type === 'task_completed') {
      const textContent: Descendant[] = [{ text: data.content || '任务已完成' }]
      return {
        ...baseMessage,
        type: EConversationType.text,
        textContent,
      } as TConversationItem
    }

    // 工具消息通常显示为通知
    const toolName =
      data.metadata?.tool_name || data.metadata?.name || '未知工具'

    // 特殊处理 tina_task_query_status 的结果
    if (toolName.includes('tina_task') && toolName.includes('query')) {
      if (data.metadata?.tool_metadata?.xml_type === 'change_plan') {
        // 如果是修改计划的查询，显示具体的结果内容
        let resultText = data.content || '需要修改当前计划，是否同意？'

        // 如果tool_result是字符串，使用它；如果是对象，尝试提取message字段
        if (data.metadata?.tool_result) {
          if (typeof data.metadata.tool_result === 'string') {
            resultText = data.metadata.tool_result
          } else if (
            typeof data.metadata.tool_result === 'object' &&
            data.metadata.tool_result.message
          ) {
            resultText = data.metadata.tool_result.message
          }
        }

        const textContent: Descendant[] = [{ text: resultText }]
        return {
          ...baseMessage,
          type: EConversationType.text,
          textContent,
        } as TConversationItem
      }
    }

    // 使用统一的工具配置映射
    const toolConfig = getToolConfigByPartialMatch(toolName)
    const notificationText = toolConfig.notificationText
    const icon = toolConfig.icon

    return {
      ...baseMessage,
      type: EConversationType.notification,
      notificationId: data.id || `tool-${data.timestamp}`,
      text: notificationText,
      icon,
    } as TConversationItem
  }

  console.error('消息处理失败:', data)
  return null
}

/**
 * 历史消息服务类
 */
export class HistoryMessageService {
  private isLoading = false
  private hasMoreHistoryFlag = true
  private oldestTimestamp: number | undefined

  /**
   * 加载历史消息
   */
  async loadHistoryMessages(limit: number = 20): Promise<TConversationItem[]> {
    if (this.isLoading) {
      console.log('📜 [HistoryMessageService] 正在加载中，跳过重复请求')
      return []
    }

    if (!this.hasMoreHistoryFlag) {
      console.log('📜 [HistoryMessageService] 没有更多历史消息')
      return []
    }

    this.isLoading = true

    try {
      // 使用传入的时间戳或者已记录的最老时间戳
      const timestamp = this.oldestTimestamp || Date.now()
      console.log('📜 [HistoryMessageService] 开始加载历史消息', {
        timestamp,
      })
      // 调用ChatServiceManager的客户端获取历史消息
      const client = (chatServiceManager as any).client
      if (!client) {
        throw new Error('EmotionMindClient 未初始化')
      }

      // 获取当前用户ID
      const currentUserId = (chatServiceManager as any).currentUserId
      if (!currentUserId) {
        throw new Error('用户未登录')
      }

      const response = await client.queryHistoryByTimestamp({
        user_id: currentUserId,
        timestamp: timestamp,
        limit,
        offset: 0,
      })

      console.log('📜 [HistoryMessageService] 服务器返回历史消息', response)

      if (!response.messages || response.messages.length === 0) {
        console.log('📜 [HistoryMessageService] 没有更多历史消息')
        this.hasMoreHistoryFlag = false
        return []
      }

      // 反转
      response.messages.reverse()

      // 更新最老时间戳
      if (response.messages.length > 0) {
        this.oldestTimestamp = Math.min(
          ...response.messages.map((msg) => this.parseTimestamp(msg.timestamp)),
        )
        console.log(
          '📜 [HistoryMessageService] 最老时间戳更新为',
          this.oldestTimestamp,
          response.messages[0].id,
        )
      }
      // 如果返回的消息数量少于请求数量，说明没有更多消息了
      if (response.messages.length < limit) {
        this.hasMoreHistoryFlag = false
        console.log('📜 [HistoryMessageService] 已加载所有历史消息')
      }

      console.log(
        '📜 [HistoryMessageService] 成功加载历史消息',
        response.messages.length,
      )
      return response.messages
        .map((msg: HistoryMessage, index: number) =>
          convertHistoryMessageToTConversationItem(msg, index),
        )
        .flat()
        .filter((item) => item)
    } catch (error) {
      console.error('📜 [HistoryMessageService] 加载历史消息失败:', error)
      throw error
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 检查是否还有更多历史消息
   */
  hasMoreHistory(): boolean {
    return this.hasMoreHistoryFlag
  }

  /**
   * 检查是否正在加载历史消息
   */
  isLoadingHistory(): boolean {
    return this.isLoading
  }

  /**
   * 重置历史消息状态（用于用户切换等场景）
   */
  reset(): void {
    this.isLoading = false
    this.hasMoreHistoryFlag = true
    this.oldestTimestamp = undefined
    console.log('📜 [HistoryMessageService] 重置历史消息状态')
  }

  private parseTimestamp(timestamp: string): number {
    // 尝试解析ISO字符串或直接转换为数字
    const parsed = Date.parse(timestamp)
    return isNaN(parsed) ? parseInt(timestamp, 10) : parsed
  }
}

// 导出单例实例
export const historyMessageService = new HistoryMessageService()
