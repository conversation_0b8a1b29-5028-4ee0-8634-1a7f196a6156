import { useAuthStore } from '@/tina/stores/authStore'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { chatServiceManager } from './chat-service-manager'

// Mock EmotionMindClient
vi.mock('@/tina/lib/EmotionMindClient.browser', () => {
  const mockConnection = {
    close: vi.fn(),
    isConnected: vi.fn(() => true),
    getReadyState: vi.fn(() => 'connected'),
  }

  const mockClient = {
    setAuthToken: vi.fn(),
    initializeUserAfterLogin: vi.fn(),
    createMessageStream: vi.fn(() => mockConnection),
    sendMessage: vi.fn(),
    getConfig: vi.fn(() => ({ debug: false })),
  }

  return {
    EmotionMindClient: vi.fn(() => mockClient),
    MessageType: { TEXT: 'text' },
  }
})

// Mock useAuthStore
vi.mock('@/tina/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      auth: {
        isLoggedIn: () => true,
        userId: 'test-user-id',
        token: 'test-token',
        user: { name: 'test-user', id: 'test-user-id' },
      },
    })),
  },
}))

// Mock fetch for getUserCity
global.fetch = vi.fn(() => Promise.reject(new Error('Network error'))) as any

// Mock document.cookie
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: '',
})

describe('ChatServiceManager 心跳检测和重连功能', () => {
  let mockCallbacks: any
  let mockStreamCallbacks: any

  beforeEach(() => {
    vi.clearAllMocks()

    // 重置服务状态
    chatServiceManager.reset()

    mockCallbacks = {
      onConnectionStatusChange: vi.fn(),
      onLLMResponse: vi.fn(),
      onUserMessage: vi.fn(),
      onError: vi.fn(),
      onToolMessage: vi.fn(),
      onToolCallback: vi.fn(),
      onTask: vi.fn(),
    }

    // 模拟 createMessageStream 的回调
    const mockClient = (chatServiceManager as any).client
    mockClient.createMessageStream.mockImplementation(
      (userId: string, deviceId: string, callbacks: any) => {
        mockStreamCallbacks = callbacks
        return {
          close: vi.fn(),
          isConnected: vi.fn(() => true),
          getReadyState: vi.fn(() => 'connected'),
        }
      },
    )
  })

  afterEach(() => {
    chatServiceManager.reset()
  })

  describe('心跳时间记录', () => {
    it('应该在连接建立时初始化心跳时间', () => {
      chatServiceManager.initialize(mockCallbacks)

      // 模拟连接建立
      mockStreamCallbacks.onConnected({})

      const heartbeatStatus = chatServiceManager.getHeartbeatStatus()
      expect(heartbeatStatus.lastHeartbeatTime).toBeGreaterThan(0)
      expect(heartbeatStatus.isHeartbeatHealthy).toBe(true)
    })

    it('应该在收到心跳包时更新心跳时间', () => {
      vi.useFakeTimers()

      chatServiceManager.initialize(mockCallbacks)

      // 模拟连接建立
      mockStreamCallbacks.onConnected({})

      const initialStatus = chatServiceManager.getHeartbeatStatus()

      // 等待一小段时间
      vi.advanceTimersByTime(1000)

      // 模拟收到心跳包
      mockStreamCallbacks.onHeartbeat({
        type: 'heartbeat',
        device_id: 'mobile_test',
        timestamp: new Date().toISOString(),
      })

      const updatedStatus = chatServiceManager.getHeartbeatStatus()
      expect(updatedStatus.lastHeartbeatTime).toBeGreaterThan(
        initialStatus.lastHeartbeatTime,
      )
      expect(updatedStatus.isHeartbeatHealthy).toBe(true)

      vi.useRealTimers()
    })
  })

  describe('心跳检测逻辑', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该在心跳超时时触发重连', async () => {
      chatServiceManager.initialize(mockCallbacks)

      // 模拟连接建立
      mockStreamCallbacks.onConnected({})

      // 模拟3分钟后发送消息（心跳超时）
      vi.advanceTimersByTime(3 * 60 * 1000 + 1000) // 3分钟 + 1秒

      // 发送消息应该触发心跳检测和重连
      await chatServiceManager.sendMessage('test message')

      // 验证重连被触发（通过检查 createMessageStream 被调用了两次）
      const mockClient = (chatServiceManager as any).client
      expect(mockClient.createMessageStream).toHaveBeenCalledTimes(2)
    })

    it('应该在心跳正常时不触发重连', async () => {
      chatServiceManager.initialize(mockCallbacks)

      // 模拟连接建立
      mockStreamCallbacks.onConnected({})

      // 模拟收到心跳包
      mockStreamCallbacks.onHeartbeat({
        type: 'heartbeat',
        device_id: 'mobile_test',
        timestamp: new Date().toISOString(),
      })

      // 模拟1分钟后发送消息（心跳正常）
      vi.advanceTimersByTime(1 * 60 * 1000)

      await chatServiceManager.sendMessage('test message')

      // 验证没有触发额外的重连
      const mockClient = (chatServiceManager as any).client
      expect(mockClient.createMessageStream).toHaveBeenCalledTimes(1)
    })
  })

  describe('心跳状态信息', () => {
    it('应该正确返回心跳状态信息', () => {
      // 初始状态
      let status = chatServiceManager.getHeartbeatStatus()
      expect(status.lastHeartbeatTime).toBe(0)
      expect(status.timeSinceLastHeartbeat).toBe(-1)
      expect(status.isHeartbeatHealthy).toBe(true)

      chatServiceManager.initialize(mockCallbacks)
      mockStreamCallbacks.onConnected({})

      // 连接后状态
      status = chatServiceManager.getHeartbeatStatus()
      expect(status.lastHeartbeatTime).toBeGreaterThan(0)
      expect(status.timeSinceLastHeartbeat).toBeGreaterThanOrEqual(0)
      expect(status.isHeartbeatHealthy).toBe(true)
    })
  })

  describe('重置功能', () => {
    it('应该在重置时清除心跳时间', () => {
      chatServiceManager.initialize(mockCallbacks)
      mockStreamCallbacks.onConnected({})

      // 验证心跳时间已设置
      let status = chatServiceManager.getHeartbeatStatus()
      expect(status.lastHeartbeatTime).toBeGreaterThan(0)

      // 重置服务
      chatServiceManager.reset()

      // 验证心跳时间已清除
      status = chatServiceManager.getHeartbeatStatus()
      expect(status.lastHeartbeatTime).toBe(0)
    })
  })
})
