/**
 * 测试账户管理器
 * 用于开发测试环境下的账户管理功能
 */

export interface TestAccount {
  id: string
  username: string
  password: string
  displayName: string
  createdAt: string
}

const TEST_ACCOUNTS_KEY = 'tina_test_accounts'

/**
 * 生成随机用户名
 */
export function generateRandomUsername(): string {
  const prefixes = ['test', 'demo', 'user', 'dev']
  const suffixes = ['001', '002', '003', '004', '005']
  const randomPrefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const randomSuffix = suffixes[Math.floor(Math.random() * suffixes.length)]
  const timestamp = Date.now().toString().slice(-4)
  
  return `${randomPrefix}_${timestamp}_${randomSuffix}`
}

/**
 * 生成随机密码
 */
export function generateRandomPassword(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let password = ''
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

/**
 * 获取所有测试账户
 */
export function getTestAccounts(): TestAccount[] {
  try {
    const accounts = localStorage.getItem(TEST_ACCOUNTS_KEY)
    return accounts ? JSON.parse(accounts) : []
  } catch (error) {
    console.error('获取测试账户失败:', error)
    return []
  }
}

/**
 * 保存测试账户
 */
export function saveTestAccount(account: TestAccount): void {
  try {
    const accounts = getTestAccounts()
    const existingIndex = accounts.findIndex(acc => acc.id === account.id)
    
    if (existingIndex >= 0) {
      accounts[existingIndex] = account
    } else {
      accounts.push(account)
    }
    
    localStorage.setItem(TEST_ACCOUNTS_KEY, JSON.stringify(accounts))
  } catch (error) {
    console.error('保存测试账户失败:', error)
  }
}

/**
 * 删除测试账户
 */
export function deleteTestAccount(accountId: string): void {
  try {
    const accounts = getTestAccounts()
    const filteredAccounts = accounts.filter(acc => acc.id !== accountId)
    localStorage.setItem(TEST_ACCOUNTS_KEY, JSON.stringify(filteredAccounts))
  } catch (error) {
    console.error('删除测试账户失败:', error)
  }
}

/**
 * 创建新的测试账户对象
 */
export function createTestAccount(username: string, password: string): TestAccount {
  return {
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    username,
    password,
    displayName: `测试用户_${username}`,
    createdAt: new Date().toISOString(),
  }
}