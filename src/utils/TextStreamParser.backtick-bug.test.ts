import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

/**
 * 反引号干扰XML标签识别的Bug测试
 *
 * Bug描述：
 * 当文本中包含非voice格式的反引号块（如 ```L4Bxxxxxx```）时，
 * TextStreamParser会错误地将这些反引号作为分割点，
 * 导致后续的XML标签（如<card>、<user_maybe_say>等）无法被正确识别为XML类型，
 * 而是被错误地归类为text类型。
 *
 * 修复方案：
 * 只将 ```voice\n 格式的反引号视为特殊标记，
 * 其他格式的反引号当作普通文本字符处理。
 */
describe('TextStreamParser - 反引号干扰Bug测试', () => {
  describe('核心Bug验证', () => {
    it('问题1: card标签应该被识别为XML而不是text', () => {
      // 简化的问题场景：card标签前有反引号块
      const testText = `微信扫码验证：<card type="image" url="test.jpg"></card>`

      const parser = new TextStreamParser('test-card')
      const outputs = parser.processText(testText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      // 检查是否有XML输出
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(xmlOutputs.length).toBeGreaterThan(0)

      // 找到包含card标签的输出
      const cardOutput = xmlOutputs.find((o) => o.content.includes('<card'))
      expect(cardOutput).toBeDefined()
      expect(cardOutput?.content).toContain('<card type="image"')
      expect(cardOutput?.content).toContain('</card>')
    })

    it('问题2: br标签应该正确拆分消息而不是进入text', () => {
      // 简化的问题场景：br标签拆分
      const testText = `东南亚水果贸易背后的地缘政治隐喻：<br>http://test.com/link`

      const parser = new TextStreamParser('test-br')
      const outputs = parser.processText(testText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      // br标签应该触发新段落，所以应该有多个输出
      expect(allOutputs.length).toBeGreaterThan(1)

      // 检查内容是否被正确分割
      const allContent = allOutputs.map((o) => o.content).join('')
      expect(allContent).toContain('东南亚水果贸易背后的地缘政治隐喻：')
      expect(allContent).toContain('http://test.com/link')

      // br标签本身不应该出现在最终输出中
      expect(allContent).not.toContain('<br>')
    })
  })

  describe('反引号干扰验证', () => {
    it('验证反引号确实是问题根源', () => {
      // 测试1：没有反引号的情况
      const textWithoutBackticks =
        '前面的文本内容。<card type="test">卡片内容</card>后面的文本。'

      const parser1 = new TextStreamParser('no-backticks')
      const outputs1 = parser1.processText(textWithoutBackticks)
      const finalOutputs1 = parser1.finalize()
      const allOutputs1 = [...outputs1, ...finalOutputs1]

      const xmlOutputs1 = allOutputs1.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs1.length).toBeGreaterThan(0) // 应该成功

      // 测试2：Card标签前有反引号块
      const textWithBackticks =
        '前面的文本内容。```L4Bxxxxxx```（第4位B代表2024年）微信扫码验证：<card type="test">卡片内容</card>后面的文本。'

      const parser2 = new TextStreamParser('with-backticks')
      const outputs2 = parser2.processText(textWithBackticks)
      const finalOutputs2 = parser2.finalize()
      const allOutputs2 = [...outputs2, ...finalOutputs2]

      const xmlOutputs2 = allOutputs2.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs2.length).toBeGreaterThan(0) // 修复后应该也成功
    })

    it('精确问题场景测试', () => {
      // 模拟原始问题的精确场景
      const problemScenario =
        '```L4Bxxxxxx```（第4位B代表2024年）\n   - 微信扫码验证：<card type="image" url="test.jpg"></card>'

      const parser = new TextStreamParser('problem-scenario')
      const outputs = parser.processText(problemScenario)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs.length).toBeGreaterThan(0)

      const cardOutput = xmlOutputs.find((o) => o.content.includes('<card'))
      expect(cardOutput).toBeDefined()
    })
  })

  describe('Voice功能不受影响验证', () => {
    it('voice格式的反引号应该仍然正常工作', () => {
      const voiceText = '```voice\n你好啊\n```'
      const parser = new TextStreamParser('voice-test')
      const outputs = parser.processText(voiceText)

      expect(outputs).toHaveLength(1)
      expect(outputs[0].message_id_type).toContain(':voice')
      expect(outputs[0].content).toBe('你好啊```')
    })

    it('voice和XML标签混合使用应该正常', () => {
      const mixedText =
        '前面文本```voice\n语音内容\n```<card type="test">卡片</card>后面文本'
      const parser = new TextStreamParser('mixed-test')
      const outputs = parser.processText(mixedText, true)

      const voiceOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':voice'),
      )
      const xmlOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(voiceOutputs.length).toBeGreaterThan(0)
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })
  })

  describe('边界情况测试', () => {
    it('多个反引号块不应该影响XML识别', () => {
      const multiBackticks =
        '```code1```和```code2```之间的<card>测试</card>内容'
      const parser = new TextStreamParser('multi-backticks')
      const outputs = parser.processText(multiBackticks)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })

    it('反引号和XML标签紧邻不应该有问题', () => {
      const adjacentText = '```code```<card>紧邻的卡片</card>'
      const parser = new TextStreamParser('adjacent-test')
      const outputs = parser.processText(adjacentText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs.length).toBeGreaterThan(0)

      const cardOutput = xmlOutputs.find((o) => o.content.includes('<card'))
      expect(cardOutput).toBeDefined()
    })
  })
})
