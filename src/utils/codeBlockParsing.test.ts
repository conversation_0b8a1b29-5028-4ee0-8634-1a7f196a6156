import { describe, it, expect, beforeEach } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

describe('代码块解析测试 - 简化版', () => {
  let parser: TextStreamParser
  const messageId = 'test-codeblock'

  beforeEach(() => {
    parser = new TextStreamParser(messageId)
  })

  describe('代码块处理简化测试', () => {
    it('验证：普通代码块现在应该被当作文本处理', () => {
      const codeBlockText = `前面的文本
\`\`\`javascript
console.log("Hello");
\`\`\`
后面的文本`

      const outputs = parser.processText(codeBlockText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('代码块简化测试 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 50)}..."`,
        )
      })

      // 验证：所有内容都应该被当作普通文本处理
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const voiceOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':voice'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(voiceOutputs.length).toBe(0) // 不应该有voice输出
      expect(xmlOutputs.length).toBe(0) // 不应该有XML输出
      expect(textOutputs.length).toBeGreaterThan(0) // 应该有文本输出

      // 验证最终内容包含完整的代码块
      const finalContent = allOutputs[allOutputs.length - 1].content
      expect(finalContent).toContain('前面的文本')
      expect(finalContent).toContain('```javascript')
      expect(finalContent).toContain('console.log("Hello");')
      expect(finalContent).toContain('```')
      expect(finalContent).toContain('后面的文本')

      console.log('✅ 普通代码块被正确当作文本处理')
    })

    it('验证：voice块仍然被正确处理', () => {
      const voiceText = `前面的文本
\`\`\`voice
这是语音内容
\`\`\`
后面的文本`

      const outputs = parser.processText(voiceText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('Voice块测试 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 50)}..."`,
        )
      })

      // 验证：应该有voice输出
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const voiceOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':voice'),
      )

      expect(voiceOutputs.length).toBeGreaterThan(0) // 应该有voice输出
      expect(textOutputs.length).toBeGreaterThan(0) // 也应该有文本输出

      // 验证voice内容
      const voiceContent = voiceOutputs[0].content
      expect(voiceContent).toContain('这是语音内容')

      console.log('✅ Voice块被正确处理')
    })

    it('验证：列表格式问题已修复', () => {
      const listWithCodeBlock = `1. 第一项
2. 包含代码的项：
   \`\`\`text
   代码内容
   \`\`\`
3. 第三项`

      const outputs = parser.processText(listWithCodeBlock)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('列表格式修复测试 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 80)}..."`,
        )
      })

      const finalContent = allOutputs[allOutputs.length - 1].content
      console.log('最终内容:', JSON.stringify(finalContent))

      // 验证：第三项应该正确显示，不应该连接到代码块
      expect(finalContent).toContain('1. 第一项')
      expect(finalContent).toContain('2. 包含代码的项：')
      expect(finalContent).toContain('```text')
      expect(finalContent).toContain('代码内容')
      expect(finalContent).toContain('```')
      expect(finalContent).toContain('3. 第三项')

      // 关键验证：确保没有 "```3. 第三项" 这样的错误连接
      expect(finalContent).not.toContain('```3. 第三项')

      console.log('✅ 列表格式问题已修复')
    })

    it('验证：复杂实际文本处理', () => {
      const problemText = `根据搜索结果，目前未能找到《雅各布之书》的官方授权试读节选。不过我可以为您：

1. **提供替代方案**：
   - 通过波兰国家图书馆查询
   - 亚马逊Kindle样章服务

2. **描述开篇场景**（基于公开书评）：
   \`\`\`text
   "雅各布在里昂修道院抄写经文时，羊皮纸上的字母开始游动。
   这是1699年冬天，异端审判的火光正在欧洲各地闪烁..."
   （来源：《纽约书评》2024年3月号）
   \`\`\`

3. **建议合法获取途径**：
   - 联系上海外文书店
   - 高校图书馆文献传递服务

需要我尝试其他语种资源的节选查询吗？`

      const outputs = parser.processText(problemText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('复杂文本修复验证 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 80)}..."`,
        )
      })

      // 检查段落分割
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const segmentIds = [
        ...new Set(textOutputs.map((o) => o.message_id_type.split(':')[0])),
      ]

      console.log('复杂文本修复验证 - 文本段落ID数量:', segmentIds.length)

      // 验证：整个文本应该在一个段落中（除非有其他分割标记如<br>）
      expect(segmentIds.length).toBe(1)

      // 验证最终内容完整性
      const finalContent = allOutputs[allOutputs.length - 1].content
      expect(finalContent).toContain('1. **提供替代方案**：')
      expect(finalContent).toContain('2. **描述开篇场景**')
      expect(finalContent).toContain('3. **建议合法获取途径**：')
      expect(finalContent).toContain('雅各布在里昂修道院')
      expect(finalContent).toContain('需要我尝试其他语种资源')

      console.log('✅ 复杂文本处理正确 - 修复成功')
    })
  })

  describe('修复验证测试 - 验证代码块解析修复效果', () => {
    it('修复验证: 代码块不应该分割段落', () => {
      const testText = `第一段内容
\`\`\`text
代码块内容
\`\`\`
第二段内容`

      const outputs = parser.processText(testText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('修复验证 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 50)}..."`,
        )
      })

      // 检查段落ID - 所有输出应该属于同一个段落
      const segmentIds = [
        ...new Set(allOutputs.map((o) => o.message_id_type.split(':')[0])),
      ]
      console.log('修复验证 - 段落ID数量:', segmentIds.length)
      console.log('修复验证 - 段落IDs:', segmentIds)

      // 关键验证：所有输出应该属于同一个段落（segment-0）
      expect(segmentIds.length).toBe(1)
      expect(segmentIds[0]).toBe('test-codeblock-0')

      // 验证最终内容完整性
      const finalContent = allOutputs[allOutputs.length - 1].content
      expect(finalContent).toContain('第一段内容')
      expect(finalContent).toContain('```text')
      expect(finalContent).toContain('代码块内容')
      expect(finalContent).toContain('```')
      expect(finalContent).toContain('第二段内容')

      console.log('✅ 代码块不分割段落 - 修复成功')
    })

    it('修复验证: 列表结构保持完整', () => {
      const listText = `1. 第一项
2. 包含代码的项：
   \`\`\`text
   代码内容
   \`\`\`
3. 第三项`

      const outputs = parser.processText(listText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('列表修复验证 - 输出结构:')
      allOutputs.forEach((output, index) => {
        console.log(
          `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 50)}..."`,
        )
      })

      // 检查段落ID - 所有输出应该属于同一个段落
      const segmentIds = [
        ...new Set(allOutputs.map((o) => o.message_id_type.split(':')[0])),
      ]
      console.log('列表修复验证 - 段落ID数量:', segmentIds.length)

      // 关键验证：列表不应该被分割成多个段落
      expect(segmentIds.length).toBe(1)

      // 验证最终内容包含完整的列表结构
      const finalContent = allOutputs[allOutputs.length - 1].content
      expect(finalContent).toContain('1. 第一项')
      expect(finalContent).toContain('2. 包含代码的项：')
      expect(finalContent).toContain('```text')
      expect(finalContent).toContain('代码内容')
      expect(finalContent).toContain('3. 第三项')

      console.log('✅ 列表结构保持完整 - 修复成功')
    })
  })
})
