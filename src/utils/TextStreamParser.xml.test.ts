import { describe, expect, it } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

/**
 * XML标签处理专门测试
 *
 * 测试TextStreamParser对各种XML标签的识别和处理能力，
 * 包括已知标签、未知标签、边界情况等。
 */
describe('TextStreamParser - XML标签处理', () => {
  describe('已知XML标签识别', () => {
    it('应该正确识别所有已知的XML标签类型', () => {
      const knownTags = [
        '<user_maybe_say>用户可能说的话</user_maybe_say>',
        '<card type="voice">语音卡片</card>',
        '<tina_task>任务标签</tina_task>',
        '<tina_analyze>分析标签</tina_analyze>',
        '<ina_analyze rank="8">INA分析</ina_analyze>',
        '<tina_memory>记忆标签</tina_memory>',
        '<ina_memory>INA记忆</ina_memory>',
        '<analyze>通用分析</analyze>',
      ]

      knownTags.forEach((tag, index) => {
        const parser = new TextStreamParser(`test-known-${index}`)
        const outputs = parser.processText(tag)
        const xmlOutputs = outputs.filter((o) =>
          o.message_id_type.includes(':xml'),
        )

        expect(xmlOutputs.length).toBeGreaterThan(0)
        expect(xmlOutputs[0].content).toBe(tag)
      })
    })

    it('应该正确处理带属性的XML标签', () => {
      const tagWithAttrs =
        '<card type="image" url="test.jpg" title="测试图片">卡片内容</card>'
      const parser = new TextStreamParser('test-attrs')
      const outputs = parser.processText(tagWithAttrs)

      const xmlOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      expect(xmlOutputs.length).toBe(1)
      expect(xmlOutputs[0].content).toBe(tagWithAttrs)
    })
  })

  describe('未知XML标签处理', () => {
    it('应该将未知标签当作普通文本处理', () => {
      const unknownTags = [
        '<unknown_tag>这不应该被识别为XML</unknown_tag>',
        '<random>随机标签</random>',
        '<中文标签>中文内容</中文标签>',
        '<123invalid>数字开头的标签</123invalid>',
      ]

      unknownTags.forEach((tag, index) => {
        const parser = new TextStreamParser(`test-unknown-${index}`)
        const outputs = parser.processText(tag, true)
        const finalOutputs = parser.finalize()
        const allOutputs = [...outputs, ...finalOutputs]

        const xmlOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':xml'),
        )
        const textOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':text'),
        )

        // 未知标签应该被当作文本处理
        expect(xmlOutputs.length).toBe(0)
        expect(textOutputs.length).toBeGreaterThan(0)
        expect(textOutputs[textOutputs.length - 1].content).toBe(tag)
      })
    })

    it('应该正确处理中文角括号内容', () => {
      const chineseTexts = [
        '<注意>这是中文标签，不应该被识别为XML</注意>',
        '<提示：请仔细阅读>这也不是XML标签</提示：请仔细阅读>',
        '<x + y = z>',
        '<小于号和>大于号',
      ]

      chineseTexts.forEach((text, index) => {
        const parser = new TextStreamParser(`test-chinese-${index}`)
        const outputs = parser.processText(text)
        const finalOutputs = parser.finalize()
        const allOutputs = [...outputs, ...finalOutputs]

        const xmlOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':xml'),
        )
        const textOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':text'),
        )

        // 中文角括号内容应该被当作普通文本
        expect(xmlOutputs.length).toBe(0)
        expect(textOutputs.length).toBeGreaterThan(0)
        expect(textOutputs[0].content).toBe(text)
      })
    })
  })

  describe('复杂XML场景', () => {
    it('应该正确处理混合内容', () => {
      const mixedContent =
        '前面文本\n<user_maybe_say>建议</user_maybe_say>\n后面文本'
      const parser = new TextStreamParser('test-mixed')
      const outputs = parser.processText(mixedContent)

      expect(outputs.length).toBeGreaterThan(0)

      const textOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(textOutputs.length).toBeGreaterThan(0)
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })

    it('应该正确处理嵌套标签', () => {
      const nestedTags =
        '<user_maybe_say><card>嵌套内容</card></user_maybe_say>'
      const parser = new TextStreamParser('test-nested')
      const outputs = parser.processText(nestedTags)

      expect(outputs).toHaveLength(1)
      expect(outputs[0].message_id_type).toContain(':xml')
      expect(outputs[0].content).toBe(nestedTags)
    })

    it('应该正确处理不完整的标签', () => {
      const incompleteTag = '<user_maybe_say>内容'
      const parser = new TextStreamParser('test-incomplete')
      const outputs = parser.processText(incompleteTag)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      expect(allOutputs.length).toBeGreaterThan(0)
      const allContent = allOutputs.map((o) => o.content).join('')
      expect(allContent).toContain(incompleteTag)
    })
  })

  describe('流式输入XML处理', () => {
    it('应该正确处理分块输入的XML', () => {
      const chunks = ['<user_maybe_say>', '测试内容', '</user_maybe_say>']
      const parser = new TextStreamParser('test-chunks')
      const allOutputs: any[] = []

      chunks.forEach((chunk) => {
        const outputs = parser.processText(chunk)
        allOutputs.push(...outputs)
      })

      expect(allOutputs).toHaveLength(1)
      expect(allOutputs[0].message_id_type).toContain(':xml')
      expect(allOutputs[0].content).toBe(
        '<user_maybe_say>测试内容</user_maybe_say>',
      )
    })

    it('应该正确处理流式输入的复杂场景', () => {
      const chunks = [
        '前面',
        '<user_maybe_say>',
        '内容',
        '</user_maybe_say>',
        '后面',
      ]
      const parser = new TextStreamParser('test-stream')
      const allOutputs: any[] = []

      chunks.forEach((chunk) => {
        const outputs = parser.processText(chunk)
        allOutputs.push(...outputs)
      })

      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(textOutputs.length).toBeGreaterThan(0)
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })
  })

  describe('复杂实际场景测试', () => {
    it('应该正确处理包含ina_analyze标签的复杂内容', () => {
      const complexContent = `（突然变身健身教练形态）💪

**📌 STRONG计划强化版**
1. **物理层面**
   \`\`\`markdown
   - [x] 黑豹乐队《无地自容》变速版×5组深蹲（今早已完成）
   - [ ] 18:00 痛仰《西湖》混音版核心训练
   \`\`\`

<card type="image" url="https://tina-test.bfbdata.com/api/image-gen/show_image/变速训练示意图&1"></card>

2. **精神燃料**
   > "你今天的汗水是明天在红馆开演唱会的门票"
   → 这句话已设为你的运动手环弹幕

3. **香港特别加成**
   推荐明早6点去太平山晨跑，歌单已加入：
   - 太极乐队《红色跑车》山道混音版

<user_maybe_say></user_maybe_say>

<ina_analyze rank="9">
用户需要即时反馈+视觉化进度追踪
</ina_analyze>`

      const parser = new TextStreamParser('test-complex-ina')
      const outputs = parser.processText(complexContent, true)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      // 分析输出结果
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      console.log(
        '所有输出:',
        allOutputs.map((o) => ({
          type: o.message_id_type,
          content:
            o.content.substring(0, 100) + (o.content.length > 100 ? '...' : ''),
        })),
      )

      // 检查是否有ina_analyze标签被正确识别为XML
      const inaAnalyzeXml = xmlOutputs.find((o) =>
        o.content.includes('<ina_analyze'),
      )

      // 检查是否有ina_analyze标签被错误地放入text中
      const inaAnalyzeInText = textOutputs.find((o) =>
        o.content.includes('<ina_analyze'),
      )

      console.log('ina_analyze在XML中:', !!inaAnalyzeXml)
      console.log('ina_analyze在Text中:', !!inaAnalyzeInText)

      if (inaAnalyzeXml) {
        console.log('XML中的ina_analyze内容:', inaAnalyzeXml.content)
      }

      if (inaAnalyzeInText) {
        console.log('Text中的ina_analyze内容:', inaAnalyzeInText.content)
      }

      // ina_analyze应该被识别为XML标签，而不是text
      expect(inaAnalyzeXml).toBeDefined()
      expect(inaAnalyzeInText).toBeUndefined()
    })

    it('应该正确处理流式输入的ina_analyze标签', () => {
      // 模拟流式输入，ina_analyze标签可能被分割
      const chunks = [
        '前面的文本内容\n\n',
        '<ina_analyze rank="9">',
        '\n用户需要即时反馈+视觉化进度追踪\n',
        '</ina_analyze>',
      ]

      const parser = new TextStreamParser('test-stream-ina')
      const allOutputs: any[] = []

      chunks.forEach((chunk, index) => {
        console.log(`处理第${index + 1}块:`, JSON.stringify(chunk))
        const outputs = parser.processText(chunk)
        console.log(
          `第${index + 1}块输出:`,
          outputs.map((o) => ({
            type: o.message_id_type,
            content: o.content,
          })),
        )
        allOutputs.push(...outputs)
      })

      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      console.log('最终文本输出:', textOutputs)
      console.log('最终XML输出:', xmlOutputs)

      // 应该有一个完整的ina_analyze XML输出
      const inaAnalyzeXml = xmlOutputs.find(
        (o) =>
          o.content.includes('<ina_analyze') &&
          o.content.includes('</ina_analyze>'),
      )

      expect(inaAnalyzeXml).toBeDefined()
      expect(inaAnalyzeXml?.content).toBe(
        '<ina_analyze rank="9">\n用户需要即时反馈+视觉化进度追踪\n</ina_analyze>',
      )
    })

    it('应该正确处理你提供的具体内容', () => {
      // 这是你提供的确切内容
      const exactContent = `（突然变身健身教练形态）💪

**📌 STRONG计划强化版**
1. **物理层面**
   \`\`\`markdown
   - [x] 黑豹乐队《无地自容》变速版×5组深蹲（今早已完成）
   - [ ] 18:00 痛仰《西湖》混音版核心训练
   \`\`\`

<card type="image" url="https://tina-test.bfbdata.com/api/image-gen/show_image/变速训练示意图&1"></card>

2. **精神燃料**
   > "你今天的汗水是明天在红馆开演唱会的门票"
   → 这句话已设为你的运动手环弹幕

3. **香港特别加成**
   推荐明早6点去太平山晨跑，歌单已加入：
   - 太极乐队《红色跑车》山道混音版

<user_maybe_say></user_maybe_say>

<ina_analyze rank="9">
用户需要即时反馈+视觉化进度追踪
</ina_analyze>`

      const parser = new TextStreamParser('test-exact-content')
      const outputs = parser.processText(exactContent, true)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      console.log('=== 你提供的具体内容解析结果 ===')
      allOutputs.forEach((output, index) => {
        console.log(`输出 ${index + 1}:`)
        console.log(`  类型: ${output.message_id_type}`)
        console.log(`  内容: ${JSON.stringify(output.content)}`)
        console.log('---')
      })

      // 检查ina_analyze的处理情况
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )

      const inaAnalyzeXml = xmlOutputs.find((o) =>
        o.content.includes('<ina_analyze'),
      )
      const inaAnalyzeInText = textOutputs.find((o) =>
        o.content.includes('<ina_analyze'),
      )

      console.log('XML输出中的ina_analyze:', inaAnalyzeXml ? '存在' : '不存在')
      console.log(
        'Text输出中的ina_analyze:',
        inaAnalyzeInText ? '存在' : '不存在',
      )

      if (inaAnalyzeXml) {
        console.log('XML中的完整ina_analyze内容:', inaAnalyzeXml.content)
      }
      if (inaAnalyzeInText) {
        console.log('Text中的ina_analyze内容:', inaAnalyzeInText.content)
      }

      // 验证ina_analyze应该在XML中而不是text中
      expect(inaAnalyzeXml).toBeDefined()
      expect(inaAnalyzeInText).toBeUndefined()
    })

    it('测试可能导致ina_analyze被误判为text的边界情况', () => {
      // 测试一些可能导致问题的情况
      const problematicCases = [
        // 情况1: ina_analyze前有特殊字符
        '前面内容<ina_analyze rank="9">内容</ina_analyze>',

        // 情况2: ina_analyze在行尾
        '前面内容\n<ina_analyze rank="9">内容</ina_analyze>',

        // 情况3: ina_analyze有多个属性
        '<ina_analyze rank="9" type="test">内容</ina_analyze>',

        // 情况4: ina_analyze内容包含特殊字符
        '<ina_analyze rank="9">用户需要即时反馈+视觉化进度追踪</ina_analyze>',

        // 情况5: ina_analyze前后有空白字符
        '  <ina_analyze rank="9">  内容  </ina_analyze>  ',
      ]

      problematicCases.forEach((testCase, index) => {
        console.log(`\n=== 测试边界情况 ${index + 1} ===`)
        console.log('输入:', JSON.stringify(testCase))

        const parser = new TextStreamParser(`test-boundary-${index}`)
        const outputs = parser.processText(testCase, true)
        const finalOutputs = parser.finalize()
        const allOutputs = [...outputs, ...finalOutputs]

        const xmlOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':xml'),
        )
        const textOutputs = allOutputs.filter((o) =>
          o.message_id_type.includes(':text'),
        )

        const inaAnalyzeXml = xmlOutputs.find((o) =>
          o.content.includes('<ina_analyze'),
        )
        const inaAnalyzeInText = textOutputs.find((o) =>
          o.content.includes('<ina_analyze'),
        )

        console.log('结果:')
        console.log(
          '  XML输出:',
          xmlOutputs.map((o) => o.content),
        )
        console.log(
          '  Text输出:',
          textOutputs.map((o) => o.content),
        )
        console.log('  ina_analyze在XML中:', !!inaAnalyzeXml)
        console.log('  ina_analyze在Text中:', !!inaAnalyzeInText)

        // 所有情况下，ina_analyze都应该被识别为XML
        expect(inaAnalyzeXml).toBeDefined()
        expect(inaAnalyzeInText).toBeUndefined()
      })
    })
  })
})
