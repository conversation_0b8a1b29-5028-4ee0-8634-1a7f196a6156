// 文件名: TextStreamParser.streaming-card.test.ts

import { describe, it, expect } from 'vitest';
import { TextStreamParser, type StreamOutput } from './TextStreamParser';

describe('TextStreamParser - 流式输入中Card标签的识别', () => {
  it('应该在文本流中正确识别被分割的card标签为xml类型', () => {
    // 从用户提供的真实流数据中提取并简化
    const chunks = [
      '根据最新数据，这些中国短剧在北美特别受欢迎：\n\n🔥 **爆款推荐** 🔥\n1. 《重生之我在美国当总裁》\n   - 首月播放破5000万\n   - 中美合拍豪门商战题材\n  <card ',
      'type="image" url="http://i0.hdslb.com/bfs/archive/b6e1625e21caf9e3338365dcf3b486122ef129a9.jpg">',
      '</card>',
      '\n\n2. 《总统爱上清洁工》\n'
    ];

    const parser = new TextStreamParser('streaming-card-test');
    const allOutputs: StreamOutput[] = [];

    chunks.forEach(chunk => {
      allOutputs.push(...parser.processText(chunk));
    });

    const finalOutputs = parser.finalize();
    allOutputs.push(...finalOutputs);

    // 打印结果进行分析
    console.log('流式 Card 测试 - 输出结构:');
    allOutputs.forEach((output, index) => {
      console.log(
        `  ${index}: ${output.message_id_type} -> "${output.content.substring(0, 100).replace(/\n/g, '\\n')}..."`
      );
    });

    const textOutputs = allOutputs.filter(o => o.message_id_type.includes(':text'));
    const xmlOutputs = allOutputs.filter(o => o.message_id_type.includes(':xml'));

    // 1. 应该有XML输出
    expect(xmlOutputs.length).toBeGreaterThan(0);

    // 2. XML输出应该只有一个，并且是card标签
    expect(xmlOutputs.length).toBe(1);
    const cardXml = xmlOutputs[0];
    expect(cardXml.message_id_type).toContain(':xml');
    expect(cardXml.content).toBe('<card type="image" url="http://i0.hdslb.com/bfs/archive/b6e1625e21caf9e3338365dcf3b486122ef129a9.jpg"></card>');

    // 3. 文本输出应该被正确分割在card标签前后
    const firstTextContent = textOutputs[0].content;
    const lastTextContent = textOutputs[textOutputs.length - 1].content;

    expect(firstTextContent).toContain('中美合拍豪门商战题材');
    expect(lastTextContent).toContain('《总统爱上清洁工》');

    // 4. 验证段落ID (segmentIndex) 的正确性
    const textSegmentId = textOutputs[0].message_id_type.split(':')[0];
    const cardSegmentId = cardXml.message_id_type.split(':')[0];

    // card 标签应该开启一个新的段落，因此其ID应该与之前的文本不同
    expect(cardSegmentId).not.toBe(textSegmentId);
  });
});