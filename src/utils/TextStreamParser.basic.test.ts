import { describe, it, expect, beforeEach } from 'vitest'
import { TextStreamParser, type StreamOutput } from '@/utils/TextStreamParser'

/**
 * TextStreamParser 基础功能测试
 *
 * 测试TextStreamParser的核心功能，包括：
 * - 基础文本处理
 * - XML标签处理
 * - Voice块处理
 * - 流式输入处理
 * - BR标签拆分
 */
describe('TextStreamParser - 基础功能', () => {
  let parser: TextStreamParser
  const messageId = 'test-msg'

  beforeEach(() => {
    parser = new TextStreamParser(messageId)
  })

  describe('基础功能测试', () => {
    it('应该正确处理纯文本内容', () => {
      const text = '### 秋日絮语\\n\\n金风送爽时，我总爱站在窗前看那片法国梧桐。叶片边缘已泛起焦糖色，像被岁月烘焙过的信笺，在风中沙沙作响，仿佛在诵读往事的诗篇。\\n\\n#### 晨露未晞\\n拂晓的校园总铺着一层银霜。物理楼前的银杏树下，总有三两学子捧着热豆浆诵读。记得去年此时，张教授总爱踩着落叶来讲量子力学，他说电子跃迁就像秋叶飘落——看似无序却暗合天道。如今他退休南下，空留满地金黄公式无人解答。\\n\\n#### 午后的光晕\\n图书馆西侧的爬山虎最懂光影魔术。正午阳光穿过绛红藤蔓，在古籍阅览室地板上投下跳动的光斑。管理员李阿姨会在这时取出收藏的《芥子园画谱》，她说这些虫蛀的页脚里，藏着比二维码更生动的艺术基因。\\n\\n#### 暮色四合\\n小吃街的桂花香混着烤红薯的焦甜。修车铺王师傅边给单车补胎边哼评弹，油渍斑斑的收音机里放着《玉蜻蜓》。转角书店亮起暖黄灯光，橱窗里海明威的《流动的盛宴》与当季糖炒栗子摆在一起，构成奇妙的互文。\\n\\n#### 夜雨秋灯\\n晚课结束时的雨总带着琴房的气息。音乐系女孩们裹着驼色大衣跑过林荫道，发梢扬起肖邦夜曲的旋律。生物实验室仍亮着灯，培养皿中的枫叶状菌落，正以肉眼可见的速度由绿转红——像被按了快进键的四季轮回。\\n\\n---\\n\\n\u003ccard type=\\"link\\" title=\\"秋日书单推荐\\" img=\\"https://tina-test.bfbdata.com/api/image-gen/show_image/秋日阅读\u0026\u00261\\" url=\\"https://tina-test.bfbdata.com/html/booklist\\"\u003e\\n《故都的秋》郁达夫\u003cbr\u003e\\n《瓦尔登湖》梭罗\u003cbr\u003e\\n《秋园》杨本芬\u003cbr\u003e\\n\u003c/card\u003e\\n\\n需要调整篇幅或补充细节可以随时告诉我~ 比如添加：\u003cbr\u003e\\n- 校园秋日摄影技巧\u003cbr\u003e\\n- 描写秋雨的经典文学作品摘录\\n\\n\u003cuser_maybe_say\u003e\u003c/user_maybe_say\u003e\\n\\n\u003canalyze rank=\\"8.5\\"\u003e\\n用户对场景化写作接受度高，建议后续可结合热点中的文艺资讯（如正在展出的文物特展）延伸文化内涵。\\n\u003c/analyze\u003e'
      const outputs = parser.processText(text, true)

      expect(outputs).toHaveLength(8)
    })

    it('应该正确处理空字符串', () => {
      const outputs = parser.processText('')
      expect(outputs).toHaveLength(0)
    })

    it('应该在finalize时输出剩余的文本内容', () => {
      const outputs = parser.processText('Hello')
      expect(outputs).toHaveLength(1)
      expect(outputs[0].content).toBe('Hello')

      const finalOutputs = parser.finalize()
      expect(finalOutputs).toHaveLength(0)
    })
  })

  describe('XML 处理测试', () => {
    it('应该正确处理完整的XML标签', () => {
      const xmlText = '<user_maybe_say>测试内容</user_maybe_say>'
      const outputs = parser.processText(xmlText)

      expect(outputs).toHaveLength(1)
      expect(outputs[0].message_id_type).toContain(':xml')
      expect(outputs[0].content).toBe(xmlText)
    })

    it('应该正确处理分块输入的XML', () => {
      const chunks = ['<user_maybe_say>', '测试内容', '</user_maybe_say>']
      const allOutputs: StreamOutput[] = []

      chunks.forEach((chunk) => {
        const outputs = parser.processText(chunk)
        allOutputs.push(...outputs)
      })

      expect(allOutputs).toHaveLength(1)
      expect(allOutputs[0].message_id_type).toContain(':xml')
      expect(allOutputs[0].content).toBe(
        '<user_maybe_say>测试内容</user_maybe_say>',
      )
    })
  })

  describe('Voice 处理测试', () => {
    it('应该正确处理完整的voice块', () => {
      const voiceText = '```voice\n你好啊\n```'
      const outputs = parser.processText(voiceText)

      expect(outputs).toHaveLength(1)
      expect(outputs[0].message_id_type).toContain(':voice')
      expect(outputs[0].content).toBe('你好啊```')
    })

    it('应该正确处理分块输入的voice', () => {
      const chunks = ['```voice\n', '你好', '啊\n```']
      const allOutputs: StreamOutput[] = []

      chunks.forEach((chunk) => {
        const outputs = parser.processText(chunk)
        allOutputs.push(...outputs)
      })

      expect(allOutputs.length).toBeGreaterThan(0)
      const voiceOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':voice'),
      )
      expect(voiceOutputs.length).toBeGreaterThan(0)
      expect(voiceOutputs[voiceOutputs.length - 1].content).toBe('你好啊```')
    })
  })

  describe('复杂场景测试', () => {
    it('应该正确处理混合内容', () => {
      const mixedContent =
        '前面文本\n<user_maybe_say>建议</user_maybe_say>\n后面文本'
      const outputs = parser.processText(mixedContent)

      expect(outputs.length).toBeGreaterThan(0)

      const textOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = outputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(textOutputs.length).toBeGreaterThan(0)
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })

    it('应该正确处理BR标签拆分', () => {
      const brText = '前面文本<br>后面文本'
      const outputs = parser.processText(brText)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      // br标签应该触发新段落
      expect(allOutputs.length).toBeGreaterThan(1)

      // br标签本身不应该出现在最终输出中
      const allContent = allOutputs.map((o) => o.content).join('')
      expect(allContent).not.toContain('<br>')
      expect(allContent).toContain('前面文本')
      expect(allContent).toContain('后面文本')
    })

    it('应该正确处理流式输入的复杂场景', () => {
      const chunks = [
        '前面',
        '<user_maybe_say>',
        '内容',
        '</user_maybe_say>',
        '后面',
      ]
      const allOutputs: StreamOutput[] = []

      chunks.forEach((chunk) => {
        const outputs = parser.processText(chunk)
        allOutputs.push(...outputs)
      })

      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(textOutputs.length).toBeGreaterThan(0)
      expect(xmlOutputs.length).toBeGreaterThan(0)
    })
  })

  describe('边界情况测试', () => {
    it('应该正确处理不完整的标签', () => {
      const incompleteTag = '<user_maybe_say>内容'
      const outputs = parser.processText(incompleteTag)
      const finalOutputs = parser.finalize()
      const allOutputs = [...outputs, ...finalOutputs]

      expect(allOutputs.length).toBeGreaterThan(0)
      const allContent = allOutputs.map((o) => o.content).join('')
      expect(allContent).toContain(incompleteTag)
    })

    it('应该正确处理嵌套标签', () => {
      const nestedTags =
        '<user_maybe_say><card>嵌套内容</card></user_maybe_say>'
      const outputs = parser.processText(nestedTags)

      expect(outputs).toHaveLength(1)
      expect(outputs[0].message_id_type).toContain(':xml')
      expect(outputs[0].content).toBe(nestedTags)
    })
  })
})
