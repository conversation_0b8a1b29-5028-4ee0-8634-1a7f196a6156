type AudioPlayerCallback = {
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onError?: (error: any) => void
  onLoadStart?: () => void
  onLoadEnd?: () => void
}

type AudioPlayerInstance = {
  id: string
  audio: HTMLAudioElement
  callbacks: AudioPlayerCallback
  eventListeners: { [key: string]: EventListener }
}

class GlobalAudioPlayer {
  private static instance: GlobalAudioPlayer
  private currentPlayer: AudioPlayerInstance | null = null
  private isLoading = false

  private constructor() {}

  static getInstance(): GlobalAudioPlayer {
    if (!GlobalAudioPlayer.instance) {
      GlobalAudioPlayer.instance = new GlobalAudioPlayer()
    }
    return GlobalAudioPlayer.instance
  }

  // 播放音频
  async play(
    id: string,
    url: string,
    callbacks: AudioPlayerCallback = {},
  ): Promise<void> {
    // 如果是同一个音频且正在播放，则暂停
    if (
      this.currentPlayer &&
      this.currentPlayer.id === id &&
      !this.currentPlayer.audio.paused
    ) {
      this.pause()
      return
    }

    // 如果是同一个音频且已暂停，则继续播放
    if (
      this.currentPlayer &&
      this.currentPlayer.id === id &&
      this.currentPlayer.audio.paused
    ) {
      try {
        await this.currentPlayer.audio.play()
        callbacks.onPlay?.()
      } catch (error) {
        // 忽略AbortError，这通常是由于快速切换音频造成的
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('音频播放失败:', error)
          callbacks.onError?.(error)
        }
      }
      return
    }

    // 如果有其他音频正在播放，先停止它
    if (this.currentPlayer && this.currentPlayer.id !== id) {
      this.stop()
    }

    // 创建新的音频播放实例
    this.createNewPlayer(id, url, callbacks)
  }

  private createNewPlayer(
    id: string,
    url: string,
    callbacks: AudioPlayerCallback,
  ) {
    const audio = document.createElement('audio')
    audio.src = url
    audio.preload = 'auto'

    // 创建事件监听器对象
    const eventListeners: { [key: string]: EventListener } = {}

    const playerInstance: AudioPlayerInstance = {
      id,
      audio,
      callbacks,
      eventListeners,
    }

    // 设置事件监听器
    eventListeners.loadstart = () => {
      this.isLoading = true
      callbacks.onLoadStart?.()
    }

    eventListeners.canplay = () => {
      this.isLoading = false
      callbacks.onLoadEnd?.()
    }

    eventListeners.play = () => {
      callbacks.onPlay?.()
    }

    eventListeners.pause = () => {
      callbacks.onPause?.()
    }

    eventListeners.ended = () => {
      callbacks.onEnded?.()
      this.cleanup()
    }

    eventListeners.error = (error) => {
      console.error('音频加载/播放错误:', error)
      callbacks.onError?.(error)
      this.isLoading = false
      this.cleanup()
    }

    // 添加事件监听器
    Object.entries(eventListeners).forEach(([event, listener]) => {
      audio.addEventListener(event, listener)
    })

    // 设置为当前播放器
    this.currentPlayer = playerInstance

    // 开始播放
    audio.play().catch((error) => {
      // 忽略AbortError，这通常是由于快速切换音频造成的
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('音频播放失败:', error)
        callbacks.onError?.(error)
        this.cleanup()
      }
    })
  }

  // 暂停当前播放
  pause(): void {
    if (this.currentPlayer && !this.currentPlayer.audio.paused) {
      this.currentPlayer.audio.pause()
    }
  }

  // 停止当前播放
  stop(): void {
    if (this.currentPlayer) {
      this.currentPlayer.audio.pause()
      this.currentPlayer.callbacks.onPause?.()
      this.cleanup()
    }
  }

  // 清理当前播放器
  private cleanup(): void {
    if (this.currentPlayer) {
      const audio = this.currentPlayer.audio
      const eventListeners = this.currentPlayer.eventListeners

      // 移除所有事件监听器
      Object.entries(eventListeners).forEach(([event, listener]) => {
        audio.removeEventListener(event, listener)
      })

      // 清理音频资源
      audio.src = ''
      audio.load()

      this.currentPlayer = null
    }
    this.isLoading = false
  }

  // 检查指定ID的音频是否正在播放
  isPlaying(id: string): boolean {
    return this.currentPlayer?.id === id && !this.currentPlayer.audio.paused
  }

  // 检查指定ID的音频是否正在加载
  isLoadingAudio(id: string): boolean {
    return this.currentPlayer?.id === id && this.isLoading
  }

  // 获取当前播放的音频ID
  getCurrentPlayingId(): string | null {
    return this.currentPlayer?.id || null
  }
}

// 导出单例实例
export const globalAudioPlayer = GlobalAudioPlayer.getInstance()
