/**
 * 页面导航工具函数
 * 根据当前路径智能判断跳转地址
 */

/**
 * 智能页面跳转 - 根据当前路径决定跳转地址
 * @param targetPage 目标页面名称 (如: 'home', '', 'onboarding')
 * @param forceRefresh 是否强制刷新页面，默认为 true
 */
export function navigateToPage(targetPage: string = '', forceRefresh: boolean = true) {
  const currentPath = window.location.pathname
  const isNewMvpPath = currentPath.includes('new_mvp')
  
  // 构建目标路径
  let targetPath: string
  if (targetPage === '') {
    // 跳转到根页面
    targetPath = isNewMvpPath ? '/new_mvp/' : '/'
  } else {
    // 跳转到指定页面
    targetPath = isNewMvpPath ? `/new_mvp/${targetPage}` : `/${targetPage}`
  }
  
  console.log('🔧 [Navigation] 当前路径:', currentPath, '跳转到:', targetPath)
  
  if (forceRefresh) {
    // 强制刷新页面
    window.location.href = targetPath
  } else {
    // 不刷新页面，使用 history API
    window.history.pushState(null, '', targetPath)
  }
}

/**
 * 跳转到主页
 * @param forceRefresh 是否强制刷新页面，默认为 true
 */
export function navigateToHome(forceRefresh: boolean = true) {
  navigateToPage('home', forceRefresh)
}

/**
 * 跳转到登录页
 * @param forceRefresh 是否强制刷新页面，默认为 true
 */
export function navigateToLogin(forceRefresh: boolean = true) {
  navigateToPage('', forceRefresh)
}

/**
 * 跳转到引导页
 * @param forceRefresh 是否强制刷新页面，默认为 false（引导页通常不需要强制刷新）
 */
export function navigateToOnboarding(forceRefresh: boolean = false) {
  navigateToPage('onboarding', forceRefresh)
}

/**
 * 获取智能路径 - 仅返回路径字符串，不执行跳转
 * @param targetPage 目标页面名称
 * @returns 智能判断后的完整路径
 */
export function getSmartPath(targetPage: string = ''): string {
  const currentPath = window.location.pathname
  const isNewMvpPath = currentPath.includes('new_mvp')
  
  if (targetPage === '') {
    return isNewMvpPath ? '/new_mvp/' : '/'
  } else {
    return isNewMvpPath ? `/new_mvp/${targetPage}` : `/${targetPage}`
  }
}