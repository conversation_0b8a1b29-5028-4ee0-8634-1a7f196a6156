/**
 * Android 权限管理 - 直接调用注入的原生方法
 * 通过 WebView 的 JavascriptInterface 实现
 */

// 扩展 Window 接口，添加 Android 注入的方法
declare global {
  interface Window {
    AndroidPermissions?: {
      checkMicrophonePermission: (callbackName: string) => void
      requestMicrophonePermission: (callbackName: string) => void
      openAppSettings: (callbackName: string) => void
    }
  }
}

export interface PermissionResult {
  hasPermission?: boolean
  granted?: boolean
  permission: string
  message?: string
  doNotAskAgain?: boolean
  success?: boolean
}

class AndroidPermissionManager {
  private callbackCounter: number

  constructor() {
    this.callbackCounter = 0
  }

  /**
   * 检查是否运行在 Android 环境且有权限接口
   */
  private isAndroidAvailable(): boolean {
    return typeof window !== 'undefined' && 
           window.AndroidPermissions !== undefined
  }

  /**
   * 创建回调函数并生成唯一回调名
   */
  private createCallback<T>(resolve: (value: T) => void, reject: (error: Error) => void): string {
    this.callbackCounter = this.callbackCounter + 1
    const callbackName = `androidPermissionCallback_${this.callbackCounter}`
    
    // 在 window 上注册回调函数
    const windowAny = window as any
    windowAny[callbackName] = (result: T) => {
      // 清理回调函数
      delete windowAny[callbackName]
      resolve(result)
    }
    
    // 设置超时清理
    setTimeout(() => {
      if (windowAny[callbackName]) {
        delete windowAny[callbackName]
        reject(new Error('权限请求超时'))
      }
    }, 10000) // 10秒超时
    
    return callbackName
  }

  /**
   * 检查麦克风权限（只检查，不申请）
   */
  async checkMicrophonePermission(): Promise<PermissionResult> {
    if (!this.isAndroidAvailable()) {
      // 非 Android 环境，返回默认成功
      return { hasPermission: true, permission: 'microphone' }
    }

    return new Promise<PermissionResult>((resolve, reject) => {
      const callbackName = this.createCallback(resolve, reject)
      window.AndroidPermissions!.checkMicrophonePermission(callbackName)
    })
  }

  /**
   * 请求麦克风权限
   */
  async requestMicrophonePermission(): Promise<PermissionResult> {
    if (!this.isAndroidAvailable()) {
      // 非 Android 环境，尝试标准浏览器权限请求
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
        return { granted: true, permission: 'microphone', message: '权限已授权' }
      } catch (error) {
        return { 
          granted: false, 
          permission: 'microphone', 
          message: '权限被拒绝: ' + (error as Error).message 
        }
      }
    }

    return new Promise<PermissionResult>((resolve, reject) => {
      const callbackName = this.createCallback(resolve, reject)
      window.AndroidPermissions!.requestMicrophonePermission(callbackName)
    })
  }

  /**
   * 打开应用设置页面
   */
  async openAppSettings(): Promise<PermissionResult> {
    if (!this.isAndroidAvailable()) {
      // 非 Android 环境，无法打开设置
      return { success: false, permission: '', message: '当前环境不支持打开设置页面' }
    }

    return new Promise<PermissionResult>((resolve, reject) => {
      const callbackName = this.createCallback(resolve, reject)
      window.AndroidPermissions!.openAppSettings(callbackName)
    })
  }

  /**
   * 确保麦克风权限（检查+可选择性请求+处理拒绝）
   * @param autoRequest 是否自动申请权限，默认为 true
   */
  async ensureMicrophonePermission(autoRequest: boolean = true): Promise<boolean> {
    try {
      // 先检查权限
      const checkResult = await this.checkMicrophonePermission()
      if (checkResult.hasPermission) {
        return true
      }

      // 如果不自动申请权限，直接返回 false
      if (!autoRequest) {
        return false
      }

      // 请求权限
      const requestResult = await this.requestMicrophonePermission()
      
      if (!requestResult.granted) {
        console.log('权限被拒绝:', requestResult.message)
        
        // 如果被永久拒绝，可以选择是否提示用户前往设置
        if (requestResult.doNotAskAgain) {
          const shouldGoToSettings = confirm('麦克风权限已被永久拒绝，是否前往设置页面手动开启？')
          if (shouldGoToSettings) {
            await this.openAppSettings()
          }
        }
        
        return false
      }

      return true
    } catch (error) {
      console.error('确保麦克风权限失败:', error)
      return false
    }
  }
}

// 导出单例
export const androidPermissions = new AndroidPermissionManager()

// 导出便捷方法
export const checkMicrophonePermission = () => 
  androidPermissions.checkMicrophonePermission()

export const requestMicrophonePermission = () => 
  androidPermissions.requestMicrophonePermission()

export const ensureMicrophonePermission = (autoRequest: boolean = true) => 
  androidPermissions.ensureMicrophonePermission(autoRequest)

export const openAppSettings = () => 
  androidPermissions.openAppSettings() 