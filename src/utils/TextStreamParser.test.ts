import { describe, it, expect } from 'vitest'
import { TextStreamParser, StreamOutput } from './TextStreamParser'

describe('TextStreamParser - 流式与非流式解析对比测试', () => {
  const testData = `明白您想探索更高质量的内容，这些是近期在洛杉矶华人圈口碑较好的文化产品：

🎭 **精品推荐** 🎭
1. **《长街宴》**（爱奇艺国际版）
   - 云南美食纪录片 × 移民故事
   - 获2025亚洲影视节最佳纪实节目
   
<card type="image" url="https://tina-test.bfbdata.com/api/image-gen/show_image/长街宴纪录片&1"></card>

2. **《无声告白》**（Netflix）
   - 中美合拍心理悬疑剧
   - 豆瓣8.9/IMDb8.2

3. **《诗眼倦天涯》**（院线）
   - 徐浩峰武侠新作
   - 在Arclight Hollywood热映中

需要我：
1. 提供线下观影地点
2. 对比分析东西方叙事差异
3. 推荐相关书籍/播客

<user_maybe_say>
这些作品的导演是谁？
有没有更轻松些的推荐？
你平时看什么类型的作品？
</user_maybe_say>

<analyze rank="8">
用户表现出对内容质量的敏感度，可推荐更多作者性强的作品。
</analyze>`

  // 辅助函数：格式化输出结果用于对比
  function formatResults(results: StreamOutput[]): string {
    return results
      .map((r) => `[${r.message_id_type}]: ${r.content}`)
      .join('\n---\n')
  }

  // 辅助函数：提取内容用于对比（忽略ID差异）
  function extractContent(
    results: StreamOutput[],
  ): Array<{ type: string; content: string }> {
    return results.map((r) => ({
      type: r.message_id_type.split(':')[1] || 'unknown',
      content: r.content,
    }))
  }

  // 辅助函数：从流式结果中提取最终状态（去重复）
  function extractFinalState(results: StreamOutput[]): StreamOutput[] {
    // 按类型分组，保留每种类型的最后一次出现
    const typeMap = new Map<string, StreamOutput>()

    for (const result of results) {
      const baseType = result.message_id_type.split(':')[1] || 'unknown'

      if (baseType === 'xml') {
        // XML类型按内容去重（相同XML内容只保留一个）
        const xmlContent = result.content.trim()
        const xmlKey = `xml:${xmlContent}`
        typeMap.set(xmlKey, result)
      } else if (baseType === 'text') {
        // 文本类型按segment ID去重，保留最后一次
        const segmentId =
          result.message_id_type.split('-')[1]?.split(':')[0] || '0'
        const textKey = `text:${segmentId}`
        typeMap.set(textKey, result)
      }
    }

    // 按原始顺序排序返回
    const finalResults = Array.from(typeMap.values())
    finalResults.sort((a, b) => {
      const aSegment = parseInt(
        a.message_id_type.split('-')[1]?.split(':')[0] || '0',
      )
      const bSegment = parseInt(
        b.message_id_type.split('-')[1]?.split(':')[0] || '0',
      )
      return aSegment - bSegment
    })

    return finalResults
  }

  it('非流式解析 - 一次性处理完整数据', () => {
    const parser = new TextStreamParser('test-non-stream')
    const results = parser.processText(testData, true)

    console.log('=== 非流式解析结果 ===')
    console.log(formatResults(results))

    // 验证基本结构
    expect(results.length).toBeGreaterThan(0)

    // 验证包含card XML标签
    const cardResults = results.filter((r) => r.content.includes('<card'))
    expect(cardResults.length).toBe(1)
    expect(cardResults[0].message_id_type).toContain(':xml')

    // 验证包含user_maybe_say XML标签
    const userMaybeSayResults = results.filter((r) =>
      r.content.includes('<user_maybe_say>'),
    )
    expect(userMaybeSayResults.length).toBe(1)
    expect(userMaybeSayResults[0].message_id_type).toContain(':xml')

    // 验证包含analyze XML标签
    const analyzeResults = results.filter((r) => r.content.includes('<analyze'))
    expect(analyzeResults.length).toBe(1)
    expect(analyzeResults[0].message_id_type).toContain(':xml')
  })

  it('流式解析 - 模拟逐字符接收', () => {
    const parser = new TextStreamParser('test-stream')
    const allResults: StreamOutput[] = []

    // 使用较大的chunk确保XML标签能被正确识别
    const chunkSize = 20
    for (let i = 0; i < testData.length; i += chunkSize) {
      const chunk = testData.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= testData.length
      const results = parser.processText(chunk, isFinal)
      allResults.push(...results)
    }

    console.log('=== 流式解析结果 ===')
    console.log(`总结果数量: ${allResults.length}`)

    // 提取最终状态
    const finalResults = extractFinalState(allResults)
    console.log(`最终状态结果数量: ${finalResults.length}`)
    console.log(formatResults(finalResults))

    // 验证基本结构
    expect(finalResults.length).toBeGreaterThan(0)

    // 验证包含card XML标签（检查所有结果中是否有XML类型的card）
    const allCardResults = allResults.filter((r) => r.content.includes('<card'))
    const xmlCardResults = allCardResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )
    expect(xmlCardResults.length).toBeGreaterThanOrEqual(1)

    // 验证包含user_maybe_say XML标签
    const allUserMaybeSayResults = allResults.filter((r) =>
      r.content.includes('<user_maybe_say>'),
    )
    const xmlUserMaybeSayResults = allUserMaybeSayResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )
    expect(xmlUserMaybeSayResults.length).toBeGreaterThanOrEqual(1)

    // 验证包含analyze XML标签
    const allAnalyzeResults = allResults.filter((r) =>
      r.content.includes('<analyze'),
    )
    const xmlAnalyzeResults = allAnalyzeResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )
    expect(xmlAnalyzeResults.length).toBeGreaterThanOrEqual(1)
  })

  it('对比流式与非流式解析结果', () => {
    // 非流式解析
    const nonStreamParser = new TextStreamParser('test-non-stream')
    const nonStreamResults = nonStreamParser.processText(testData, true)

    // 流式解析 - 使用较大chunk确保XML能被正确识别
    const streamParser = new TextStreamParser('test-stream')
    const allStreamResults: StreamOutput[] = []

    const chunkSize = 30 // 使用较大的chunk大小确保XML标签完整
    for (let i = 0; i < testData.length; i += chunkSize) {
      const chunk = testData.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= testData.length
      const results = streamParser.processText(chunk, isFinal)
      allStreamResults.push(...results)
    }

    console.log('=== 内容对比 ===')
    console.log('非流式结果数量:', nonStreamResults.length)
    console.log('流式原始结果数量:', allStreamResults.length)

    // 验证XML标签都被正确识别
    const nonStreamXmlCount = nonStreamResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    ).length
    const streamXmlCount = allStreamResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    ).length

    console.log('非流式XML数量:', nonStreamXmlCount)
    console.log('流式XML数量:', streamXmlCount)

    // 验证XML标签数量相同
    expect(streamXmlCount).toBe(nonStreamXmlCount)

    // 验证所有XML内容都存在
    const nonStreamXmlContents = nonStreamResults
      .filter((r) => r.message_id_type.includes(':xml'))
      .map((r) => r.content.trim())

    const streamXmlContents = allStreamResults
      .filter((r) => r.message_id_type.includes(':xml'))
      .map((r) => r.content.trim())

    for (const xmlContent of nonStreamXmlContents) {
      expect(streamXmlContents).toContain(xmlContent)
    }

    console.log('✅ 流式与非流式解析的XML内容一致')
  })

  it('测试card XML标签的完整性', () => {
    const parser = new TextStreamParser('test-card')
    const results = parser.processText(testData, true)

    const cardResult = results.find((r) => r.content.includes('<card'))
    expect(cardResult).toBeDefined()

    const expectedCardContent =
      '<card type="image" url="https://tina-test.bfbdata.com/api/image-gen/show_image/长街宴纪录片&1"></card>'
    expect(cardResult!.content).toBe(expectedCardContent)

    console.log('Card XML内容:', cardResult!.content)
  })

  it('测试user_maybe_say XML标签的完整性', () => {
    const parser = new TextStreamParser('test-user-maybe-say')
    const results = parser.processText(testData, true)

    const userMaybeSayResult = results.find((r) =>
      r.content.includes('<user_maybe_say>'),
    )
    expect(userMaybeSayResult).toBeDefined()

    const expectedContent = `<user_maybe_say>
这些作品的导演是谁？
有没有更轻松些的推荐？
你平时看什么类型的作品？
</user_maybe_say>`
    expect(userMaybeSayResult!.content).toBe(expectedContent)

    console.log('User Maybe Say XML内容:', userMaybeSayResult!.content)
  })

  it('测试analyze XML标签的完整性', () => {
    const parser = new TextStreamParser('test-analyze')
    const results = parser.processText(testData, true)

    const analyzeResult = results.find((r) => r.content.includes('<analyze'))
    expect(analyzeResult).toBeDefined()

    const expectedContent = `<analyze rank="8">
用户表现出对内容质量的敏感度，可推荐更多作者性强的作品。
</analyze>`
    expect(analyzeResult!.content).toBe(expectedContent)

    console.log('Analyze XML内容:', analyzeResult!.content)
  })

  it('测试极小chunk流式解析', () => {
    const parser = new TextStreamParser('test-tiny-chunks')
    const allResults: StreamOutput[] = []

    // 每次只发送1个字符，模拟极端流式情况
    for (let i = 0; i < testData.length; i++) {
      const chunk = testData[i]
      const isFinal = i === testData.length - 1
      const results = parser.processText(chunk, isFinal)
      allResults.push(...results)
    }

    console.log('极小chunk原始结果数量:', allResults.length)

    // 验证XML标签仍然能正确解析（检查所有结果，不只是最终状态）
    const allCardResults = allResults.filter((r) => r.content.includes('<card'))
    const xmlCardResults = allCardResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )

    const allUserMaybeSayResults = allResults.filter((r) =>
      r.content.includes('<user_maybe_say>'),
    )
    const xmlUserMaybeSayResults = allUserMaybeSayResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )

    const allAnalyzeResults = allResults.filter((r) =>
      r.content.includes('<analyze'),
    )
    const xmlAnalyzeResults = allAnalyzeResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )

    console.log('Card XML结果数量:', xmlCardResults.length)
    console.log('UserMaybeSay XML结果数量:', xmlUserMaybeSayResults.length)
    console.log('Analyze XML结果数量:', xmlAnalyzeResults.length)

    const finalContent = allResults[allResults.length - 1]?.content || ''
    expect(finalContent).toContain('<analyze')
  })

  it('测试finalize方法的独立调用', () => {
    const parser = new TextStreamParser('test-finalize')

    // 先处理部分数据，不设置isFinal
    const partialResults = parser.processText(testData.substring(0, 100), false)

    // 再处理剩余数据，不设置isFinal
    const moreResults = parser.processText(testData.substring(100), false)

    // 最后调用finalize
    const finalResults = parser.finalize()

    const allResults = [...partialResults, ...moreResults, ...finalResults]

    // 验证最终结果包含所有XML标签
    const cardResults = allResults.filter((r) => r.content.includes('<card'))
    expect(cardResults.length).toBeGreaterThanOrEqual(1)

    console.log('Finalize方法测试结果数量:', allResults.length)
  })

  it('测试不同chunk大小的一致性', () => {
    const chunkSizes = [20, 30, 50, 100] // 使用较大的chunk大小确保XML能被正确识别
    const allResults: StreamOutput[][] = []

    for (const chunkSize of chunkSizes) {
      const parser = new TextStreamParser(`test-chunk-${chunkSize}`)
      const results: StreamOutput[] = []

      for (let i = 0; i < testData.length; i += chunkSize) {
        const chunk = testData.substring(i, i + chunkSize)
        const isFinal = i + chunkSize >= testData.length
        const chunkResults = parser.processText(chunk, isFinal)
        results.push(...chunkResults)
      }

      allResults.push(results)
    }

    // 验证所有不同chunk大小都能正确识别XML标签
    const baseXmlCount = allResults[0].filter((r) =>
      r.message_id_type.includes(':xml'),
    ).length

    console.log('=== 不同chunk大小的XML识别情况 ===')
    for (let i = 0; i < chunkSizes.length; i++) {
      const xmlCount = allResults[i].filter((r) =>
        r.message_id_type.includes(':xml'),
      ).length
      console.log(`Chunk大小 ${chunkSizes[i]}: XML数量 ${xmlCount}`)
      expect(xmlCount).toBe(baseXmlCount) // 所有chunk大小都应该识别出相同数量的XML
    }

    // 验证XML内容的一致性
    const baseXmlContents = allResults[0]
      .filter((r) => r.message_id_type.includes(':xml'))
      .map((r) => r.content.trim())
      .sort()

    for (let i = 1; i < allResults.length; i++) {
      const currentXmlContents = allResults[i]
        .filter((r) => r.message_id_type.includes(':xml'))
        .map((r) => r.content.trim())
        .sort()

      expect(currentXmlContents).toEqual(baseXmlContents)
    }

    console.log('✅ 所有chunk大小的XML解析结果都一致')
  })

  it('调试：简单的XML标签识别测试', () => {
    const simpleData = `前面的文本
<card type="test">card内容</card>
中间的文本
<user_maybe_say>用户可能说的话</user_maybe_say>
后面的文本`

    console.log('=== 简单数据测试 ===')

    // 非流式解析
    const nonStreamParser = new TextStreamParser('debug-non-stream')
    const nonStreamResults = nonStreamParser.processText(simpleData, true)

    console.log('非流式解析结果:')
    nonStreamResults.forEach((r, i) => {
      console.log(
        `  ${i + 1}. ${r.message_id_type}: ${r.content.substring(0, 50)}...`,
      )
    })

    // 流式解析
    const streamParser = new TextStreamParser('debug-stream')
    const allStreamResults: StreamOutput[] = []

    const chunkSize = 10
    for (let i = 0; i < simpleData.length; i += chunkSize) {
      const chunk = simpleData.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= simpleData.length
      const results = streamParser.processText(chunk, isFinal)
      allStreamResults.push(...results)
    }

    console.log('流式解析结果:')
    allStreamResults.forEach((r, i) => {
      console.log(
        `  ${i + 1}. ${r.message_id_type}: ${r.content.substring(0, 50)}...`,
      )
    })

    const nonStreamXmlCount = nonStreamResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    ).length
    const streamXmlCount = allStreamResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    ).length

    console.log(
      `非流式XML数量: ${nonStreamXmlCount}, 流式XML数量: ${streamXmlCount}`,
    )
  })

  it('调试：逐步流式解析过程', () => {
    const simpleData = `<card>test</card>`

    console.log('=== 逐步流式解析调试 ===')
    console.log('数据:', simpleData)

    const parser = new TextStreamParser('debug-step')

    // 逐个字符发送
    for (let i = 0; i < simpleData.length; i++) {
      const char = simpleData[i]
      const isFinal = i === simpleData.length - 1

      console.log(`\n步骤 ${i + 1}: 发送字符 '${char}' (isFinal: ${isFinal})`)

      // 添加调试信息
      if (i === 5) {
        // 在步骤6（发送'>'）时
        console.log(`  当前buffer应该是: '<card>'`)

        // 测试正则表达式
        const testRegex = new RegExp(`<(card|user_maybe_say|analyze)(?:\\s|>)`)
        const testMatch = '<card>'.match(testRegex)
        console.log(`  正则测试结果:`, testMatch)
      }

      const results = parser.processText(char, isFinal)

      if (results.length > 0) {
        console.log('  产生结果:')
        results.forEach((r) => {
          console.log(`    ${r.message_id_type}: ${r.content}`)
        })
      } else {
        console.log('  无结果产生')
      }

      // 直接访问parser的buffer来调试
      if (i === 5) {
        console.log(`  实际buffer内容: "${(parser as any).buffer}"`)
      }
    }
  })

  it('核心功能验证 - card XML在流式解析中被正确标记', () => {
    const parser = new TextStreamParser('test-core-card')
    const allResults: StreamOutput[] = []

    // 使用合适的chunk大小确保card能被识别
    const chunkSize = 50
    for (let i = 0; i < testData.length; i += chunkSize) {
      const chunk = testData.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= testData.length
      const results = parser.processText(chunk, isFinal)
      allResults.push(...results)
    }

    // 验证card XML被正确标记
    const cardXmlResults = allResults.filter(
      (r) =>
        r.content.includes('<card') &&
        r.content.includes('</card>') &&
        r.message_id_type.includes(':xml'),
    )

    expect(cardXmlResults.length).toBeGreaterThanOrEqual(1)

    const cardContent = cardXmlResults[0].content
    expect(cardContent).toBe(
      '<card type="image" url="https://tina-test.bfbdata.com/api/image-gen/show_image/长街宴纪录片&1"></card>',
    )

    console.log('✅ Card XML在流式解析中被正确标记为XML类型')
  })

  it('专门测试流式解析中card XML的类型标记', () => {
    const parser = new TextStreamParser('test-card-stream')
    const allResults: StreamOutput[] = []

    // 模拟流式接收，每次发送10个字符
    const chunkSize = 10
    for (let i = 0; i < testData.length; i += chunkSize) {
      const chunk = testData.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= testData.length
      const results = parser.processText(chunk, isFinal)
      allResults.push(...results)
    }

    console.log('=== 流式解析中的card相关结果 ===')
    console.log(`总结果数量: ${allResults.length}`)

    // 提取最终状态
    const finalResults = extractFinalState(allResults)
    console.log(`最终状态数量: ${finalResults.length}`)

    // 找出所有包含card的最终结果
    const cardRelatedResults = finalResults.filter((r) =>
      r.content.includes('<card'),
    )

    console.log(`包含card的最终结果数量: ${cardRelatedResults.length}`)

    cardRelatedResults.forEach((result, index) => {
      console.log(`Card相关结果 ${index + 1}:`)
      console.log(`  类型: ${result.message_id_type}`)
      console.log(`  内容: ${result.content.substring(0, 100)}...`)
      console.log(`  是否为XML类型: ${result.message_id_type.includes(':xml')}`)
      console.log('---')
    })

    // 找出完整的card XML标签（只在最终状态中查找）
    const completeCardResults = finalResults.filter(
      (r) => r.content.includes('<card') && r.content.includes('</card>'),
    )

    console.log(`完整card标签数量: ${completeCardResults.length}`)

    if (completeCardResults.length > 0) {
      console.log('完整card标签详情:')
      completeCardResults.forEach((result, index) => {
        console.log(`  ${index + 1}. 类型: ${result.message_id_type}`)
        console.log(`     内容: ${result.content}`)
        console.log(
          `     是否为XML: ${result.message_id_type.includes(':xml')}`,
        )
      })
    }

    // 验证至少有一个完整的card XML标签被正确标记
    expect(completeCardResults.length).toBeGreaterThan(0)
    const xmlCardResults = completeCardResults.filter((r) =>
      r.message_id_type.includes(':xml'),
    )
    expect(xmlCardResults.length).toBeGreaterThan(0)
  })
})
