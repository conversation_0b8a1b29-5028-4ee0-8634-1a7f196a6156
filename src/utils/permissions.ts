import { isPlatform } from '@ionic/react'
import { Toast } from '@capacitor/toast'
import { androidPermissions } from './androidPermissions'
// 移除 Capacitor 插件导入
// import PermissionPlugin from '../plugins/permission'

export type PermissionType = 'microphone' | 'camera' | 'storage' | 'location'

export interface PermissionResult {
  granted: boolean
  message: string
  doNotAskAgain?: boolean
}

class PermissionManager {
  /**
   * 检查是否是 Android 环境
   */
  private isAndroid(): boolean {
    return isPlatform('android') && isPlatform('hybrid')
  }

  /**
   * 检查单个权限
   */
  async checkPermission(permission: PermissionType): Promise<boolean> {
    if (!isPlatform('hybrid')) {
      // 在 Web 环境下，实际检查浏览器权限
      if (permission === 'microphone') {
        try {
          // 检查浏览器是否支持权限查询
          if (navigator.permissions && navigator.permissions.query) {
            const result = await navigator.permissions.query({ name: 'microphone' as PermissionName })
            return result.state === 'granted'
          }
          // 如果不支持权限查询，返回false，让后续的请求权限逻辑来处理
          return false
        } catch (error) {
          console.log('Web环境权限检查失败:', error)
          // 检查失败时返回false，让后续请求权限
          return false
        }
      }
      // 其他权限在Web环境下返回true
      return true
    }

    // Android 环境使用我们的原生方案
    if (this.isAndroid()) {
      if (permission === 'microphone') {
        const result = await androidPermissions.checkMicrophonePermission()
        return result.hasPermission || false
      }
      // 其他权限暂时返回 true，因为我们主要关心麦克风权限
      return true
    }

    // iOS 或其他平台暂时返回 true
    return true
  }

  /**
   * 请求单个权限
   */
  async requestPermission(permission: PermissionType): Promise<PermissionResult> {
    if (!isPlatform('hybrid')) {
      // 在 Web 环境下，实际请求浏览器权限
      if (permission === 'microphone') {
        try {
          // 尝试获取麦克风权限
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
          // 成功获取，立即停止流以释放资源
          stream.getTracks().forEach(track => track.stop())
          return { granted: true, message: '麦克风权限已授权' }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          console.log('Web环境麦克风权限请求被拒绝:', errorMessage)
          
          // 判断是否为权限被永久拒绝
          const isDenied = errorMessage.includes('Permission denied') || 
                          errorMessage.includes('NotAllowedError') ||
                          errorMessage.includes('denied')
          
          return { 
            granted: false, 
            message: '麦克风权限被拒绝',
            doNotAskAgain: isDenied
          }
        }
      }
      // 其他权限在Web环境下返回成功
      return { granted: true, message: '浏览器环境，权限检查通过' }
    }

    // Android 环境使用我们的原生方案
    if (this.isAndroid()) {
      if (permission === 'microphone') {
        const result = await androidPermissions.requestMicrophonePermission()
        return {
          granted: result.granted || false,
          message: result.message || '权限请求完成',
          doNotAskAgain: result.doNotAskAgain
        }
      }
      // 其他权限暂时返回成功
      return { granted: true, message: '权限已授权' }
    }

    // iOS 或其他平台暂时返回成功
    return { granted: true, message: '权限已授权' }
  }

  /**
   * 请求多个权限
   */
  async requestMultiplePermissions(permissions: PermissionType[]): Promise<{
    allGranted: boolean
    results: Record<PermissionType, boolean>
    message: string
  }> {
    if (!isPlatform('hybrid')) {
      const results = {} as Record<PermissionType, boolean>
      permissions.forEach(p => results[p] = true)
      return {
        allGranted: true,
        results,
        message: '浏览器环境，权限检查通过'
      }
    }

    // Android 环境逐个处理权限
    if (this.isAndroid()) {
      const results = {} as Record<PermissionType, boolean>
      let allGranted = true
      
      for (const permission of permissions) {
        const result = await this.requestPermission(permission)
        results[permission] = result.granted
        if (!result.granted) {
          allGranted = false
        }
      }

      return {
        allGranted,
        results,
        message: allGranted ? '所有权限已授权' : '部分权限被拒绝'
      }
    }

    // iOS 或其他平台暂时返回成功
    const results = {} as Record<PermissionType, boolean>
    permissions.forEach(p => results[p] = true)
    return {
      allGranted: true,
      results,
      message: '权限已授权'
    }
  }

  /**
   * 检查并请求麦克风权限（专门为语音功能设计）
   * @param autoRequest 是否自动申请权限，默认为 true
   */
  async ensureMicrophonePermission(autoRequest: boolean = true): Promise<boolean> {
    try {
      // Android 环境使用我们的原生方案
      if (this.isAndroid()) {
        return await androidPermissions.ensureMicrophonePermission(autoRequest)
      }

      // 非 Android 环境的处理（包括Web环境）
      // 先检查是否已有权限
      const hasPermission = await this.checkPermission('microphone')
      if (hasPermission) {
        return true
      }

      // 如果不自动申请权限，直接返回 false
      if (!autoRequest) {
        return false
      }

      // 请求权限
      const result = await this.requestPermission('microphone')
      
      if (!result.granted) {
        console.log('权限被拒绝:', result.message)
        
        // 在Web环境下，不显示Toast，让调用方处理错误提示
        if (!isPlatform('hybrid')) {
          return false
        }
        
        // 只在Capacitor环境下显示Toast
        await Toast.show({
          text: result.message,
          duration: 'long'
        })

        // 如果被永久拒绝，询问是否跳转到设置
        if (result.doNotAskAgain) {
          return this.showGoToSettingsDialog()
        }
      }

      return result.granted
    } catch (error) {
      console.error('确保麦克风权限失败:', error)
      
      // 在Web环境下，不显示Toast，让调用方处理错误提示
      if (!isPlatform('hybrid')) {
        return false
      }
      
      // 只在Capacitor环境下显示Toast
      await Toast.show({
        text: '获取麦克风权限失败',
        duration: 'short'
      })
      return false
    }
  }

  /**
   * 显示跳转到设置的对话框
   */
  private async showGoToSettingsDialog(): Promise<boolean> {
    // Android 环境使用我们的原生方案
    if (this.isAndroid()) {
      const shouldGoToSettings = confirm('麦克风权限已被永久拒绝，是否前往设置页面手动开启？')
      
      if (shouldGoToSettings) {
        try {
          await androidPermissions.openAppSettings()
          return false // 跳转到设置后返回 false，用户需要手动返回应用
        } catch (error) {
          console.error('打开设置页面失败:', error)
          await Toast.show({
            text: '打开设置页面失败',
            duration: 'short'
          })
        }
      }
      
      return false
    }

    // 其他平台的处理保持不变
    const shouldGoToSettings = confirm('麦克风权限已被永久拒绝，是否前往设置页面手动开启？')
    
    if (shouldGoToSettings) {
      try {
        // 在非 Android 平台，我们无法直接打开设置
        await Toast.show({
          text: '请手动前往系统设置开启麦克风权限',
          duration: 'long'
        })
      } catch (error) {
        console.error('显示设置提示失败:', error)
      }
    }
    
    return false
  }

  /**
   * 获取 Android 权限名称（用于映射）
   */
  private getAndroidPermission(permission: PermissionType): string {
    const permissionMap = {
      microphone: 'android.permission.RECORD_AUDIO',
      camera: 'android.permission.CAMERA',
      storage: 'android.permission.WRITE_EXTERNAL_STORAGE',
      location: 'android.permission.ACCESS_FINE_LOCATION'
    }
    return permissionMap[permission] || ''
  }
}

// 导出单例
export const permissionManager = new PermissionManager()

// 导出便捷方法
export const checkMicrophonePermission = () => 
  permissionManager.checkPermission('microphone')

export const requestMicrophonePermission = () => 
  permissionManager.requestPermission('microphone')

export const ensureMicrophonePermission = (autoRequest: boolean = true) => 
  permissionManager.ensureMicrophonePermission(autoRequest) 