import { describe, expect, it } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

/**
 * 最小化测试用例：验证包含HTML标签的代码块不影响XML标签识别
 */
describe('TextStreamParser - 最小化问题测试', () => {
  it('应该正确处理包含HTML标签的代码块后的XML标签', () => {
    // 最小化测试用例
    const content = `前面内容

\`\`\`html
<p>HTML内容</p>
\`\`\`

<ina_analyze rank="8">分析内容</ina_analyze>`

    const parser = new TextStreamParser('test')
    const outputs = parser.processText(content, true)

    const xmlOutputs = outputs.filter((o) => o.message_id_type.includes(':xml'))

    console.log('XML输出数量:', xmlOutputs.length)
    console.log(
      '所有输出:',
      outputs.map((o) => ({
        type: o.message_id_type,
        hasInaAnalyze: o.content.includes('<ina_analyze'),
      })),
    )

    // 期望：ina_analyze应该被识别为XML
    expect(xmlOutputs.length).toBe(1)
    expect(xmlOutputs[0].content).toBe(
      '<ina_analyze rank="8">分析内容</ina_analyze>',
    )
  })

  it('应该正确处理用户原始的复杂内容', () => {
    // 用户原始的复杂内容
    const originalContent = `（秒切程序员皮肤）💻

**三种「劳动光荣」HTML变体**

\`\`\`html
\x3C!-- 极简主义版 -->
<!DOCTYPE html>
<html>
<body>
  <p>劳动光荣</p>
</body>
</html>
\`\`\`

<user_maybe_say></user_maybe_say>

<ina_analyze rank="8">
用户技术需求明确，可储备更多前端彩蛋
</ina_analyze>`

    const parser = new TextStreamParser('test-original')
    const outputs = parser.processText(originalContent, true)
    const finalOutputs = parser.finalize()
    const allOutputs = [...outputs, ...finalOutputs]

    const xmlOutputs = allOutputs.filter((o) =>
      o.message_id_type.includes(':xml'),
    )

    console.log('原始内容XML输出数量:', xmlOutputs.length)
    console.log(
      'XML标签:',
      xmlOutputs.map((o) => o.content),
    )

    // 期望：应该有2个XML标签被正确识别
    expect(xmlOutputs.length).toBe(2)
    expect(xmlOutputs.some((o) => o.content.includes('<user_maybe_say'))).toBe(
      true,
    )
    expect(xmlOutputs.some((o) => o.content.includes('<ina_analyze'))).toBe(
      true,
    )
  })
})
