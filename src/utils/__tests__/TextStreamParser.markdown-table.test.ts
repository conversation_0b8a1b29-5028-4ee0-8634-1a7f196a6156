import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '../TextStreamParser'

describe('TextStreamParser - Markdown Table Bug', () => {
  it('should not split markdown table into multiple parts', () => {
    const parser = new TextStreamParser('test')

    // 测试用例：包含markdown表格的完整文本
    const markdownWithTable = `# 奥尔加·托卡尔丘克（<PERSON>）深度档案

## 核心身份
- **生卒**：1962年1月29日生于波兰苏莱胡夫
- **荣誉**：2018年诺贝尔文学奖得主（2019年补颁）、两次获波兰最高文学奖「尼刻奖」
- **职业**：小说家、心理学家、公共知识分子

## 创作特征
\`\`\`mermaid
graph TD
    A[创作风格] --> B[魔幻现实主义]
    A --> C[多声部叙事]
    A --> D[历史解构]
    B --> E[《雅各布之书》梦境叙事]
    C --> F[《航班》碎片化视角]
    D --> G[《太古和其他的时间》神话重写]
\`\`\`

## 关键作品年表
| 年份 | 作品（中文/波兰文） | 国际影响 |
|------|---------------------|----------|
| 1996 | 《太古和其他的时间》<br>《Prawiek i inne czasy》 | 波兰当代文学经典 |
| 2007 | 《航班》<br>《Bieguni》 | 获2018年布克国际奖 |
| 2014 | 《雅各布之书》<br>《Księgi Jakubowe》 | 诺奖重要评审依据 |
| 2022 | 《Empuzjon》 | 最新生态主题小说 |

## 思想体系
1. **心理学背景**：华沙大学心理学学位影响其潜意识描写
2. **女性视角**：重构东欧历史中的边缘群体叙事
3. **生态关怀**：近年创作聚焦环境伦理议题

## 争议事件
- 2019年因批评波兰民族主义遭极端分子威胁
- 《雅各布之书》涉及犹太教改革内容引发宗教讨论

**权威资源**：
- 诺奖演讲视频 [官网链接](https://www.nobelprize.org/prizes/literature/2018/tokarczuk/lecture/)
- 华沙大学作家档案 [数据库](http://rcin.org.pl/dlibra/publication/edition/168066)`

    // 模拟流式输入，分块发送
    const chunks = [
      '# 奥尔加·托卡尔丘克（Olga Tokarczuk）深度档案\n\n## 核心身份\n- **生卒**：1962年1月29日生于波兰苏莱胡夫\n- **荣誉**：2018年诺贝尔文学奖得主（2019年补颁）、两次获波兰最高文学奖「尼刻奖」\n- **职业**：小说家、心理学家、公共知识分子\n\n## 创作特征\n```mermaid\ngraph TD\n    A[创作风格] --> B[魔幻现实主义]\n    A --> C[多声部叙事]\n    A --> D[历史解构]\n    B --> E[《雅各布之书》梦境叙事]\n    C --> F[《航班》碎片化视角]\n    D --> G[《太古和其他的时间》神话重写]\n```\n\n## 关键作品年表\n| 年份 | 作品（中文/波兰文） | 国际影响 |\n|------|---------------------|----------|\n| 1996 | 《太古和其他的时间》',
      '<br>《Prawiek i inne czasy》 | 波兰当代文学经典 |\n| 2007 | 《航班》',
      '<br>《Bieguni》 | 获2018年布克国际奖 |\n| 2014 | 《雅各布之书》',
      '<br>《Księgi Jakubowe》 | 诺奖重要评审依据 |\n| 2022 | 《Empuzjon》 | 最新生态主题小说 |\n\n## 思想体系\n1. **心理学背景**：华沙大学心理学学位影响其潜意识描写\n2. **女性视角**：重构东欧历史中的边缘群体叙事\n3. **生态关怀**：近年创作聚焦环境伦理议题\n\n## 争议事件\n- 2019年因批评波兰民族主义遭极端分子威胁\n- 《雅各布之书》涉及犹太教改革内容引发宗教讨论\n\n**权威资源**：\n- 诺奖演讲视频 [官网链接](https://www.nobelprize.org/prizes/literature/2018/tokarczuk/lecture/)\n- 华沙大学作家档案 [数据库](http://rcin.org.pl/dlibra/publication/edition/168066)',
    ]

    const allResults: any[] = []

    // 逐块处理
    chunks.forEach((chunk, index) => {
      console.log(`\n=== 处理第 ${index + 1} 块 ===`)
      console.log(`输入: "${chunk.substring(0, 100)}..."`)

      const results = parser.processText(chunk)
      allResults.push(...results)

      console.log(`输出 ${results.length} 个片段:`)
      results.forEach((result, i) => {
        console.log(
          `  ${i + 1}. ${result.message_id_type}: "${result.content.substring(0, 100)}..."`,
        )
      })
    })

    // 完成处理
    const finalResults = parser.finalize()
    allResults.push(...finalResults)

    console.log(`\n=== 最终结果 ===`)
    console.log(`总共 ${allResults.length} 个片段`)

    // 重新组合所有文本内容
    const reconstructedText = allResults
      .filter((result) => result.message_id_type.includes(':text'))
      .map((result) => result.content)
      .join('')

    console.log(`\n=== 重构的文本 ===`)
    console.log(reconstructedText)

    // 检查表格是否被正确保持为一个整体
    const tableStart = '| 年份 | 作品（中文/波兰文） | 国际影响 |'
    const tableEnd = '| 2022 | 《Empuzjon》 | 最新生态主题小说 |'

    // 查找包含表格开始的片段
    const tableStartFragment = allResults.find((result) =>
      result.content.includes(tableStart),
    )

    // 查找包含表格结束的片段
    const tableEndFragment = allResults.find((result) =>
      result.content.includes(tableEnd),
    )

    console.log('\n=== 表格分析 ===')
    console.log('表格开始片段:', tableStartFragment?.message_id_type)
    console.log('表格结束片段:', tableEndFragment?.message_id_type)

    // 验证表格没有被拆分
    if (tableStartFragment && tableEndFragment) {
      const sameFragment =
        tableStartFragment.message_id_type === tableEndFragment.message_id_type
      console.log('表格是否在同一片段:', sameFragment)

      if (!sameFragment) {
        console.log('\n❌ BUG: 表格被拆分到不同片段!')
        console.log(
          '开始片段内容:',
          tableStartFragment.content.substring(0, 200),
        )
        console.log('结束片段内容:', tableEndFragment.content.substring(0, 200))
      } else {
        console.log('\n✅ 表格保持完整')
      }

      // 测试断言
      expect(sameFragment).toBe(true)
    } else {
      throw new Error('无法找到表格内容')
    }
  })

  it('should handle table with <br> tags correctly', () => {
    const parser = new TextStreamParser('test-br')

    // 模拟流式输入，分块发送包含<br>标签的表格
    const chunks = [
      '| 年份 | 作品 | 影响 |\n|------|------|------|\n| 1996 | 《太古和其他的时间》',
      '<br>《Prawiek i inne czasy》 | 经典 |\n| 2007 | 《航班》',
      '<br>《Bieguni》 | 获奖 |',
    ]

    const allResults: any[] = []

    // 逐块处理
    chunks.forEach((chunk, index) => {
      const results = parser.processText(chunk)
      allResults.push(...results)
    })

    // 完成处理
    const finalResults = parser.finalize()
    allResults.push(...finalResults)

    console.log('\n=== 简化表格测试 ===')
    allResults.forEach((result, i) => {
      console.log(`${i + 1}. ${result.message_id_type}: "${result.content}"`)
    })

    // 重新组合所有文本内容
    const reconstructedText = allResults
      .filter((result) => result.message_id_type.includes(':text'))
      .map((result) => result.content)
      .join('')

    console.log(
      `文本片段数量: ${allResults.filter((r) => r.message_id_type.includes(':text')).length}`,
    )
    console.log(`重构的文本: "${reconstructedText}"`)

    // 验证关键点：
    // 1. <br>标签应该被保留在表格中
    expect(reconstructedText).toContain('<br>')

    // 2. 表格结构应该完整
    expect(reconstructedText).toContain('| 年份 | 作品 | 影响 |')
    expect(reconstructedText).toContain(
      '《太古和其他的时间》<br>《Prawiek i inne czasy》',
    )
    expect(reconstructedText).toContain('《航班》<br>《Bieguni》')

    // 3. 表格内容不应该被错误拆分（不应该有段落分隔符导致的内容丢失）
    expect(reconstructedText).toContain('| 1996 |')
    expect(reconstructedText).toContain('| 2007 |')
  })
})
