import { StreamMessage } from '@/tina/lib/EmotionMindClient.browser'

/**
 * 显示确认对话框的工具函数
 * 通过全局事件系统触发对话框显示
 */
export const showConfirmationDialog = (
  message: StreamMessage,
  requireInput: boolean = false,
) => {
  // 检查全局方法是否可用
  if (typeof (window as any).showConfirmationDialog === 'function') {
    ;(window as any).showConfirmationDialog(message, requireInput)
  } else {
    console.warn('ConfirmationDialogManager 未初始化，无法显示确认对话框')
  }
}

/**
 * 创建模拟的确认消息
 * 用于测试和开发
 */
export const createMockConfirmationMessage = (
  type: 'change_plan' | 'ask_user_info',
  content: string,
  sessionId: string = 'mock-session-id',
): StreamMessage => {
  return {
    type: 'task',
    tool_name: 'tina_task_query_status',
    content,
    timestamp: new Date().toISOString(),
    metadata: {
      content,
      session_id: sessionId,
      step: -1,
      timestamp: Date.now(),
      xml_type: type,
    },
  }
}
