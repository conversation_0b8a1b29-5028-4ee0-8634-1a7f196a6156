import { isPlatform } from '@ionic/core'
import { atomWithStorage as jotaiAtomWithStorage } from 'jotai/utils'
import type { SyncStorage } from 'jotai/vanilla/utils/atomWithStorage'
// 导入 localStorage 数据
import localStorageData from '../data/localStorage.json'
import localStorageDataMobile from '../data/localStorage.mobile.json'

export const storageEventEmitter = new EventTarget()

export const STORAEG_UPDATE_KEY = 'STORAEG_UPDATE'

// 创建一个模拟的存储，从 JSON 文件读取数据
const createMockStorage = (): Pick<
  Storage,
  'getItem' | 'setItem' | 'removeItem'
> => {
  const data = isPlatform('hybrid') ? localStorageDataMobile : localStorageData

  return {
    getItem: (key: string): string | null => {
      const value = data[key as keyof typeof data]
      return value ? JSON.stringify(value) : null
    },
    setItem: (key: string, value: string): void => {
      try {
        ;(data as any)[key] = JSON.parse(value)
      } catch {
        ;(data as any)[key] = value
      }
    },
    removeItem: (key: string): void => {
      delete (data as any)[key]
    },
  }
}

const mockStorage = createMockStorage()

function atomWithStorage<Value>(key: string, initialValue: Value) {
  return jotaiAtomWithStorage<Value>(
    key,
    initialValue,
    {
      ...mockStorage,
      setItem: (key: string, newValue: Value) => {
        const stringValue = JSON.stringify(newValue)
        mockStorage.setItem(key, stringValue)
        storageEventEmitter.dispatchEvent(
          new CustomEvent(STORAEG_UPDATE_KEY, { detail: [key, stringValue] }),
        )
      },
      getItem: (key: string): Value | null => {
        const value = mockStorage.getItem(key)
        if (value === null) return null
        try {
          return JSON.parse(value)
        } catch {
          return value as Value
        }
      },
      removeItem: (key: string): void => {
        mockStorage.removeItem(key)
        storageEventEmitter.dispatchEvent(
          new CustomEvent(STORAEG_UPDATE_KEY, { detail: [key, null] }),
        )
      },
    } as SyncStorage<Value>,
    { getOnInit: true },
  )
}

export default atomWithStorage
