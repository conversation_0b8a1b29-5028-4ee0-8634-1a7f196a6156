import { atom } from 'jotai'

// 基础按钮发送模式原子
const baseButtonSendModeAtom = atom<boolean>(
  // 从 localStorage 读取初始值，默认为 true（按钮发送）
  (() => {
    try {
      const stored = localStorage.getItem('buttonSendMode')
      return stored ? JSON.parse(stored) : true
    } catch {
      return true
    }
  })()
)

// 带持久化的按钮发送模式原子
const buttonSendModeAtom = atom(
  (get) => get(baseButtonSendModeAtom),
  (get, set, newValue: boolean) => {
    set(baseButtonSendModeAtom, newValue)
    try {
      localStorage.setItem('buttonSendMode', JSON.stringify(newValue))
    } catch (error) {
      console.warn('保存按钮发送模式设置失败:', error)
    }
  }
)

export default buttonSendModeAtom