import type { Descendant } from 'slate'

export enum EConversationType {
  text = 'text',
  image = 'image',
  video = 'video',
  voice = 'voice',
  centerText = 'centerText',
  transfer = 'transfer',
  redPacket = 'redPacket',
  redPacketAcceptedReply = 'redPacketAcceptedReply',
  personalCard = 'personalCard',
  news = 'news',
  markdown = 'markdown',
  markmap = 'markmap',
  userMaybeSay = 'userMaybeSay',
  notification = 'notification',
}

export type TConversationRole = 'mine' | 'friend'

export interface IConversationItemBase {
  id: string
  upperText?: string
  timestamp?: string
  role: TConversationRole
}

export interface IConversationTypeText extends IConversationItemBase {
  type: EConversationType.text
  textContent: Descendant[]
  referenceId?: IConversationItemBase['id']
  sendStatus?: 'sending' | 'sent' | 'failed'
}

export interface IConversationTypeSingleUpperText
  extends IConversationItemBase {
  type: EConversationType.centerText
  simpleContent: string
  extraClassName?: string
}

export interface IConversationTypeTransfer extends IConversationItemBase {
  type: EConversationType.transfer
  originalSender: TConversationRole
  transferStatus: 'awaiting' | 'accepted' | 'rejected' | 'expired'
  amount: string
  note?: string
}

export interface IConversationTypeRedPacket extends IConversationItemBase {
  type: EConversationType.redPacket
  originalSender: TConversationRole
  redPacketStatus: 'awaiting' | 'accepted' | 'expired'
  amount: string
  note?: string
}

export interface IConversationTypeRedPacketAcceptedReply
  extends IConversationItemBase {
  type: EConversationType.redPacketAcceptedReply
  redPacketId: string
}

export interface IConversationTypeImage extends IConversationItemBase {
  type: EConversationType.image
  imageInfo: string
  sendStatus?: 'sending' | 'sent' | 'failed'
}

export interface ICoversationTypeVideo extends IConversationItemBase {
  type: EConversationType.video
  videoInfo: string
  videoUrl?: string
  sendStatus?: 'sending' | 'sent' | 'failed'
}

export interface IConversationTypeVoice extends IConversationItemBase {
  type: EConversationType.voice
  duration: number
  isRead?: boolean
  /** 是否显示语音转文字内容 */
  showStt?: boolean
  /** 语音转文字内容（处理后的，用于显示） */
  stt?: string
  /** 原始语音转文字内容（用于音频URL） */
  originalStt?: string
  /** 流式状态: 'creating' | 'first-buffered' | 'complete' */
  streamState?: 'creating' | 'first-buffered' | 'complete'
  /** 第一段语音内容（用于快速播放） */
  firstSegment?: string
  /** 完整语音内容（流式累加） */
  fullContent?: string
  /** 剩余段落内容（第一段之后的内容） */
  remainingContent?: string
}

export interface IConversationTypePersonalCard extends IConversationItemBase {
  type: EConversationType.personalCard
  avatarInfo: string
  nickname: string
}

export interface IConversationTypeNews extends IConversationItemBase {
  type: EConversationType.news
  title: string
  description: string
  imageUrl: string
  source: string
  url: string
}

export interface IConversationTypeMarkdown extends IConversationItemBase {
  type: EConversationType.markdown
  markdownContent: string
  summary?: string
}

export interface IConversationTypeMarkmap extends IConversationItemBase {
  type: EConversationType.markmap
  title: string
  content: string
}

export interface IConversationTypeUserMaybeSay extends IConversationItemBase {
  type: EConversationType.userMaybeSay
  suggestions: string[]
}

export interface IConversationTypeNotification extends IConversationItemBase {
  type: EConversationType.notification
  notificationId: string
  text: string
  icon?: string // FontAwesome icon class
  hideAfter?: number // 隐藏时间（毫秒）
  extraClassName?: string
}

export type TConversationItem =
  | IConversationTypeText
  | IConversationTypeSingleUpperText
  | IConversationTypeTransfer
  | IConversationTypeImage
  | ICoversationTypeVideo
  | IConversationTypeVoice
  | IConversationTypeRedPacket
  | IConversationTypeRedPacketAcceptedReply
  | IConversationTypePersonalCard
  | IConversationTypeNews
  | IConversationTypeMarkdown
  | IConversationTypeMarkmap
  | IConversationTypeUserMaybeSay
  | IConversationTypeNotification
