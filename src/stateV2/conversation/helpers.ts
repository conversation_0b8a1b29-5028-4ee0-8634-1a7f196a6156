import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'

// 扩展 dayjs 插件
dayjs.extend(isToday)

/**
 * 根据当前消息时间戳和前一条消息时间戳生成上方时间文本
 * @param currentTimestamp 当前消息的时间戳
 * @param previousTimestamp 前一条消息的时间戳（可选）
 * @returns 如果时间间隔超过5分钟则返回格式化的时间字符串，否则返回undefined
 */
export const fromLastGenerateUpperText = (
  currentTimestamp: string,
  previousTimestamp?: string,
): string | undefined => {
  // 如果当前消息没有时间戳，不显示时间
  if (!currentTimestamp) {
    return undefined
  }

  const currentTime = dayjs(currentTimestamp)

  // 如果没有前一条消息，显示格式化的时间
  if (!previousTimestamp) {
    return formatMessageTime(currentTime)
  }

  // 计算时间差（分钟）
  const previousTime = dayjs(previousTimestamp)
  const timeDiffInMinutes = currentTime.diff(previousTime, 'minute')

  // 如果时间间隔超过5分钟，显示格式化的时间
  if (timeDiffInMinutes >= 5) {
    return formatMessageTime(currentTime)
  }

  // 否则不显示时间
  return undefined
}

/**
 * 根据时间距离现在的远近格式化时间显示
 * @param time dayjs 时间对象
 * @returns 格式化的时间字符串
 */
function formatMessageTime(time: dayjs.Dayjs): string {
  const now = dayjs()

  // 今天内的消息：只显示时间 (HH:mm)
  if (time.isToday()) {
    return time.format('HH:mm')
  }

  // 本周内的消息：显示周几 + 时间
  if (isThisWeek(time, now)) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const weekday = weekdays[time.day()]
    return `${weekday} ${time.format('HH:mm')}`
  }

  // 本年内的消息：显示月日 + 时间
  if (isThisYear(time, now)) {
    return time.format('MM月DD日 HH:mm')
  }

  // 更早的消息：显示年月日 + 时间
  return time.format('YYYY年MM月DD日 HH:mm')
}

/**
 * 判断时间是否在本周内（周一到周日）
 * @param time 要判断的时间
 * @param now 当前时间
 * @returns 是否在本周内
 */
function isThisWeek(time: dayjs.Dayjs, now: dayjs.Dayjs): boolean {
  // 获取本周一的开始时间
  const startOfWeek = now.startOf('week').add(1, 'day').startOf('day')
  // 获取本周日的结束时间
  const endOfWeek = startOfWeek.add(6, 'day').endOf('day')
  return (
    (time.isAfter(startOfWeek) || time.isSame(startOfWeek)) &&
    (time.isBefore(endOfWeek) || time.isSame(endOfWeek))
  )
}

/**
 * 判断时间是否在本年内
 * @param time 要判断的时间
 * @param now 当前时间
 * @returns 是否在本年内
 */
function isThisYear(time: dayjs.Dayjs, now: dayjs.Dayjs): boolean {
  return time.year() === now.year()
}
