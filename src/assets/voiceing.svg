<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    .speaker-wave {
      fill: black;
      fill-opacity: 0.9; /* Original fill opacity */
      fill-rule: evenodd;
      clip-rule: evenodd;
    }

    #wave-middle {
      animation: animateWaveMiddle 1.5s infinite step-end;
      opacity: 0; /* Start hidden for the first frame if animation is delayed or not perfectly synced */
    }

    #wave-outer {
      animation: animateWaveOuter 1.5s infinite step-end;
      opacity: 0; /* Start hidden for the first two frames */
    }

    /*
    Animation states (total duration 1.5s, 3 steps of 0.5s each):
    - Frame 1 (0s - 0.5s):       Base only (Middle: opacity 0, Outer: opacity 0)
    - Frame 2 (0.5s - 1.0s):     Base + Middle (Middle: opacity 1, Outer: opacity 0)
    - Frame 3 (1.0s - 1.5s):     Base + Middle + Outer (Middle: opacity 1, Outer: opacity 1)
    */

    @keyframes animateWaveMiddle {
      0% { opacity: 0; }         /* Frame 1: Hidden */
      33.33% { opacity: 1; }     /* Frame 2: Visible */
      100% { opacity: 1; }       /* Frame 3: Stays Visible */
    }

    @keyframes animateWaveOuter {
      0% { opacity: 0; }         /* Frame 1: Hidden */
      33.33% { opacity: 0; }     /* Frame 2: Still Hidden */
      66.66% { opacity: 1; }     /* Frame 3: Visible */
      100% { opacity: 1; }       /* Stays Visible until loop */
    }
  </style>

  <!-- Speaker Base (innermost part, always visible) -->
  <path id="speaker-base" class="speaker-wave"
        d="M9.9 12C9.9 12.6627 9.63137 13.2627 9.19706 13.6971L7.5 12L9.19706 10.3029C9.63137 10.7373 9.9 11.3373 9.9 12Z"/>

  <!-- Middle Sound Wave -->
  <path id="wave-middle" class="speaker-wave"
        d="M10.3284 14.8284C11.0745 14.0823 11.5 13.0764 11.5 12C11.5 10.9236 11.0745 9.91765 10.3284 9.17157L11.177 8.32304C12.118 9.26405 12.7 10.5641 12.7 12C12.7 13.4359 12.118 14.7359 11.177 15.6769L10.3284 14.8284Z"/>

  <!-- Outer Sound Wave -->
  <path id="wave-outer" class="speaker-wave"
        d="M12.3083 16.8083C13.5761 15.5405 14.3 13.8291 14.3 12C14.3 10.1709 13.5761 8.45946 12.3083 7.19167L13.1569 6.34314C14.6046 7.79085 15.5 9.79085 15.5 12C15.5 14.2091 14.6046 16.2091 13.1569 17.6568L12.3083 16.8083Z"/>
</svg>