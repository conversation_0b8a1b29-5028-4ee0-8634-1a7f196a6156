@tailwind base;
@tailwind components;
@tailwind utilities;

/* 确保全屏高度 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#root {
  --ion-background-color: #f5f5f5;
  height: 100%;
  width: 100%;
}

img {
  @apply select-none;
  -webkit-user-drag: none;
}

/* 移动端优化 */
@media (max-width: 768px) {
  html,
  body {
    height: 100vh;
    height: 100dvh; /* 动态视口高度，支持移动端 */
  }

  #root {
    height: 100vh;
    height: 100dvh;
  }

  /* 确保所有元素不超出视口宽度 */
  * {
    box-sizing: border-box;
    max-width: 100vw;
  }
}

/* todo: 好像是 tailwind3 导致的浅蓝色激活背景，没找到*/
*:active {
  -webkit-tap-highlight-color: transparent; /* 针对移动端点击高亮 */
  outline: none; /* 移除焦点轮廓（谨慎使用，需保留 accessibility） */
}
