import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'
import en from 'dayjs/locale/en'
import zhCN from 'dayjs/locale/zh-cn'
import zhTW from 'dayjs/locale/zh-tw'
import isToday from 'dayjs/plugin/isToday'
import isYesterday from 'dayjs/plugin/isYesterday'
import relativeTime from 'dayjs/plugin/relativeTime'
import updateLocale from 'dayjs/plugin/updateLocale'
import weekday from 'dayjs/plugin/weekday'
import { getCurrentLanguage } from './i18n'

export const LOCALE_MAP = {
  'zh-CN': zhCN,
  'en-US': en,
  'zh-TW': zhTW,
}

const isCurrentYear = (_option: any, dayjsClass: any, dayjsFactory: any) => {
  dayjsClass.prototype.isCurrentYear = function () {
    const comparisonTemplate = 'YYYY'
    const now = dayjsFactory()
    return this.format(comparisonTemplate) === now.format(comparisonTemplate)
  }
}

export const initDayjs = () => {
  const curLang = getCurrentLanguage()
  ;(dayjs as any).locale(LOCALE_MAP[curLang as keyof typeof LOCALE_MAP])
  ;(dayjs as any).extend(updateLocale)
  ;(dayjs as any).extend(isToday)
  ;(dayjs as any).extend(isYesterday)
  ;(dayjs as any).extend(relativeTime)
  ;(dayjs as any).extend(isCurrentYear)
  ;(dayjs as any).extend(weekday)
}
