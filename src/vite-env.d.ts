/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />
/// <reference types="@emotion/react/types/css-prop" />
/// <reference lib="dom" />
/// <reference lib="dom.iterable" />

declare global {
  interface DisplayMediaStreamOptions {
    preferCurrentTab?: boolean
  }

  interface MediaStreamTrack {
    cropTo: (element: Element) => Promise<unknown>
  }

  interface CropTarget {
    fromElement: (element: Element) => Promise<Element>
  }

  declare const CropTarget: CropTarget

  interface Window {
    setDevice: (v: string) => void
    __SHARE_KEY__: string | undefined
    isOpenedByPuppeteer?: boolean
    importDB: (db: { data: number[] }) => Promise<void>
  }
}

declare module 'dayjs' {
  interface Dayjs {
    isCurrentYear(): boolean
  }
}

export interface CustomElementEmoji {
  type: 'emoji'
  emojiSymbol: string
  children: Array<{ text: string }>
}
