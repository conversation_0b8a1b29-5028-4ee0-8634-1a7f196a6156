import { useState } from 'react'
import {
  IonContent,
  IonPage,
  IonTextarea,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonBackButton,
} from '@ionic/react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import rehypeHighlight from 'rehype-highlight'

const TextTestPage = () => {
  const [inputText, setInputText] = useState(`# 欢迎使用 Text 组件测试

这是一个 **Markdown** 测试页面，用于测试 Text 组件的 Markdown 渲染效果。

## 功能特性

- 支持 *斜体* 和 **粗体** 文本
- 支持 [链接](https://example.com)
- 支持 \`内联代码\`

### 代码块示例

\`\`\`javascript
console.log("Hello World!");
const message = "这是一个代码块";
\`\`\`

### 引用块

> 这是一个引用块
> 可以包含多行内容

### 列表

#### 有序列表
1. 第一项
2. 第二项
3. 第三项

#### 无序列表
- 项目 A
- 项目 B
- 项目 C

### 分隔线

---

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

试试修改左侧的文本，右侧会实时更新！`)

  const handleInputChange = (value: string) => {
    setInputText(value)
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/test" />
          </IonButtons>
          <IonTitle>Text 组件 Markdown 测试</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="ion-padding">
        <div className="h-full">
          {/* 主要内容区域 - 左右分栏 */}
          <div className="grid h-full grid-cols-1 gap-4 lg:grid-cols-2">
            {/* 左侧输入区域 */}
            <div className="flex flex-col">
              <h2 className="mb-4 text-lg font-bold text-gray-800">
                Markdown 输入
              </h2>
              <IonTextarea
                value={inputText}
                onIonInput={(e) => handleInputChange(e.detail.value!)}
                placeholder="请输入 Markdown 文本..."
                className="flex-1 font-mono text-sm"
                fill="outline"
                rows={100}
              />
            </div>

            {/* 右侧渲染区域 */}
            <div className="flex flex-col">
              <h2 className="mb-4 text-lg font-bold text-gray-800">
                Text 组件 Markdown 渲染效果
              </h2>
              <div className="flex-1 overflow-auto rounded-lg border border-gray-200 bg-gray-50 p-4">
                {/* 模拟微信消息气泡样式，使用与 Text 组件相同的 Markdown 渲染 */}
                <div className="group friend flex items-start gap-3">
                  {/* 头像 */}
                  <div className="h-10 w-10 min-w-10 cursor-pointer rounded bg-blue-500 object-cover object-center flex items-center justify-center text-white text-sm font-bold">
                    测
                  </div>
                  
                  {/* 消息内容 - 使用与 Text 组件相同的样式和配置 */}
                  <div className="relative max-w-[85%] break-words rounded p-[10px] text-gray-800 border border-gray-200">
                    <div className="mb-2 text-xs text-gray-500">
                      测试用户
                    </div>
                    
                    <div className="leading-relaxed" style={{ lineHeight: '1.618' }}>
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeRaw ]}
                        skipHtml={false}
                        components={{
                          // 链接 - 与 Text 组件相同的样式
                          a: ({ href, children, ...props }) => (
                            <a
                              href="#"
                              onClick={(e) => {
                                e.preventDefault()
                                console.log('Link clicked:', href)
                              }}
                              className="inline-flex cursor-pointer items-center gap-1 text-blue-600 hover:text-blue-800"
                              {...props}
                            >
                              <span className="break-all">
                                {children}
                                <svg
                                  className="inline-block h-3 w-3"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              </span>
                            </a>
                          ),
                          // 代码 - 与 Text 组件相同的样式
                          code: ({ node, className, children, ...props }) => {
                            const isCodeBlock = className && className.startsWith('language-')
                            
                            if (!isCodeBlock) {
                              // 内联代码
                              return (
                                <span
                                  className="mx-1 rounded bg-gray-200 px-1 py-0.5 font-mono text-sm text-gray-800"
                                  {...props}
                                >
                                  {children}
                                </span>
                              )
                            } else {
                              // 代码块
                              return (
                                <blockquote className="border-l-4 border-gray-300 bg-gray-100 p-3 italic text-gray-700">
                                  <code
                                    className="bg-transparent font-mono text-sm"
                                    {...props}
                                  >
                                    {children}
                                  </code>
                                </blockquote>
                              )
                            }
                          },
                          // 标题
                          h1: ({ children, ...props }) => (
                            <h1 className="mb-4 text-2xl font-bold text-gray-900" {...props}>
                              {children}
                            </h1>
                          ),
                          h2: ({ children, ...props }) => (
                            <h2 className="mb-3 text-xl font-bold text-gray-900" {...props}>
                              {children}
                            </h2>
                          ),
                          h3: ({ children, ...props }) => (
                            <h3 className="mb-2 text-lg font-bold text-gray-900" {...props}>
                              {children}
                            </h3>
                          ),
                          h4: ({ children, ...props }) => (
                            <h4 className="mb-2 text-base font-bold text-gray-900" {...props}>
                              {children}
                            </h4>
                          ),
                          h5: ({ children, ...props }) => (
                            <h5 className="mb-1 text-sm font-bold text-gray-900" {...props}>
                              {children}
                            </h5>
                          ),
                          h6: ({ children, ...props }) => (
                            <h6 className="mb-1 text-xs font-bold text-gray-900" {...props}>
                              {children}
                            </h6>
                          ),
                          // 引用块
                          blockquote: ({ children, ...props }) => (
                            <blockquote
                              className="my-4 border-l-4 border-gray-300 bg-gray-50 p-4 italic text-gray-700"
                              {...props}
                            >
                              {children}
                            </blockquote>
                          ),
                          // 表格
                          table: ({ children, ...props }) => (
                            <div className="my-4 overflow-x-auto">
                              <table
                                className="min-w-full border-collapse border border-gray-300"
                                {...props}
                              >
                                {children}
                              </table>
                            </div>
                          ),
                          thead: ({ children, ...props }) => (
                            <thead className="bg-gray-100" {...props}>
                              {children}
                            </thead>
                          ),
                          tbody: ({ children, ...props }) => (
                            <tbody className="bg-white" {...props}>
                              {children}
                            </tbody>
                          ),
                          tr: ({ children, ...props }) => (
                            <tr className="border-b border-gray-200" {...props}>
                              {children}
                            </tr>
                          ),
                          th: ({ children, ...props }) => (
                            <th
                              className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900"
                              {...props}
                            >
                              {children}
                            </th>
                          ),
                          td: ({ children, ...props }) => (
                            <td
                              className="border border-gray-300 px-4 py-2 text-gray-800"
                              {...props}
                            >
                              {children}
                            </td>
                          ),
                          // 列表
                          ul: ({ children, ...props }) => (
                            <ul className="my-4 list-disc pl-6 text-gray-800" {...props}>
                              {children}
                            </ul>
                          ),
                          ol: ({ children, ...props }) => (
                            <ol className="my-4 list-decimal pl-6 text-gray-800" {...props}>
                              {children}
                            </ol>
                          ),
                          li: ({ children, ...props }) => (
                            <li className="text-gray-800" {...props}>
                              {children}
                            </li>
                          ),
                          // 段落
                          p: ({ children, ...props }) => <p {...props}>{children}</p>,
                          // 强调
                          strong: ({ children, ...props }) => (
                            <strong className="font-bold text-gray-900" {...props}>
                              {children}
                            </strong>
                          ),
                          em: ({ children, ...props }) => (
                            <em className="italic text-gray-800" {...props}>
                              {children}
                            </em>
                          ),
                          // 分隔线
                          hr: ({ ...props }) => (
                            <hr className="my-6 border-t border-gray-300" {...props} />
                          ),
                        }}
                      >
                        {inputText}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default TextTestPage
