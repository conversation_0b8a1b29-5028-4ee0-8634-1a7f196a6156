import React, { useState } from 'react'
import { IonButton, IonContent, IonHeader, IonPage, IonTitle, IonToolbar, IonItem, IonLabel, IonInput, IonTextarea } from '@ionic/react'
import { generateUserMaybeSay, parseUserMaybeSayContent } from '@/tina/lib/user-maybe-say'
import { useAuthStore } from '@/tina/stores/authStore'

const TestUserMaybeSayPage: React.FC = () => {
  const [userId, setUserId] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [suggestions, setSuggestions] = useState<string[]>([])

  const auth = useAuthStore((state) => state.auth)

  const handleTest = async () => {
    if (!userId.trim()) {
      setError('请输入用户ID')
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)
    setSuggestions([])

    try {
      console.log('🔄 测试获取 user maybe say:', userId)
      const response = await generateUserMaybeSay(userId.trim())
      
      console.log('✅ API 响应:', response)
      setResult(response)

      // 解析建议内容
      if (response.data.user_maybe_say) {
        const parsedSuggestions = parseUserMaybeSayContent(response.data.user_maybe_say)
        setSuggestions(parsedSuggestions)
        console.log('✅ 解析的建议:', parsedSuggestions)
      }
    } catch (err: any) {
      console.error('🚨 测试失败:', err)
      setError(err.message || '测试失败')
    } finally {
      setLoading(false)
    }
  }

  const handleUseCurrentUser = () => {
    if (auth.userId) {
      setUserId(auth.userId)
    } else {
      setError('当前用户未登录')
    }
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>测试 User Maybe Say API</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <IonItem>
          <IonLabel position="stacked">用户ID</IonLabel>
          <IonInput
            value={userId}
            onIonInput={(e) => setUserId(e.detail.value!)}
            placeholder="输入用户ID"
          />
        </IonItem>

        <div className="ion-margin-top">
          <IonButton 
            expand="block" 
            onClick={handleUseCurrentUser}
            fill="outline"
            disabled={!auth.userId}
          >
            使用当前登录用户 ({auth.userId || '未登录'})
          </IonButton>
        </div>

        <div className="ion-margin-top">
          <IonButton 
            expand="block" 
            onClick={handleTest}
            disabled={loading || !userId.trim()}
          >
            {loading ? '测试中...' : '测试 API'}
          </IonButton>
        </div>

        {error && (
          <div className="ion-margin-top">
            <IonItem color="danger">
              <IonLabel>
                <h3>错误</h3>
                <p>{error}</p>
              </IonLabel>
            </IonItem>
          </div>
        )}

        {result && (
          <div className="ion-margin-top">
            <IonItem>
              <IonLabel>
                <h3>API 响应</h3>
                <IonTextarea
                  readonly
                  value={JSON.stringify(result, null, 2)}
                  rows={8}
                />
              </IonLabel>
            </IonItem>
          </div>
        )}

        {suggestions.length > 0 && (
          <div className="ion-margin-top">
            <IonItem>
              <IonLabel>
                <h3>解析的建议 ({suggestions.length} 条)</h3>
                {suggestions.map((suggestion, index) => (
                  <p key={index}>
                    {index + 1}. {suggestion}
                  </p>
                ))}
              </IonLabel>
            </IonItem>
          </div>
        )}

        <div className="ion-margin-top">
          <IonItem>
            <IonLabel>
              <h3>使用说明</h3>
              <p>1. 输入用户ID或使用当前登录用户</p>
              <p>2. 点击"测试 API"按钮</p>
              <p>3. 查看API响应和解析结果</p>
              <p>4. 检查控制台日志获取详细信息</p>
            </IonLabel>
          </IonItem>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default TestUserMaybeSayPage
