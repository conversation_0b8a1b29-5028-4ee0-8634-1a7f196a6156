import { memo } from 'react'
import type { TaskInfo } from '@/tina/services/task-service.ts'
import { IonIcon } from '@ionic/react'
import {
  checkmarkCircleOutline,
  chevronDownOutline,
  chevronUpOutline,
  pauseOutline,
  timeOutline,
} from 'ionicons/icons'
import { useTaskCenterStore } from './store'

interface TaskListProps {
  tasks: TaskInfo[]
  onTaskClick: (taskId: string) => void
  isLoading?: boolean
  error?: string | null
}

/**
 * 任务列表组件
 * 显示执行中、待执行、已完成的任务
 */
export const TaskList = memo(({ tasks, onTaskClick }: TaskListProps) => {
  // 分类任务
  const executingTasks = tasks.filter((t) => t.status === 'executing')
  const pendingTasks = tasks.filter((t) => t.status === 'pending')
  const completedTasks = tasks.filter((t) => t.status === 'completed')

  return (
    <div className='task-content-area' style={{ backgroundColor: '#EDEDED' }}>
      {/* 执行中任务 */}
      <TaskSection
        title='执行中'
        count={executingTasks.length}
        icon={timeOutline}
        iconColor='#07C160'
        tasks={executingTasks}
        onTaskClick={onTaskClick}
        category='executing'
      />

      {/* 待执行任务 */}
      <TaskSection
        title='待执行'
        count={pendingTasks.length}
        icon={pauseOutline}
        iconColor='#888888'
        tasks={pendingTasks}
        onTaskClick={onTaskClick}
        category='pending'
      />

      {/* 已完成任务 */}
      <TaskSection
        title='已完成'
        count={completedTasks.length}
        icon={checkmarkCircleOutline}
        iconColor='#888888'
        tasks={completedTasks}
        onTaskClick={onTaskClick}
        category='completed'
        isCompleted
      />

      {/* 空状态 */}
      {tasks.length === 0 && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '48px 16px',
            textAlign: 'center',
            backgroundColor: '#EDEDED',
          }}
        >
          <IonIcon
            icon={checkmarkCircleOutline}
            style={{ fontSize: '48px', color: '#CCCCCC', marginBottom: '16px' }}
          />
          <p style={{ fontSize: '14px', color: '#888888', margin: 0 }}>
            暂无任务
          </p>
        </div>
      )}
    </div>
  )
})

interface TaskSectionProps {
  title: string
  count: number
  icon: string
  iconColor: string
  tasks: TaskInfo[]
  onTaskClick: (taskId: string) => void
  isCompleted?: boolean
  category: 'executing' | 'pending' | 'completed'
}

/**
 * 任务分组组件 - 微信列表样式
 */
const TaskSection = memo(
  ({
    title,
    count,
    icon,
    iconColor,
    tasks,
    onTaskClick,
    isCompleted = false,
    category,
  }: TaskSectionProps) => {
    // 直接使用统一store
    const {
      categoryExpanded,
      categoryShowAll,
      setCategoryExpanded,
      setCategoryShowAll,
    } = useTaskCenterStore()

    const isExpanded = categoryExpanded[category]
    const showAll = categoryShowAll[category]

    // 显示的任务列表
    const displayTasks = showAll ? tasks : tasks.slice(0, 3)
    const hasMore = tasks.length > 3

    if (tasks.length === 0) {
      return null
    }

    return (
      <div style={{ marginBottom: '8px' }}>
        {/* 微信风格分组标题 */}
        <button
          onClick={() => setCategoryExpanded(category, !isExpanded)}
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            backgroundColor: '#FFFFFF',
            border: 'none',
            borderTop: '0.5px solid #EEEEEE',
            borderBottom: '0.5px solid #EEEEEE',
            padding: '12px 16px',
            fontSize: '17px',
            fontWeight: '400',
            color: '#000000',
            cursor: 'pointer',
            fontFamily:
              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          }}
          onTouchStart={(e) => {
            e.currentTarget.style.backgroundColor = '#F5F5F5'
          }}
          onTouchEnd={(e) => {
            e.currentTarget.style.backgroundColor = '#FFFFFF'
          }}
          onMouseDown={(e) => {
            e.currentTarget.style.backgroundColor = '#F5F5F5'
          }}
          onMouseUp={(e) => {
            e.currentTarget.style.backgroundColor = '#FFFFFF'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#FFFFFF'
          }}
        >
          <IonIcon
            icon={icon}
            style={{
              fontSize: '20px',
              color: iconColor,
              marginRight: '12px',
            }}
          />
          <span style={{ flex: 1, textAlign: 'left' }}>{title}</span>
          <span
            style={{ fontSize: '14px', color: '#888888', marginRight: '12px' }}
          >
            {count}
          </span>
          <IonIcon
            icon={isExpanded ? chevronUpOutline : chevronDownOutline}
            style={{ fontSize: '16px', color: '#CCCCCC' }}
          />
        </button>

        {/* 任务列表 - 微信风格列表项 */}
        {isExpanded && (
          <div style={{ backgroundColor: '#FFFFFF' }}>
            {displayTasks.map((task, index) => (
              <TaskItem
                key={task.id}
                task={task}
                onClick={() => onTaskClick(task.sessionId)}
                isCompleted={isCompleted}
                isLastItem={index === displayTasks.length - 1 && !hasMore}
              />
            ))}

            {/* 查看全部/收起按钮 */}
            {hasMore && (
              <button
                onClick={() => setCategoryShowAll(category, !showAll)}
                style={{
                  width: '100%',
                  backgroundColor: '#FFFFFF',
                  border: 'none',
                  borderTop: '0.5px solid #EEEEEE',
                  padding: '12px 16px',
                  fontSize: '14px',
                  color: '#888888',
                  cursor: 'pointer',
                  textAlign: 'center',
                  fontFamily:
                    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                }}
                onTouchStart={(e) => {
                  e.currentTarget.style.backgroundColor = '#F5F5F5'
                }}
                onTouchEnd={(e) => {
                  e.currentTarget.style.backgroundColor = '#FFFFFF'
                }}
                onMouseDown={(e) => {
                  e.currentTarget.style.backgroundColor = '#F5F5F5'
                }}
                onMouseUp={(e) => {
                  e.currentTarget.style.backgroundColor = '#FFFFFF'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#FFFFFF'
                }}
              >
                {showAll ? `收起` : `查看全部${tasks.length}个任务`}
              </button>
            )}
          </div>
        )}
      </div>
    )
  },
)

interface TaskItemProps {
  task: TaskInfo
  onClick: () => void
  isCompleted?: boolean
  isLastItem?: boolean
}

/**
 * 任务项组件 - 微信聊天列表样式
 */
const TaskItem = memo(
  ({
    task,
    onClick,
    isCompleted = false,
    isLastItem = false,
  }: TaskItemProps) => {
    // 格式化时间显示
    const formatTime = (dateString: string) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`

      return date.toLocaleDateString('zh-CN', {
        month: 'numeric',
        day: 'numeric',
      })
    }

    // 获取状态显示文本
    const getStatusText = () => {
      switch (task.status) {
        case 'executing':
          return `${task.progress || 0}%`
        case 'pending':
          return '等待中'
        case 'completed':
          return '已完成'
        default:
          return ''
      }
    }

    return (
      <div
        onClick={onClick}
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '12px 16px',
          backgroundColor: '#FFFFFF',
          borderBottom: isLastItem ? 'none' : '0.5px solid #EEEEEE',
          cursor: 'pointer',
          minHeight: '56px',
          fontFamily:
            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        }}
        onTouchStart={(e) => {
          e.currentTarget.style.backgroundColor = '#F5F5F5'
        }}
        onTouchEnd={(e) => {
          e.currentTarget.style.backgroundColor = '#FFFFFF'
        }}
        onMouseDown={(e) => {
          e.currentTarget.style.backgroundColor = '#F5F5F5'
        }}
        onMouseUp={(e) => {
          e.currentTarget.style.backgroundColor = '#FFFFFF'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = '#FFFFFF'
        }}
      >
        {/* 任务状态图标 */}
        <div
          style={{
            width: '40px',
            height: '40px',
            borderRadius: '4px',
            backgroundColor: isCompleted ? '#07C160' : '#F5F5F5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '12px',
            flexShrink: 0,
          }}
        >
          {task.status === 'executing' && (
            <div
              style={{
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                border: '2px solid #07C160',
                borderTopColor: 'transparent',
                animation: 'spin 1s linear infinite',
              }}
            />
          )}
          {task.status === 'pending' && (
            <IonIcon
              icon={pauseOutline}
              style={{ fontSize: '20px', color: '#888888' }}
            />
          )}
          {task.status === 'completed' && (
            <IonIcon
              icon={checkmarkCircleOutline}
              style={{ fontSize: '20px', color: '#FFFFFF' }}
            />
          )}
        </div>

        {/* 任务内容 */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '2px',
            }}
          >
            <span
              style={{
                fontSize: '17px',
                fontWeight: '400',
                color: '#000000',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                flex: 1,
                marginRight: '8px',
              }}
            >
              {task.title}
            </span>
            <span
              style={{
                fontSize: '12px',
                color: '#888888',
                flexShrink: 0,
              }}
            >
              {formatTime(task.updatedAt)}
            </span>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <span
              style={{
                fontSize: '13px',
                color: '#888888',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                flex: 1,
                marginRight: '8px',
              }}
            >
              {task.description || '无描述'}
            </span>
            {(task.status === 'executing' || task.status === 'pending') && (
              <span
                style={{
                  fontSize: '12px',
                  color: task.status === 'executing' ? '#07C160' : '#888888',
                  backgroundColor:
                    task.status === 'executing' ? '#F0F9FF' : '#F5F5F5',
                  padding: '2px 8px',
                  borderRadius: '10px',
                  flexShrink: 0,
                }}
              >
                {getStatusText()}
              </span>
            )}
          </div>
        </div>
      </div>
    )
  },
)

// 添加旋转动画
const style = document.createElement('style')
style.textContent = `
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`
document.head.appendChild(style)

TaskList.displayName = 'TaskList'
TaskSection.displayName = 'TaskSection'
TaskItem.displayName = 'TaskItem'
