import { memo, useEffect } from 'react'
import { menuController } from '@ionic/core/components'
import {
  IonContent,
  IonHeader,
  IonMenu,
  IonRefresher,
  IonRefresherContent,
  IonTitle,
  IonToolbar,
} from '@ionic/react'
import { useHistory } from 'react-router-dom'
import { ErrorMessage } from '../../components/error-message.tsx'
import { LoadingSkeleton } from '../../components/loading-skeleton.tsx'
import { useTaskCenterStore } from './store'
import './task-center.css'
import { TaskList } from './task-list.tsx'

interface TaskCenterProps {
  className?: string
}

/**
 * 任务中心组件
 * 包含任务列表、下拉刷新等功能
 */
export const TaskCenter = memo(({ className }: TaskCenterProps) => {
  const history = useHistory()

  // 使用统一的store
  const { tasks, isLoading, error, loadTasks, refreshTasks, shouldRefresh } =
    useTaskCenterStore()

  // 初始化加载任务数据
  useEffect(() => {
    if (shouldRefresh()) {
      loadTasks()
    }
  }, [shouldRefresh, loadTasks])

  // 侧滑菜单打开时刷新数据
  const handleMenuDidOpen = () => {
    console.log('侧滑菜单已打开，刷新任务列表')
    // 避免在已经加载中时重复调用
    if (!isLoading) {
      loadTasks()
    }
  }

  // 处理任务点击
  const handleTaskClick = async (taskId: string) => {
    history.push(`/task-detail/${taskId}`)
    await menuController.close('main-menu')
  }

  return (
    <IonMenu
      contentId='main-content'
      menuId='main-menu'
      type='overlay'
      onIonDidOpen={handleMenuDidOpen}
      className={`${className || ''} wechat-menu`}
      style={{ '--background': '#EDEDED' }}
    >
      <div
        className='relative flex h-full flex-col'
        style={{
          backgroundColor: '#EDEDED',
          fontFamily:
            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        }}
      >
        {/* 头部 - 微信风格导航栏 */}
        <IonHeader className='ion-no-border'>
          <IonToolbar
            style={{
              '--background': '#EDEDED',
              '--border-width': '0',
              '--padding-top': '8px',
              '--padding-bottom': '8px',
              height: '56px',
            }}
          >
            <IonTitle
              style={{
                color: '#000000',
                fontSize: '17px',
                fontWeight: '600',
                textAlign: 'center',
              }}
            >
              任务中心
            </IonTitle>
          </IonToolbar>
        </IonHeader>

        {/* 内容区域 */}
        <IonContent
          className='flex-1'
          style={{
            '--background': '#EDEDED',
            '--padding-top': '0',
            '--padding-bottom': '0',
          }}
        >
          {/* 下拉刷新 */}
          <IonRefresher
            slot='fixed'
            pullFactor={0.5}
            pullMin={100}
            pullMax={200}
            onIonRefresh={async (event) => {
              await refreshTasks()
              event.detail.complete()
            }}
          >
            <IonRefresherContent />
          </IonRefresher>

          {/* 任务列表内容 - 可滚动区域 */}
          {isLoading ? (
            <LoadingSkeleton count={5} type='list' />
          ) : error ? (
            <ErrorMessage message={error} onRetry={loadTasks} />
          ) : (
            <TaskList
              tasks={tasks}
              onTaskClick={handleTaskClick}
              isLoading={isLoading}
              error={error}
            />
          )}

          {/* 开发环境测试按钮
          {import.meta.env.DEV && (
              <IonButton
                fill='outline'
                expand='full'
                size='small'
                onClick={() => {
                  // 生成测试数据
                  const testTasks = [
                    {
                      id: `test-executing-${Date.now()}`,
                      title: '测试执行中任务',
                      description: '这是一个测试的执行中任务',
                      status: 'executing' as const,
                      progress: 45,
                      createdAt: new Date().toISOString(),
                      updatedAt: new Date().toISOString(),
                      sessionId: `test-session-${Date.now()}`,
                    },
                    {
                      id: `test-pending-${Date.now() + 1}`,
                      title: '测试待执行任务',
                      description: '这是一个测试的待执行任务',
                      status: 'pending' as const,
                      progress: 0,
                      createdAt: new Date().toISOString(),
                      updatedAt: new Date().toISOString(),
                      sessionId: `test-session-pending-${Date.now()}`,
                    },
                    {
                      id: `test-completed-${Date.now() + 2}`,
                      title: '测试已完成任务',
                      description: '这是一个测试的已完成任务',
                      status: 'completed' as const,
                      progress: 100,
                      createdAt: new Date().toISOString(),
                      updatedAt: new Date().toISOString(),
                      sessionId: `test-session-completed-${Date.now()}`,
                    },
                  ]
                  addTestData(testTasks)
                }}
                style={{
                  '--border-color': '#07C160',
                  '--color': '#07C160',
                  fontSize: '14px',
                  fontWeight: '400'
                }}
              >
                添加测试数据
              </IonButton>
            )} */}
        </IonContent>
      </div>
    </IonMenu>
  )
})

TaskCenter.displayName = 'TaskCenter'
