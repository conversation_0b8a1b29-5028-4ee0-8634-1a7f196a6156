import { getUserSessions, type SessionInfo } from '@/tina/lib/session.ts'
import { useAuthStore } from '@/tina/stores/authStore.ts'

// 任务状态映射
export interface TaskInfo {
  id: string
  title: string
  description: string
  status: 'pending' | 'executing' | 'completed'
  progress: number
  createdAt: string
  updatedAt: string
  sessionId: string
}

// 缓存键
const CACHE_KEY = 'mobile_task_cache'
const CACHE_EXPIRY_KEY = 'mobile_task_cache_expiry'
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

/**
 * 将 SessionInfo 转换为 TaskInfo
 */
function sessionToTask(session: SessionInfo): TaskInfo {
  // 状态映射
  let mappedStatus: 'pending' | 'executing' | 'completed'
  switch (session.status) {
    case 'init':
    case 'plan':
      mappedStatus = 'pending'
      break
    case 'tools_call':
      mappedStatus = 'executing'
      break
    case 'task_completed':
      mappedStatus = 'completed'
      break
    default:
      mappedStatus = 'pending'
  }

  // 进度计算
  let progress = 0
  try {
    if (session.step && session.max_step && session.max_step > 0) {
      progress = Math.round((session.step / session.max_step) * 100)
      // 确保进度在 0-100 范围内
      progress = Math.max(0, Math.min(100, progress))
    }
  } catch {
    // 如果有异常则按进度0展示
    progress = 0
  }

  return {
    id: session.session_id,
    title: session.content?.slice(0, 30) || '新任务',
    description: session.content || '暂无描述',
    status: mappedStatus,
    progress,
    createdAt: session.created_at,
    updatedAt: session.updated_at,
    sessionId: session.session_id,
  }
}

/**
 * 从本地存储获取缓存的任务数据
 */
function getCachedTasks(): TaskInfo[] | null {
  try {
    const cached = localStorage.getItem(CACHE_KEY)
    const expiry = localStorage.getItem(CACHE_EXPIRY_KEY)

    if (!cached || !expiry) {
      return null
    }

    const expiryTime = parseInt(expiry, 10)
    if (Date.now() > expiryTime) {
      // 缓存已过期
      clearTaskCache()
      return null
    }

    return JSON.parse(cached)
  } catch (error) {
    console.error('读取任务缓存失败:', error)
    return null
  }
}

/**
 * 将任务数据保存到本地存储
 */
function setCachedTasks(tasks: TaskInfo[]): void {
  try {
    const expiry = Date.now() + CACHE_DURATION
    localStorage.setItem(CACHE_KEY, JSON.stringify(tasks))
    localStorage.setItem(CACHE_EXPIRY_KEY, expiry.toString())
  } catch (error) {
    console.error('保存任务缓存失败:', error)
  }
}

/**
 * 从服务器获取任务数据
 */
async function fetchTasksFromServer(): Promise<TaskInfo[]> {
  const auth = useAuthStore.getState().auth
  const token = auth.token

  if (!token) {
    throw new Error('用户未登录')
  }

  try {
    const response = await getUserSessions(token)
    const sessions = response.data.data.sessions || []

    // 转换为任务格式
    const tasks = sessions.map(sessionToTask)

    // 缓存数据
    setCachedTasks(tasks)

    return tasks
  } catch (error) {
    console.error('获取任务数据失败:', error)
    throw error
  }
}

/**
 * 获取任务数据（优先从缓存读取）
 */
export async function getTasks(): Promise<TaskInfo[]> {
  // 首先尝试从缓存获取
  const cachedTasks = getCachedTasks()
  if (cachedTasks) {
    return cachedTasks
  }

  // 缓存不存在或已过期，从服务器获取
  return fetchTasksFromServer()
}

/**
 * 刷新任务数据（强制从服务器获取）
 */
export async function refreshTasks(): Promise<TaskInfo[]> {
  return fetchTasksFromServer()
}

/**
 * 清除任务缓存
 */
export function clearTaskCache(): void {
  localStorage.removeItem(CACHE_KEY)
  localStorage.removeItem(CACHE_EXPIRY_KEY)
}
