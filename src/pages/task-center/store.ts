import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { getTasks, refreshTasks, type TaskInfo } from './task-service'

interface TaskCenterState {
  // 任务数据
  tasks: TaskInfo[]
  isLoading: boolean
  error: string | null
  lastUpdated: number | null

  // UI状态
  categoryExpanded: {
    executing: boolean
    pending: boolean
    completed: boolean
  }
  categoryShowAll: {
    executing: boolean
    pending: boolean
    completed: boolean
  }

  // 数据操作
  setTasks: (tasks: TaskInfo[]) => void
  addTask: (task: TaskInfo) => void
  addTasks: (tasks: TaskInfo[]) => void
  updateTask: (taskId: string, updates: Partial<TaskInfo>) => void
  removeTask: (taskId: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearTasks: () => void

  // UI操作
  setCategoryExpanded: (
    category: 'executing' | 'pending' | 'completed',
    expanded: boolean,
  ) => void
  setCategoryShowAll: (
    category: 'executing' | 'pending' | 'completed',
    showAll: boolean,
  ) => void

  // 业务逻辑
  loadTasks: () => Promise<void>
  refreshTasks: () => Promise<void>
  shouldRefresh: () => boolean
}

const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

export const useTaskCenterStore = create<TaskCenterState>()(
  persist(
    (set, get) => ({
      // 初始数据状态
      tasks: [],
      isLoading: false,
      error: null,
      lastUpdated: null,

      // 初始UI状态
      categoryExpanded: {
        executing: true,
        pending: true,
        completed: true,
      },
      categoryShowAll: {
        executing: false,
        pending: false,
        completed: false,
      },

      // 数据操作方法
      setTasks: (tasks) => set({ tasks, lastUpdated: Date.now(), error: null }),
      addTask: (task) =>
        set((state) => ({
          tasks: [...state.tasks, task],
          lastUpdated: Date.now(),
        })),
      addTasks: (newTasks) =>
        set((state) => ({
          tasks: [...state.tasks, ...newTasks],
          lastUpdated: Date.now(),
        })),
      updateTask: (taskId, updates) =>
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === taskId ? { ...task, ...updates } : task,
          ),
          lastUpdated: Date.now(),
        })),
      removeTask: (taskId) =>
        set((state) => ({
          tasks: state.tasks.filter((task) => task.id !== taskId),
          lastUpdated: Date.now(),
        })),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearTasks: () => set({ tasks: [], lastUpdated: null, error: null }),

      // UI操作方法
      setCategoryExpanded: (category, expanded) =>
        set((state) => ({
          categoryExpanded: {
            ...state.categoryExpanded,
            [category]: expanded,
          },
        })),
      setCategoryShowAll: (category, showAll) =>
        set((state) => ({
          categoryShowAll: {
            ...state.categoryShowAll,
            [category]: showAll,
          },
        })),

      // 业务逻辑方法
      loadTasks: async () => {
        const state = get()
        if (state.isLoading) return

        try {
          set({ isLoading: true, error: null })
          const taskData = await getTasks()
          set({ tasks: taskData, lastUpdated: Date.now(), isLoading: false })
        } catch (error) {
          console.error('加载任务失败:', error)
          set({
            error: error instanceof Error ? error.message : '加载任务失败',
            isLoading: false,
          })
        }
      },

      refreshTasks: async () => {
        try {
          set({ error: null })
          const taskData = await refreshTasks()
          set({ tasks: taskData, lastUpdated: Date.now() })
          // 延迟返回，防止UI闪动
          await new Promise((resolve) => setTimeout(resolve, 1000))
        } catch (error) {
          console.error('刷新任务失败:', error)
          set({
            error: error instanceof Error ? error.message : '刷新任务失败',
          })
        }
      },

      shouldRefresh: () => {
        const { lastUpdated } = get()
        if (!lastUpdated) return true
        return Date.now() - lastUpdated > CACHE_DURATION
      },
    }),
    {
      name: 'task-center-store',
      partialize: (state) => ({
        tasks: state.tasks,
        lastUpdated: state.lastUpdated,
        categoryExpanded: state.categoryExpanded,
        categoryShowAll: state.categoryShowAll,
      }),
    },
  ),
)
