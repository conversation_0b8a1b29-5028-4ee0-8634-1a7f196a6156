/* WeChat风格的隐藏滚动条样式 */
.wechat-menu .scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.wechat-menu .scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 任务列表容器隐藏滚动条 */
.task-list-container {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.task-list-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* IonContent内部的滚动容器 */
.wechat-menu ion-content .inner-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.wechat-menu ion-content .inner-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 确保滚动功能正常工作 */
.wechat-menu ion-content {
  overflow: hidden;
}

.wechat-menu .task-content-area {
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.wechat-menu .task-content-area::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
