import React from 'react'
import {
  IonButton,
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
} from '@ionic/react'
import {
  showConfirmationDialog,
  createMockConfirmationMessage,
} from '@/utils/confirmationDialog'
import ConfirmationDialogManager from '@/components/ConfirmationDialogManager'

const TestConfirmationDialog: React.FC = () => {
  // 模拟 change_plan 消息
  const testChangePlan = () => {
    const mockMessage = createMockConfirmationMessage(
      'change_plan',
      '需要修改当前计划，是否同意？',
      '25ebd741-6cdc-4c79-a8b8-f1d79c3f1cfa',
    )
    showConfirmationDialog(mockMessage, false)
  }

  // 模拟 ask_user_info 消息
  const testAskUserInfo = () => {
    const mockMessage = createMockConfirmationMessage(
      'ask_user_info',
      '请提供您的联系方式',
      '25ebd741-6cdc-4c79-a8b8-f1d79c3f1cfa',
    )
    showConfirmationDialog(mockMessage, true)
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>测试确认对话框</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className='ion-padding'>
        <div className='space-y-4'>
          <h2>确认对话框测试</h2>

          <div className='space-y-2'>
            <IonButton expand='block' onClick={testChangePlan}>
              测试计划变更确认 (change_plan)
            </IonButton>

            <IonButton expand='block' onClick={testAskUserInfo}>
              测试用户信息询问 (ask_user_info)
            </IonButton>
          </div>

          <div className='mt-8'>
            <h3>说明:</h3>
            <ul className='list-disc space-y-1 pl-6'>
              <li>change_plan: 显示简单确认对话框，不需要用户输入</li>
              <li>ask_user_info: 显示带输入框的对话框，需要用户输入</li>
              <li>确认或取消操作会在控制台输出日志</li>
              <li>这些功能已经集成到 WeChat 对话系统中</li>
            </ul>
          </div>

          <div className='mt-6 rounded-lg bg-blue-50 p-4'>
            <h4 className='mb-2 font-medium text-blue-900'>新架构说明:</h4>
            <p className='text-sm text-blue-700'>
              确认对话框功能现在使用独立的管理器组件，通过全局事件系统工作。
              这样的设计更加模块化，不会污染 context，便于维护和扩展。
            </p>
          </div>

          <div className='mt-4 rounded-lg bg-green-50 p-4'>
            <h4 className='mb-2 font-medium text-green-900'>架构优势:</h4>
            <ul className='list-disc space-y-1 pl-6 text-sm text-green-700'>
              <li>独立的确认对话框管理器，不依赖 context</li>
              <li>通过全局事件系统通信，解耦合</li>
              <li>可以在任何地方触发确认对话框</li>
              <li>便于测试和维护</li>
            </ul>
          </div>
        </div>

        {/* 独立的确认对话框管理器 */}
        <ConfirmationDialogManager />
      </IonContent>
    </IonPage>
  )
}

export default TestConfirmationDialog
