import type { SessionInfo } from '@/tina/lib/session'
import type { TinaTaskStep } from '@/tina/lib/xml/xmlParser'
import { create } from 'zustand'
import { TaskDataService } from './services/taskDataService'

interface TaskPanelState {
  // 数据状态
  runningTasks: SessionInfo[] // 所有执行中的任务
  currentTask: SessionInfo | null // 当前显示的任务
  isExpanded: boolean
  isLoading: boolean

  // 数据操作
  setRunningTasks: (tasks: SessionInfo[]) => void
  setCurrentTask: (task: SessionInfo | null) => void
  setExpanded: (expanded: boolean) => void
  setLoading: (loading: boolean) => void

  // 业务逻辑
  resetTasks: () => void

  // 真实数据操作
  loadTaskData: (token: string) => Promise<void>
  refreshTaskData: (token: string) => Promise<void>
  updateTaskProgress: (toolsCallData: any) => void

  // 计算属性
  getCurrentStep: () => TinaTaskStep | null
  getCompletedCount: () => number
  getIsAllCompleted: () => boolean
}

export const useTaskPanelStore = create<TaskPanelState>((set, get) => ({
  // 初始状态
  runningTasks: [],
  currentTask: null,
  isExpanded: false,
  isLoading: false,

  // 数据操作
  setRunningTasks: (tasks) => {
    const { runningTasks } = get()
    const needsUpdate = JSON.stringify(runningTasks) !== JSON.stringify(tasks)

    console.log('🔄 [TaskPanelStore] setRunningTasks 检查更新:', {
      needsUpdate,
      currentCount: runningTasks.length,
      newCount: tasks.length,
      currentTasks: runningTasks.map((t) => ({
        id: t.session_id,
        step: t.step,
        status: t.status,
      })),
      newTasks: tasks.map((t) => ({
        id: t.session_id,
        step: t.step,
        status: t.status,
      })),
    })

    if (needsUpdate) {
      set({ runningTasks: tasks })
      console.log('✅ [TaskPanelStore] runningTasks 已更新')
    } else {
      console.log('⏭️ [TaskPanelStore] runningTasks 无需更新')
    }
  },
  setCurrentTask: (task) => {
    const { currentTask } = get()
    // 检查是否需要更新：session_id不同，或者相同session_id但其他属性发生变化
    const needsUpdate =
      currentTask?.session_id !== task?.session_id ||
      currentTask?.step !== task?.step ||
      currentTask?.status !== task?.status ||
      currentTask?.updated_at !== task?.updated_at

    console.log('🔄 [TaskPanelStore] setCurrentTask 检查更新:', {
      needsUpdate,
      currentTask: currentTask
        ? {
            session_id: currentTask.session_id,
            step: currentTask.step,
            status: currentTask.status,
            updated_at: currentTask.updated_at,
          }
        : null,
      newTask: task
        ? {
            session_id: task.session_id,
            step: task.step,
            status: task.status,
            updated_at: task.updated_at,
          }
        : null,
    })

    if (needsUpdate) {
      set({ currentTask: task })
      console.log('✅ [TaskPanelStore] currentTask 已更新')
    } else {
      console.log('⏭️ [TaskPanelStore] currentTask 无需更新')
    }
  },
  setExpanded: (expanded) => {
    const { isExpanded } = get()
    if (isExpanded !== expanded) {
      set({ isExpanded: expanded })
    }
  },
  setLoading: (loading) => {
    const { isLoading } = get()
    if (isLoading !== loading) {
      set({ isLoading: loading })
    }
  },

  // 业务逻辑
  resetTasks: () => {
    set({
      runningTasks: [],
      currentTask: null,
    })
  },

  // 真实数据操作
  loadTaskData: async (token: string) => {
    set({ isLoading: true })
    try {
      const tasks = await TaskDataService.getRunningTasks(token)
      const currentTask = tasks.length > 0 ? tasks[0] : null
      set({
        runningTasks: tasks,
        currentTask,
        isLoading: false,
      })
    } catch (error) {
      console.error('加载任务数据失败:', error)
      set({
        runningTasks: [],
        currentTask: null,
        isLoading: false,
      })
    }
  },

  refreshTaskData: async (token: string) => {
    const { loadTaskData } = get()
    await loadTaskData(token)
  },

  updateTaskProgress: (toolsCallData: any) => {
    const { currentTask } = get()
    if (!currentTask) return

    const updatedTask = TaskDataService.updateSessionProgress(
      currentTask,
      toolsCallData,
    )
    set({ currentTask: updatedTask })
  },

  // 计算属性
  getCurrentStep: () => {
    const { currentTask } = get()
    if (!currentTask?.tinaTask?.steps || !currentTask.step) return null

    const stepIndex = currentTask.step - 1
    if (stepIndex >= 0 && stepIndex < currentTask.tinaTask.steps.length) {
      return currentTask.tinaTask.steps[stepIndex]
    }
    return null
  },

  getCompletedCount: () => {
    const { currentTask } = get()
    if (!currentTask?.tinaTask?.steps) return 0

    // 如果任务已完成，返回总步骤数
    if (currentTask.status === 'task_completed') {
      return currentTask.tinaTask.steps.length
    }

    // 否则，当前步骤之前的都算完成
    if (!currentTask.step) return 0
    return Math.max(0, (currentTask.step || 1) - 1)
  },

  getIsAllCompleted: () => {
    const { currentTask } = get()
    if (!currentTask?.tinaTask?.steps) return false

    return (
      currentTask.status === 'task_completed' ||
      (currentTask.step || 1) > currentTask.tinaTask.steps.length
    )
  },
}))
