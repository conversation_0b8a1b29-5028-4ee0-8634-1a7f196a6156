import { forwardRef, useImperativeHandle } from 'react'
import { ChevronUpIcon, ChevronDownIcon } from './icons'
import { useTaskPanelStore } from './store'
import { TaskItem } from './task-item'
import type { TaskPanelHandle } from './types'

interface TaskPanelProps {
  slot?: string
}

export const TaskPanel = forwardRef<TaskPanelHandle, TaskPanelProps>(
  (props, ref) => {
    const {
      currentTask,
      isExpanded,
      setExpanded,
      getCurrentStep,
      getCompletedCount,
      getIsAllCompleted,
    } = useTaskPanelStore()

    useImperativeHandle(ref, () => ({
      expand: () => setExpanded(true),
      collapse: () => setExpanded(false),
    }))

    if (!currentTask) return null

    const currentStep = getCurrentStep()
    const completedCount = getCompletedCount()
    const isAllCompleted = getIsAllCompleted()

    // 调试日志
    // console.log('🎯 [TaskPanel] 渲染状态:', {
    //   session_id: currentTask.session_id,
    //   step: currentTask.step,
    //   status: currentTask.status,
    //   stepsCount: currentTask.tinaTask?.steps?.length,
    //   completedCount,
    //   isAllCompleted,
    //   currentStepTitle: currentStep?.title,
    // })

    const CollapsedView = () => (
      <div
        className='flex h-[40px] cursor-pointer select-none items-center px-4'
        onClick={() => setExpanded(true)}
      >
        {isAllCompleted ? (
          <span className='truncate text-sm text-gray-800'>任务已全部完成</span>
        ) : currentStep ? (
          <>
            <div className='mr-3 h-3.5 w-3.5 flex-shrink-0 animate-spin rounded-full border-2 border-blue-500 border-t-transparent'></div>
            <span className='truncate text-sm text-gray-800'>
              {currentStep.title ||
                currentStep.tools_description ||
                '执行中...'}
            </span>
          </>
        ) : (
          <span className='truncate text-sm text-gray-800'>准备开始...</span>
        )}
        <div className='ml-auto flex-shrink-0 pl-2 text-gray-400'>
          <ChevronDownIcon />
        </div>
      </div>
    )

    const ExpandedView = () => (
      <>
        <div
          className='flex h-[40px] cursor-pointer select-none items-center border-b border-gray-200 px-4'
          onClick={() => setExpanded(false)}
        >
          <span className='font-semibold text-gray-800'>计划</span>
          <span className='ml-auto text-sm text-gray-500'>
            {completedCount} / {currentTask.tinaTask?.steps.length || 0}
          </span>
          <div className='ml-2 text-gray-400'>
            <ChevronUpIcon />
          </div>
        </div>
        <div className='max-h-[calc(70vh-40px)] overflow-y-auto'>
          {currentTask.tinaTask?.steps.map((step, index) => (
            <TaskItem
              key={`step-${step.index || index + 1}`}
              step={step}
              stepIndex={index}
              currentStep={currentTask.step || 1}
              status={currentTask.status || 'init'}
            />
          ))}
        </div>
      </>
    )

    return (
      <div
        slot={props.slot}
        className={`left-0 right-0 top-0 z-10 overflow-hidden border-b border-gray-200 bg-gray-100 transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-[70vh]' : 'max-h-[40px]'
        }`}
      >
        {isExpanded ? <ExpandedView /> : <CollapsedView />}
      </div>
    )
  },
)

TaskPanel.displayName = 'TaskPanel'
