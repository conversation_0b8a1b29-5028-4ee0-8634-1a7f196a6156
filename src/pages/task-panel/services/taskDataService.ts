import { getUserSessionsRunning, type SessionInfo } from '@/tina/lib/session'
import { parseXmlTinaTask } from '@/tina/lib/xml/xmlParser'

/**
 * 任务数据服务
 * 负责从Session API获取任务数据并解析XML内容
 */
export class TaskDataService {
  /**
   * 获取当前用户的执行中任务列表，并解析任务数据
   * @param token 用户认证token
   * @returns 执行中的任务列表，已过滤plan_content为空的任务，并包含解析后的tinaTask数据
   */
  static async getRunningTasks(token: string): Promise<SessionInfo[]> {
    try {
      const response = await getUserSessionsRunning(token)
      if (!response.data?.success || !response.data?.data?.sessions) {
        console.warn('获取执行中任务失败或数据为空')
        return []
      }

      const sessions = response.data.data.sessions

      // 过滤条件：只保留 plan_content 字段不为空的任务
      const filteredSessions = sessions.filter(
        (session) =>
          session.plan_content && session.plan_content.trim().length > 0,
      )

      // 解析每个任务的XML内容
      const sessionsWithParsedTasks = filteredSessions.map((session) => {
        try {
          const tinaTask = parseXmlTinaTask(session.plan_content)
          return {
            ...session,
            tinaTask: tinaTask || undefined,
          }
        } catch (error) {
          console.error(
            `解析任务XML失败 (session: ${session.session_id}):`,
            error,
          )
          return session
        }
      })

      // 只返回成功解析的任务
      const validTasks = sessionsWithParsedTasks.filter(
        (session) =>
          session.tinaTask &&
          session.tinaTask.steps &&
          session.tinaTask.steps.length > 0,
      )

      console.log(
        `获取到 ${sessions.length} 个会话，过滤后 ${filteredSessions.length} 个有效任务，成功解析 ${validTasks.length} 个任务`,
      )
      return validTasks
    } catch (error) {
      console.error('获取执行中任务失败:', error)
      return []
    }
  }

  /**
   * 根据tools_call消息更新任务进度
   * @param session 当前任务会话
   * @param toolsCallData tools_call数据
   * @returns 更新后的会话信息
   */
  static updateSessionProgress(
    session: SessionInfo,
    toolsCallData: any,
  ): SessionInfo {
    if (!toolsCallData?.step || !session.tinaTask) {
      return session
    }

    // 更新session的step信息
    return {
      ...session,
      step: toolsCallData.step,
      status: 'tools_call' as const,
    }
  }
}
