import { useCallback, useRef } from 'react'
import { useTaskPanelStore } from '../store'

/**
 * 任务面板滚动处理Hook
 * 优化滚动时的折叠逻辑，避免重复刷新
 */
export const useTaskPanelScroll = () => {
  const { currentTask, isExpanded } = useTaskPanelStore()
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isScrollingRef = useRef(false)

  const handleScroll = useCallback(() => {
    // 只有当任务存在且面板展开时才处理滚动
    if (!currentTask || !isExpanded) {
      return
    }

    // 如果已经在处理滚动，直接返回
    if (isScrollingRef.current) {
      return
    }

    // 标记正在处理滚动
    isScrollingRef.current = true

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 延迟折叠，避免频繁操作
    scrollTimeoutRef.current = setTimeout(() => {
      // 直接调用store方法，避免依赖引用
      const store = useTaskPanelStore.getState()
      if (store.isExpanded) {
        store.setExpanded(false)
      }
      isScrollingRef.current = false
    }, 150) // 增加延迟时间，减少频繁触发
  }, [currentTask, isExpanded]) // 移除 setExpanded 依赖

  // 清理函数
  const cleanup = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
      scrollTimeoutRef.current = null
    }
    isScrollingRef.current = false
  }, [])

  return {
    handleScroll,
    cleanup,
  }
}
