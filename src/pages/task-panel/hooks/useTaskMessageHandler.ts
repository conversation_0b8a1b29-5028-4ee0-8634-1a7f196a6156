import { useCallback } from 'react'
import type { TConversationItem } from '@/stateV2/conversation'
import { EConversationType } from '@/stateV2/conversation'
import type {
  StreamMessage,
  TaskMetadata,
} from '@/tina/lib/EmotionMindClient.browser'
import type { SessionInfo } from '@/tina/lib/session'
import { parseXmlTinaTask } from '@/tina/lib/xml/xmlParser'
import { nanoid } from 'nanoid'
import type { Descendant } from 'slate'
import { showConfirmationDialog } from '@/utils/confirmationDialog'
import { useTaskPanelStore } from '../store'

/**
 * 任务消息处理Hook
 * 处理来自 context.tsx 的 handleTaskResponse 的任务消息
 * 支持新任务创建和任务状态更新
 */
export const useTaskMessageHandler = (
  addMessageToChat?: (message: TConversationItem) => void,
) => {
  const { runningTasks, setRunningTasks, setCurrentTask } = useTaskPanelStore()

  /**
   * 从 TaskMetadata 创建 SessionInfo
   */
  const createSessionFromTaskMetadata = useCallback(
    (metadata: TaskMetadata): SessionInfo | null => {
      try {
        // 解析 XML 内容
        const tinaTask = parseXmlTinaTask(metadata.content)
        if (!tinaTask) {
          console.warn('无法解析任务XML内容:', metadata.content)
          return null
        }

        // 创建 SessionInfo 对象
        const sessionInfo: SessionInfo = {
          session_id: metadata.session_id,
          content: '', // 任务消息通常没有普通内容
          plan_content: metadata.content, // XML 内容存储在 plan_content 中
          created_at: new Date(metadata.timestamp * 1000).toISOString(),
          updated_at: new Date(metadata.timestamp * 1000).toISOString(),
          status: metadata.xml_type as SessionInfo['status'],
          step: metadata.step > 0 ? metadata.step : undefined,
          max_step: tinaTask.steps?.length || undefined,
          tinaTask: tinaTask, // 解析后的任务数据
        }

        console.log('✅ 成功创建SessionInfo:', {
          session_id: sessionInfo.session_id,
          status: sessionInfo.status,
          step: sessionInfo.step,
          stepsCount: tinaTask.steps?.length,
        })

        return sessionInfo
      } catch (error) {
        console.error('❌ 创建SessionInfo失败:', error)
        return null
      }
    },
    [],
  )

  /**
   * 处理新任务创建消息
   */
  const handleNewTaskMessage = useCallback((sessionInfo: SessionInfo) => {
    console.log('🆕 处理新任务创建:', {
      session_id: sessionInfo.session_id,
      type: sessionInfo.status,
      stepsCount: sessionInfo.tinaTask?.steps?.length,
    })

    // 获取最新的任务列表状态
    const store = useTaskPanelStore.getState()
    const currentTasks = store.runningTasks

    // 检查是否已存在相同的任务
    const existingTaskIndex = currentTasks.findIndex(
      (task) => task.session_id === sessionInfo.session_id,
    )

    let updatedTasks: SessionInfo[]

    if (existingTaskIndex >= 0) {
      // 更新现有任务
      updatedTasks = [...currentTasks]
      updatedTasks[existingTaskIndex] = sessionInfo
      console.log('🔄 更新现有任务:', sessionInfo.session_id)
    } else {
      // 添加新任务到列表开头（最新的任务在前面）
      updatedTasks = [sessionInfo, ...currentTasks]
      console.log('➕ 添加新任务到列表:', sessionInfo.session_id)
    }

    // 更新任务列表
    store.setRunningTasks(updatedTasks)

    // 将新任务设为当前任务
    store.setCurrentTask(sessionInfo)

    console.log('✅ 任务面板已更新，当前任务:', sessionInfo.session_id)
  }, [])

  /**
   * 处理任务状态更新消息
   */
  const handleTaskUpdateMessage = useCallback(
    (sessionInfo: SessionInfo) => {
      console.log('🔄 处理任务状态更新:', {
        session_id: sessionInfo.session_id,
        status: sessionInfo.status,
        step: sessionInfo.step,
      })

      // 获取最新的任务列表状态
      const store = useTaskPanelStore.getState()
      const currentTasks = store.runningTasks

      // 查找并更新对应的任务
      const taskIndex = currentTasks.findIndex(
        (task) => task.session_id === sessionInfo.session_id,
      )

      if (taskIndex >= 0) {
        const updatedTasks = [...currentTasks]
        updatedTasks[taskIndex] = sessionInfo
        store.setRunningTasks(updatedTasks)
        store.setCurrentTask(sessionInfo)

        console.log('✅ 任务状态已更新:', sessionInfo.session_id)
      } else {
        console.warn('⚠️ 未找到要更新的任务:', sessionInfo.session_id)
        // 如果找不到任务，可能是新任务，按新任务处理
        handleNewTaskMessage(sessionInfo)
      }
    },
    [handleNewTaskMessage],
  )

  /**
   * 处理任务执行进度更新消息 (tools_call)
   */
  const handleTaskProgressUpdate = useCallback(
    (sessionInfo: SessionInfo, step: number) => {
      console.log('🔄 处理任务执行进度更新:', {
        session_id: sessionInfo.session_id,
        step: step,
        tool_name: sessionInfo.tinaTask?.tools_call?.tool_name,
      })

      // 获取最新的任务列表状态
      const store = useTaskPanelStore.getState()
      const currentTasks = store.runningTasks

      // 查找对应的任务
      const taskIndex = currentTasks.findIndex(
        (task) => task.session_id === sessionInfo.session_id,
      )

      if (taskIndex >= 0) {
        const updatedTasks = [...currentTasks]
        // 更新任务状态和步骤
        updatedTasks[taskIndex] = {
          ...updatedTasks[taskIndex],
          status: 'tools_call' as const,
          step: step,
          updated_at: sessionInfo.updated_at,
          // 保留原有的tinaTask（计划信息），只更新执行状态
          tinaTask: {
            ...updatedTasks[taskIndex].tinaTask!,
            tools_call: sessionInfo.tinaTask?.tools_call,
          },
        }

        store.setRunningTasks(updatedTasks)
        store.setCurrentTask(updatedTasks[taskIndex])

        console.log('✅ 任务执行进度已更新:', {
          session_id: sessionInfo.session_id,
          step: step,
          tool: sessionInfo.tinaTask?.tools_call?.tool_name,
        })
      } else {
        console.warn('⚠️ 未找到要更新进度的任务:', sessionInfo.session_id)
        console.warn(
          '当前任务列表:',
          store.runningTasks.map((t) => t.session_id),
        )
      }
    },
    [],
  )

  /**
   * 处理任务完成消息 (task_completed)
   */
  const handleTaskCompletion = useCallback((session_id: string) => {
    console.log('🎉 处理任务完成:', {
      session_id: session_id,
    })

    // 获取最新的任务列表状态
    const store = useTaskPanelStore.getState()
    const currentTasks = store.runningTasks

    // 查找对应的任务
    const taskIndex = currentTasks.findIndex(
      (task) => task.session_id === session_id,
    )

    if (taskIndex >= 0) {
      const updatedTasks = [...currentTasks]
      const currentTaskData = updatedTasks[taskIndex]

      // 计算正确的步骤数：使用 max_step 或 tinaTask.steps.length
      const totalSteps =
        currentTaskData.max_step || currentTaskData.tinaTask?.steps?.length || 0

      // 更新任务为完成状态
      updatedTasks[taskIndex] = {
        ...currentTaskData,
        status: 'task_completed' as const,
        step: totalSteps, // 设置为总步骤数
        tinaTask: {
          ...currentTaskData.tinaTask!,
          completed: true,
        },
      }

      store.setRunningTasks(updatedTasks)
      store.setCurrentTask(updatedTasks[taskIndex])

      console.log('🎊 任务已标记为完成:', session_id)
    } else {
      console.warn('⚠️ 未找到要完成的任务:', session_id)
      console.warn(
        '当前任务列表:',
        store.runningTasks.map((t) => t.session_id),
      )
    }
  }, [])

  const sendTaskCompletedToChat = useCallback(
    (message: StreamMessage) => {
      // 发送任务完成消息到聊天
      if (addMessageToChat) {
        try {
          const textContent: Descendant[] = [
            { text: message.content || '任务已完成' },
          ]

          const friendMessage: TConversationItem = {
            type: EConversationType.text,
            role: 'friend',
            textContent,
            id: `task-completed-${nanoid(8)}`,
            timestamp: message.timestamp,
            // upperText 会在添加到列表时由 context 处理
          }
          addMessageToChat(friendMessage)
          console.log('📤 任务完成消息已添加到聊天:', friendMessage)
        } catch (error) {
          console.error('❌ 添加任务完成消息失败:', error)
        }
      }
    },
    [addMessageToChat],
  )

  /**
   * 主要的任务消息处理函数
   * 由 context.tsx 的 handleTaskResponse 调用
   */
  const handleTaskMessage = useCallback(
    (message: StreamMessage) => {
      console.log('📨 收到任务消息:', {
        type: message.type,
        tool_name: message.tool_name,
        metadata: message.metadata,
      })

      // 检查消息类型和工具名称
      if (
        message.type !== 'task' ||
        message.tool_name !== 'tina_task_query_status'
      ) {
        console.log('⏭️ 跳过非任务消息:', message.type, message.tool_name)
        return
      }

      // 检查 metadata
      if (!message.metadata) {
        console.warn('⚠️ 任务消息缺少metadata')
        return
      }

      const metadata = message.metadata as TaskMetadata

      // 验证必要字段
      if (!metadata.session_id || !metadata.content || !metadata.xml_type) {
        console.warn('⚠️ 任务metadata缺少必要字段:', metadata)
        return
      }

      // 处理需要用户确认的消息类型
      if (metadata.xml_type === 'change_plan') {
        console.log('📋 收到计划变更确认消息:', metadata.content)
        showConfirmationDialog(message, false) // 不需要用户输入
        return
      }

      if (metadata.xml_type === 'ask_user_info') {
        console.log('💬 收到用户信息询问消息:', metadata.content)
        showConfirmationDialog(message, true) // 需要用户输入
        return
      }

      // 非 tina task 格式
      if (metadata.xml_type === 'task_completed') {
        // 任务完成消息
        handleTaskCompletion(metadata.session_id)
        sendTaskCompletedToChat(message)
        return
      }

      // 创建 SessionInfo
      const sessionInfo = createSessionFromTaskMetadata(metadata)
      if (!sessionInfo) {
        return
      }

      // 根据消息类型和步骤判断是新任务还是更新
      if (metadata.xml_type === 'plan' && metadata.step === -1) {
        // 新任务创建消息
        handleNewTaskMessage(sessionInfo)
      } else if (metadata.xml_type === 'tools_call') {
        // 任务执行进度更新消息
        handleTaskProgressUpdate(sessionInfo, metadata.step)
      } else {
        // 其他类型的任务更新消息
        handleTaskUpdateMessage(sessionInfo)
      }
    },
    [
      createSessionFromTaskMetadata,
      handleNewTaskMessage,
      handleTaskProgressUpdate,
      handleTaskCompletion,
      handleTaskUpdateMessage,
    ],
  )

  return {
    // 返回处理函数供 context.tsx 使用
    handleTaskMessage,
    // 返回一些状态供调试使用
    runningTasksCount: runningTasks.length,
  }
}
