import { useEffect, useRef } from 'react'
import useAuth from '@/tina/stores/authStore'
import { useTaskPanelStore } from '../store'
import type { TaskPanelHandle } from '../types'
import { useTaskPanelScroll } from './useTaskPanelScroll'

/**
 * 任务面板集成Hook
 * 统一处理任务面板的初始化、数据加载、消息监听和滚动处理
 */
export const useTaskPanelIntegration = (conversationId: string) => {
  const auth = useAuth()
  const taskPanelRef = useRef<TaskPanelHandle>(null)
  const { currentTask } = useTaskPanelStore()
  const hasInitialized = useRef(false)

  // 获取滚动处理逻辑
  const { handleScroll, cleanup } = useTaskPanelScroll()

  // 初始化任务面板数据 - 只在token变化时执行一次
  useEffect(() => {
    const initTaskPanel = async () => {
      if (auth.token && !hasInitialized.current) {
        console.log('🚀 开始初始化任务面板数据')
        hasInitialized.current = true
        try {
          // 直接调用store方法，避免依赖引用
          const store = useTaskPanelStore.getState()
          await store.loadTaskData(auth.token)
          console.log('✅ 任务面板数据初始化成功')
        } catch (error) {
          console.error('❌ 初始化任务面板失败:', error)
          hasInitialized.current = false // 失败时重置，允许重试
        }
      } else if (!auth.token) {
        console.log('⏳ 等待用户认证token')
      } else if (hasInitialized.current) {
        console.log('✨ 任务面板已初始化，跳过重复初始化')
      }
    }

    // 当token变化时，重置初始化状态并重新初始化
    if (auth.token) {
      hasInitialized.current = false
      initTaskPanel()
    }
  }, [auth.token]) // 只依赖token，避免无限循环

  // 清理滚动相关的定时器
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    // 任务面板引用
    taskPanelRef,
    // 当前任务状态
    currentTask,
    // 滚动处理函数
    handleScroll,
    // 是否显示任务面板
    shouldShowTaskPanel: !!currentTask,
  }
}
