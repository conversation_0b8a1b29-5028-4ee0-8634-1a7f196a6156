import { memo } from 'react'
import type { TinaTaskStep } from '@/tina/lib/xml/xmlParser'
import { CheckmarkIcon, SpinnerIcon, TimeIcon } from './icons'

interface TaskItemProps {
  step: TinaTaskStep
  stepIndex: number
  currentStep: number
  status: 'init' | 'plan' | 'tools_call' | 'task_completed'
}

export const TaskItem = memo(
  ({ step, stepIndex, currentStep, status }: TaskItemProps) => {
    // 计算步骤状态
    const getStepStatus = () => {
      const stepNumber = stepIndex + 1

      if (status === 'task_completed') {
        return 'completed'
      } else if (status === 'tools_call') {
        if (stepNumber < currentStep) {
          return 'completed'
        } else if (stepNumber === currentStep) {
          return 'in_progress'
        } else {
          return 'pending'
        }
      } else {
        return 'pending'
      }
    }

    const stepStatus = getStepStatus()

    const renderIcon = () => {
      switch (stepStatus) {
        case 'completed':
          return <CheckmarkIcon />
        case 'in_progress':
          return <SpinnerIcon />
        case 'pending':
        default:
          return <TimeIcon />
      }
    }

    return (
      <div className='flex items-center px-4 py-2 text-sm'>
        <div className='mr-3 flex h-4 w-4 items-center justify-center'>
          {renderIcon()}
        </div>
        <span className='flex-grow text-left text-gray-800'>
          {step.title || step.tools_description || `步骤 ${stepIndex + 1}`}
        </span>
      </div>
    )
  },
)

TaskItem.displayName = 'TaskItem'
