import { IonMenuToggle } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import ListSVG from '@/assets/list.svg?react'
import ThreeDotSVG from '@/assets/three-dots.svg?react'
import { useConversationAPI } from './context'
import HeaderLogo from '@/assets/header_logo.png'

const ConversationHeader = () => {
  const history = useHistory()
  const { connectionStatus } = useConversationAPI() // 从context获取连接状态

  // 获取网络状态图标和颜色
  const getNetworkStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return { icon: 'fa-solid fa-wifi', color: 'text-green-500' }
      case 'connecting':
        return {
          icon: 'fa-solid fa-circle-notch fa-spin',
          color: 'text-yellow-500',
        }
      case 'disconnected':
        return { icon: 'fa-solid fa-wifi-slash', color: 'text-gray-400' }
      case 'error':
        return {
          icon: 'fa-solid fa-triangle-exclamation',
          color: 'text-red-500',
        }
      default:
        return { icon: 'fa-solid fa-wifi-slash', color: 'text-gray-400' }
    }
  }

  const networkStatus = getNetworkStatusIcon()

  // 处理设置点击
  const handleSettingsClick = () => {
    history.push('/settings')
  }

  return (
    <div className='flex items-center justify-between border-b border-black/5 bg-[#F5F5F5] px-4 py-2'>
      <IonMenuToggle>
        <ListSVG className={'h-8 w-8 p-1'} />
      </IonMenuToggle>
      <div className='flex items-center justify-center'>
        <img src={HeaderLogo} alt='Tina Chat Logo' className={'h-4 m-1'} />
        <i
          className={`${networkStatus.icon} ${networkStatus.color} mr-2 pl-2 text-sm`}
        />
      </div>
      <div className='flex items-center justify-end'>
        <ThreeDotSVG
          fill='black'
          className='h-8 w-8 cursor-pointer p-1'
          onClick={handleSettingsClick}
        />
      </div>
    </div>
  )
}

export default ConversationHeader
