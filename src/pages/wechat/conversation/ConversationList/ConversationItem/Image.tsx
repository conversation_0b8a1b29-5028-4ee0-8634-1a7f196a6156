import { memo, useState, useRef, useEffect } from 'react'
import { IMAGES_CACHE, initDBImagesCacheStore } from '@/db'
import type { IConversationTypeImage } from '@/stateV2/conversation'
import type { IStateProfile } from '@/stateV2/profile'
import { isMD5 } from '@/utils'
import { createPortal } from 'react-dom'
import { twJoin } from 'tailwind-merge'
import PlayFilledSVG from '@/assets/play-filled.svg?react'
import { h } from '@/components/HashAssets'
import WebViewer from '@/components/WebViewer'
import { App as cap } from '@capacitor/app'
import CommonBlock from './CommonBlock'

type Props = {
  imageInfo: IConversationTypeImage['imageInfo']
  upperText: IConversationTypeImage['upperText']
  senderId: IStateProfile['id']
  role: IConversationTypeImage['role']
  sendStatus?: IConversationTypeImage['sendStatus']
  isVideo?: boolean
  videoUrl?: string
}

const Image = ({
  imageInfo,
  upperText,
  senderId,
  role,
  sendStatus,
  isVideo,
  videoUrl,
}: Props) => {
  const [isWebViewerOpen, setIsWebViewerOpen] = useState(false)
  const [isClicked, setIsClicked] = useState(false)
  const [isFullScreenOpen, setIsFullScreenOpen] = useState(false)
  const [imageError, setImageError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>('')
  const [isImageLoaded, setIsImageLoaded] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [imageKey, setImageKey] = useState(0) // 用于强制重新加载图片

  const handleCloseFullScreen = () => {
    setIsClicked(true)
    // 短暂延迟后关闭全屏，让用户看到缩小反馈
    setTimeout(() => {
      setIsFullScreenOpen(false)
      // 恢复正常大小
      setTimeout(() => {
        setIsClicked(false)
      }, 200)
    }, 100)
  }

  // 添加移动端返回键监听器用于全屏状态
  useEffect(() => {
    if (!isFullScreenOpen) return

    // 设置全局标志，禁用默认返回键处理
    window.shouldHandleBackButton = false

    let backButtonListener: any = null

    const setupListener = async () => {
      backButtonListener = await cap.addListener('backButton', () => {
        // 只有当全屏打开时才处理返回键
        if (window.shouldHandleBackButton === false && isFullScreenOpen) {
          handleCloseFullScreen()
        }
      })
    }

    setupListener()

    return () => {
      // 恢复默认返回键处理
      window.shouldHandleBackButton = true
      if (backButtonListener) {
        backButtonListener.remove()
      }
    }
  }, [isFullScreenOpen])

  const handleClick = () => {
    // 如果正在上传，不允许点击
    if (sendStatus === 'sending') {
      return
    }

    // 添加点击放大动画效果
    setIsClicked(true)

    // 短暂延迟后打开相应界面，让用户看到放大反馈
    setTimeout(() => {
      if (isVideo && videoUrl) {
        // 如果是视频，使用WebViewer播放
        setIsWebViewerOpen(true)
      } else {
        // 如果是图片，全屏显示
        setIsFullScreenOpen(true)
      }
      // 保持放大状态直到界面开始显示
      setTimeout(() => {
        setIsClicked(false)
      }, 100)
    }, 200)
  }

  const handleCloseWebViewer = () => {
    // 关闭时先显示放大的图片组件
    setIsClicked(true)
    // 短暂延迟后恢复正常大小
    setTimeout(() => {
      setIsWebViewerOpen(false)
      setTimeout(() => {
        setIsClicked(false)
      }, 200)
    }, 100)
  }

  const getImageSrc = () => {
    let originalSrc = imageInfo // 直接使用原始imageInfo，不使用缓存

    // 如果是外部URL（http/https开头），且不包含 tina-test.bfbdata.com，使用跨域代理服务
    if (
      (originalSrc.startsWith('http://') ||
        originalSrc.startsWith('https://')) 
    ) {
      return `https://tina-test.bfbdata.com/api/image-gen/forward?url=${encodeURIComponent(originalSrc)}`
    }

    return originalSrc
  }

  const handleImageLoad = () => {
    // console.log('Image loaded successfully:', getImageSrc())
    setImageError(null)
    setDebugInfo('图片加载成功')
    setIsImageLoaded(true)
  }

  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>,
  ) => {
    const target = e.target as HTMLImageElement
    const errorDetails = {
      src: getImageSrc(),
      originalSrc: imageInfo,
      naturalWidth: target.naturalWidth,
      naturalHeight: target.naturalHeight,
      complete: target.complete,
      crossOrigin: target.crossOrigin,
      referrerPolicy: target.referrerPolicy,
      retryCount,
    }

    console.error('Image load error details:', errorDetails)

    // 如果重试次数少于5次，则等待5秒后重新加载图片
    if (retryCount < 5) {
      setRetryCount((prev) => prev + 1)
      setDebugInfo(`图片加载失败，正在重试... (${retryCount + 1}/5)`)
      console.log(`重试加载图片: ${retryCount + 1}/5`)

      // 等待5秒后重试
      setTimeout(() => {
        setImageKey((prev) => prev + 1) // 强制重新加载
      }, 5000)
    } else {
      // 达到最大重试次数后显示错误
      setImageError(`图片加载失败`)
      setDebugInfo(
        `重试失败，错误详情: ${JSON.stringify(errorDetails, null, 2)}`,
      )
      setIsImageLoaded(true) // 即使失败也设置为已加载，显示错误信息
    }
  }

  // 占位图SVG组件
  const PlaceholderSVG = () => (
    <svg
      width='100'
      height='100'
      viewBox='0 0 100 100'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className='animate-pulse'
    >
      <rect width='100' height='100' fill='#E5E7EB' rx='4' />
      <path
        d='M30 65V35C30 32.7909 31.7909 31 34 31H40L45 36H66C68.2091 36 70 37.7909 70 40V65C70 67.2091 68.2091 69 66 69H34C31.7909 69 30 67.2091 30 65Z'
        fill='#9CA3AF'
      />
      <circle cx='60' cy='45' r='3' fill='#6B7280' />
      <path
        d='M30 60L40 50L50 55L60 45L70 55V65C70 67.2091 68.2091 69 66 69H34C31.7909 69 30 67.2091 30 65V60Z'
        fill='#6B7280'
      />
    </svg>
  )

  // 上传中的加载动画
  const UploadingSpinner = () => (
    <div className='absolute inset-0 flex items-center justify-center rounded bg-black bg-opacity-30'>
      <div className='flex flex-col items-center space-y-2'>
        <div className='h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
        <span className='text-xs text-white'>上传中...</span>
      </div>
    </div>
  )

  // 上传失败的重试按钮
  const UploadFailedOverlay = () => (
    <div className='absolute inset-0 flex items-center justify-center rounded bg-black bg-opacity-50'>
      <div className='flex flex-col items-center space-y-2'>
        <div className='text-red-400'>
          <svg width='24' height='24' viewBox='0 0 24 24' fill='currentColor'>
            <path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' />
          </svg>
        </div>
        <span className='text-xs text-white'>上传失败</span>
        <span className='text-xs text-gray-300'>点击重试</span>
      </div>
    </div>
  )

  return (
    <>
      <CommonBlock
        upperText={upperText}
        senderId={senderId}
        innerBlockClassName={twJoin(
          'p-0 flex',
          role === 'mine' ? 'justify-end' : 'justify-start',
        )}
      >
        <div
          className={twJoin(
            'max-w-[60%]', // 使用固定最大宽度
            isVideo && 'relative',
            'transform transition-all duration-200 ease-out',
            isClicked ? 'scale-200' : 'scale-100',
            sendStatus === 'sending' ? 'cursor-default' : 'cursor-pointer',
          )}
          onClick={handleClick}
        >
          {/* 图片容器 */}
          <div className='relative flex min-h-[100px] min-w-[100px] items-center justify-center overflow-hidden rounded bg-gray-100'>
            {/* 占位图 - 加载完成前显示 */}
            {!isImageLoaded && !imageError && <PlaceholderSVG />}

            {/* 实际图片 */}
            <img
              key={imageKey} // 添加 key 用于强制重新加载
              src={getImageSrc()}
              className={twJoin(
                'rounded object-contain object-center transition-opacity duration-300',
                isImageLoaded && !imageError
                  ? 'h-auto max-h-64 w-full opacity-100'
                  : 'absolute opacity-0',
              )}
              onLoad={handleImageLoad}
              onError={handleImageError}
              alt='图片'
            />

            {/* 错误显示 */}
            {imageError && isImageLoaded && (
              <div className='flex min-h-[100px] flex-col items-center justify-center space-y-2 p-4 text-center text-xs text-red-500'>
                <PlaceholderSVG />
                <div>{imageError}</div>
              </div>
            )}

            {/* 上传状态覆盖层 */}
            {role === 'mine' && sendStatus === 'sending' && (
              <UploadingSpinner />
            )}
            {role === 'mine' && sendStatus === 'failed' && (
              <UploadFailedOverlay />
            )}

            {/* 视频播放按钮 */}
            {isVideo &&
              !imageError &&
              isImageLoaded &&
              sendStatus !== 'sending' && (
                <div className='absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full border border-white bg-black bg-opacity-30 p-1'>
                  <PlayFilledSVG fill='white' width={32} height={32} />
                </div>
              )}
          </div>
        </div>
      </CommonBlock>

      {/* 视频播放器 */}
      {isVideo && videoUrl && (
        <WebViewer
          url={videoUrl}
          title='视频播放'
          isOpen={isWebViewerOpen}
          onClose={handleCloseWebViewer}
        />
      )}

      {/* 图片全屏显示 */}
      {!isVideo &&
        isFullScreenOpen &&
        !imageError &&
        createPortal(
          <div
            className='fixed inset-0 z-[100] flex cursor-pointer items-center justify-center bg-black bg-opacity-90'
            onClick={handleCloseFullScreen}
          >
            <div className='relative max-h-full max-w-full'>
              <img
                src={getImageSrc()}
                className='h-full w-full object-contain'
                alt='图片'
              />
            </div>
          </div>,
          document.body,
        )}
    </>
  )
}

export default memo(Image)
