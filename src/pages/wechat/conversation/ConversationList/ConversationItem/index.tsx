import { memo } from 'react'
import { MYSELF_ID } from '@/faker/wechat/user'
import {
  EConversationType,
  type TConversationItem,
} from '@/stateV2/conversation/typing'
import { useParams } from 'react-router-dom'
import { useConversationAPI } from '../../context'
import CenterText from './CenterText'
import Image from './Image'
import Markdown from './Markdown'
import { MarkmapComponent } from './Markmap'
import News from './News'
import NotificationStatus from './NotificationStatus'
import PersonalCard from './PersonalCard'
import RedPacket from './RedPacket'
import RedPacketAcceptedReply from './RedPacketAcceptedReply'
import Text from './Text'
import Transfer from './Transfer'
import UserMaybeSay from './UserMaybeSay'
import Voice from './Voice'

type Props = {
  data: TConversationItem
}

const ConversationItem = ({ data }: Props) => {
  const { type, role, upperText, id: conversationItemId } = data
  const { id } = useParams<{ id: string }>()
  const { hideNotification } = useConversationAPI()
  const senderId = role === 'friend' ? id! : MYSELF_ID

  switch (type) {
    case EConversationType.text:
      return (
        <Text
          conversationItemId={conversationItemId}
          upperText={upperText}
          senderId={senderId}
          textContent={data.textContent}
          referenceId={data.referenceId}
          sendStatus={data.sendStatus}
          role={data.role}
        />
      )
    case EConversationType.centerText:
      return (
        <CenterText
          upperText={upperText}
          simpleContent={data.simpleContent}
          extraClassName={data.extraClassName}
        />
      )
    case EConversationType.transfer:
      return (
        <Transfer
          role={role}
          upperText={upperText}
          senderId={senderId}
          amount={data.amount}
          note={data.note}
          transferStatus={data.transferStatus}
          originalSender={data.originalSender}
        />
      )
    case EConversationType.redPacket:
      return (
        <RedPacket
          role={role}
          upperText={upperText}
          senderId={senderId}
          amount={data.amount}
          note={data.note}
          redPacketStatus={data.redPacketStatus}
          originalSender={data.originalSender}
        />
      )
    case EConversationType.image:
      return (
        <Image
          role={role}
          imageInfo={data.imageInfo}
          upperText={upperText}
          senderId={senderId}
          sendStatus={data.sendStatus}
        />
      )
    case EConversationType.video:
      return (
        <Image
          role={role}
          imageInfo={data.videoInfo}
          upperText={upperText}
          senderId={senderId}
          sendStatus={data.sendStatus}
          isVideo
          videoUrl={data.videoUrl}
        />
      )
    case EConversationType.voice:
      return (
        <Voice
          senderId={senderId}
          upperText={upperText}
          duration={data.duration}
          isRead={data.isRead}
          role={role}
          showStt={data.showStt}
          stt={data.stt}
          originalStt={data.originalStt}
          streamState={data.streamState}
          firstSegment={data.firstSegment}
          fullContent={data.fullContent}
          remainingContent={data.remainingContent}
        />
      )
    case EConversationType.redPacketAcceptedReply:
      return (
        <RedPacketAcceptedReply
          id={data.id}
          redPacketId={data.redPacketId}
          upperText={upperText}
        />
      )
    case EConversationType.personalCard:
      return (
        <PersonalCard
          avatarInfo={data.avatarInfo}
          nickname={data.nickname}
          senderId={senderId}
          upperText={upperText}
        />
      )
    case EConversationType.news:
      return (
        <News
          title={data.title}
          description={data.description}
          imageUrl={data.imageUrl}
          source={data.source}
          url={data.url}
          upperText={upperText || ''}
          senderId={senderId}
        />
      )
    case EConversationType.markdown:
      return <Markdown data={data} />
    case EConversationType.markmap:
      return (
        <MarkmapComponent
          title={data.title}
          content={data.content}
          upperText={upperText || ''}
          senderId={senderId}
        />
      )
    case EConversationType.userMaybeSay:
      return (
        <UserMaybeSay
          upperText={upperText}
          senderId={senderId}
          suggestions={data.suggestions}
        />
      )
    case EConversationType.notification:
      return (
        <NotificationStatus
          upperText={upperText}
          text={data.text}
          icon={data.icon}
          hideAfter={data.hideAfter}
          notificationId={data.notificationId}
          extraClassName={data.extraClassName}
          onHide={hideNotification}
        />
      )
    default:
      return null
  }
}

export default memo(ConversationItem)
