import React, { useState, useRef, useEffect } from 'react'
import { MYSELF_ID } from '@/faker/wechat/user'
import { IConversationTypeMarkdown } from '@/stateV2/conversation/typing'
import type { IStateProfile } from '@/stateV2/profile'
import { useParams } from 'react-router-dom'
import MarkdownRenderer from '@/components/MarkdownRenderer'
import MarkdownViewer from '@/components/MarkdownViewer'
import CommonBlock from './CommonBlock'

interface MarkdownProps {
  data: IConversationTypeMarkdown
}

const Markdown: React.FC<MarkdownProps> = ({ data }) => {
  const { markdownContent, summary, upperText, role } = data
  const { id } = useParams<{ id: string }>()
  const senderId = role === 'friend' ? id! : MYSELF_ID

  const [isViewerOpen, setIsViewerOpen] = useState(false)
  const [isClicked, setIsClicked] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [contentHeight, setContentHeight] = useState(0)
  const contentRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)
  const markdownRef = useRef<HTMLDivElement>(null)

  // 测量内容实际高度
  useEffect(() => {
    if (measureRef.current) {
      const height = measureRef.current.scrollHeight
      setContentHeight(height)
    }
  }, [markdownContent])

  // 计算当前应该显示的高度
  const getDisplayHeight = () => {
    if (!isExpanded) return 160 // 增加初始高度以适应更大的文字
    return contentHeight // 完全展开
  }

  const handleClick = () => {
    // 添加点击放大动画效果
    // setIsClicked(true);

    if (isExpanded) {
      // 已展开状态：单击打开 MarkdownViewer
      // 短暂延迟后打开 MarkdownViewer，让用户看到放大反馈
      setTimeout(() => {
        setIsViewerOpen(true)
        // 保持放大状态直到 MarkdownViewer 开始显示
        setTimeout(() => {
          setIsClicked(false)
        }, 100)
      }, 200)
    } else {
      // 未展开状态：单击展开
      // 短暂延迟后展开，让用户看到放大反馈
      setTimeout(() => {
        setIsExpanded(true)

        // 展开后检查是否需要滚动
        setTimeout(() => {
          const element = contentRef.current
          if (element) {
            const rect = element.getBoundingClientRect()
            const windowHeight = window.innerHeight

            // 如果元素底部接近屏幕底部（距离小于100px），则滚动到底部
            if (rect.bottom > windowHeight - 100) {
              window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth',
              })
            }
          }
        }, 350) // 等待展开动画完成

        // 恢复正常大小
        setTimeout(() => {
          setIsClicked(false)
        }, 200)
      }, 200)
    }
  }

  const handleCloseViewer = () => {
    // 关闭时先显示放大的 Markdown 组件
    setIsClicked(true)
    // 短暂延迟后恢复正常大小
    setTimeout(() => {
      setIsViewerOpen(false)
      setTimeout(() => {
        setIsClicked(false)
      }, 200)
    }, 100)
  }

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!isExpanded) {
      // 添加点击动画效果
      setIsClicked(true)

      setTimeout(() => {
        setIsExpanded(true)

        // 展开后检查是否需要滚动
        setTimeout(() => {
          const element = contentRef.current
          if (element) {
            const rect = element.getBoundingClientRect()
            const windowHeight = window.innerHeight

            // 如果元素底部接近屏幕底部（距离小于100px），则滚动到底部
            if (rect.bottom > windowHeight - 100) {
              window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth',
              })
            }
          }
        }, 350) // 等待展开动画完成

        // 恢复正常大小
        setTimeout(() => {
          setIsClicked(false)
        }, 200)
      }, 200)
    }
  }

  const displaySummary = summary?.trim()
  const showExpandButton = contentHeight > 160 && !isExpanded // 调整判断高度

  return (
    <>
      <div ref={markdownRef}>
        <CommonBlock
          upperText={upperText || ''}
          senderId={senderId as IStateProfile['id']}
          blockClassName='w-4/5'
          innerBlockClassName={`w-full bg-white before:bg-white p-0 cursor-pointer transition-all duration-200 ease-out transform ${
            isClicked ? 'scale-250' : 'scale-100'
          }`}
          onClick={handleClick}
        >
          <div className='p-3'>
            <div className='flex flex-col space-y-2'>
              {/* 摘要部分 */}
              {displaySummary && (
                <div className='rounded border-b border-gray-100 bg-gray-50 px-2 py-1 pb-2 text-sm text-gray-600'>
                  {displaySummary}
                </div>
              )}

              {/* Markdown 渲染内容 */}
              <div className='relative min-w-0 flex-1 overflow-hidden'>
                {/* 隐藏的测量元素 */}
                <div
                  ref={measureRef}
                  className='pointer-events-none absolute -top-[9999px] left-0 w-full opacity-0'
                  style={{ visibility: 'hidden' }}
                >
                  <MarkdownRenderer
                    content={markdownContent}
                    variant='preview'
                  />
                </div>

                {/* 实际显示的内容 */}
                <div
                  ref={contentRef}
                  className='markdown-body-preview relative overflow-hidden leading-relaxed transition-all duration-300'
                  style={{
                    height: `${getDisplayHeight()}px`,
                    maxHeight: 'none',
                  }}
                >
                  <MarkdownRenderer
                    content={markdownContent}
                    variant='preview'
                  />

                  {/* 渐变遮罩 */}
                  {!isExpanded && (
                    <div className='pointer-events-none absolute bottom-0 left-0 right-0 h-10 bg-gradient-to-t from-white to-transparent' />
                  )}
                </div>

                {/* 展开按钮 */}
                {showExpandButton && (
                  <div className='pointer-events-auto absolute bottom-0 right-2'>
                    <button
                      onClick={handleToggleExpand}
                      className='flex items-center space-x-1 rounded-full border border-gray-200 bg-white/90 px-2 py-1 text-xs text-gray-600 shadow-sm backdrop-blur-sm transition-all duration-200 hover:bg-gray-50 hover:text-gray-800'
                    >
                      <svg
                        className='h-3 w-3'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M19 9l-7 7-7-7'
                        />
                      </svg>
                      <span>展开</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CommonBlock>
      </div>

      {/* MarkdownViewer 全屏查看器 */}
      <MarkdownViewer
        content={markdownContent}
        isOpen={isViewerOpen}
        onClose={handleCloseViewer}
      />
    </>
  )
}

export default Markdown
