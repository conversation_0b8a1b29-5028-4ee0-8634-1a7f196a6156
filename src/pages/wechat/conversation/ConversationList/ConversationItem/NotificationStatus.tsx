import { memo, useEffect, useState } from 'react'
import type { IConversationTypeNotification } from '@/stateV2/conversation'
import { twMerge } from 'tailwind-merge'

type Props = {
  extraClassName: IConversationTypeNotification['extraClassName']
  upperText: IConversationTypeNotification['upperText']
  text: IConversationTypeNotification['text']
  icon?: IConversationTypeNotification['icon']
  hideAfter?: IConversationTypeNotification['hideAfter']
  notificationId: IConversationTypeNotification['notificationId']
  onHide?: (notificationId: string) => void
}

const NotificationStatus = ({
  extraClassName,
  upperText,
  text,
  icon,
  hideAfter,
  notificationId,
  onHide,
}: Props) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (hideAfter && hideAfter > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        onHide?.(notificationId)
      }, hideAfter)

      return () => clearTimeout(timer)
    }
  }, [hideAfter, notificationId, onHide])

  if (!isVisible) {
    return null
  }

  return (
    <>
      {upperText && (
        <div className={'m-auto text-xs text-black/50'}>{upperText}</div>
      )}
      <div
        className={twMerge(
          'm-auto flex items-center gap-1 text-xs text-black/50',
          extraClassName,
        )}
      >
        {icon && (
          <i
            className={`fas ${icon} text-black/50`}
            style={{ fontSize: '12px' }}
          />
        )}
        <div>{text}</div>
      </div>
    </>
  )
}

export default memo(NotificationStatus)
