import { memo, useEffect, useRef, useState } from 'react'
import type { IConversationTypeVoice } from '@/stateV2/conversation'
import type { IStateProfile } from '@/stateV2/profile'
import { SyncOutlined } from '@ant-design/icons'
import { nanoid } from 'nanoid'
import { twJoin } from 'tailwind-merge'
import VoiceSVG from '@/assets/voice.svg?react'
import VoiceingSVG from '@/assets/voiceing.svg?react'
import { globalAudioPlayer } from '@/utils/GlobalAudioPlayer'
import CommonBlock from './CommonBlock'

type Props = {
  duration: IConversationTypeVoice['duration']
  senderId: IStateProfile['id']
  upperText: IConversationTypeVoice['upperText']
  isRead: IConversationTypeVoice['isRead']
  role: IConversationTypeVoice['role']
  showStt: IConversationTypeVoice['showStt']
  stt: IConversationTypeVoice['stt']
  originalStt: IConversationTypeVoice['originalStt']
  streamState?: IConversationTypeVoice['streamState']
  firstSegment?: IConversationTypeVoice['firstSegment']
  fullContent?: IConversationTypeVoice['fullContent']
  remainingContent?: IConversationTypeVoice['remainingContent']
}

const Voice = ({
  senderId,
  upperText,
  duration,
  role,
  isRead,
  showStt,
  stt,
  originalStt,
  streamState = 'creating',
  firstSegment,
  fullContent,
  remainingContent,
}: Props) => {
  const [hasBeenPlayed, setHasBeenPlayed] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 生成唯一的音频ID
  const audioIdRef = useRef<string>(nanoid())
  const audioId = audioIdRef.current

  // 检查播放状态
  useEffect(() => {
    const checkPlayingStatus = () => {
      // 检查主音频或剩余音频是否在播放
      const mainPlaying = globalAudioPlayer.isPlaying(audioId)
      const remainingPlaying = globalAudioPlayer.isPlaying(
        `${audioId}-remaining`,
      )
      const mainLoading = globalAudioPlayer.isLoadingAudio(audioId)
      const remainingLoading = globalAudioPlayer.isLoadingAudio(
        `${audioId}-remaining`,
      )

      setIsPlaying(mainPlaying || remainingPlaying)
      setIsLoading(mainLoading || remainingLoading)
    }

    // 定期检查播放状态
    const interval = setInterval(checkPlayingStatus, 100)

    return () => clearInterval(interval)
  }, [audioId])

  // 构建语音 URL，处理特殊字符
  const buildVoiceUrl = (text: string) => {
    // 先检查输入
    if (!text || typeof text !== 'string') {
      console.error('buildVoiceUrl 接收到无效文本:', text)
      return ''
    }

    // 清理空白字符和特殊字符，检查是否为有效内容
    const trimmedText = text.trim()
    if (!trimmedText || trimmedText.length === 0) {
      console.warn('buildVoiceUrl 文本为空或只有空白字符:', text)
      return ''
    }

    // 如果只包含特殊字符（如引号、空格等），则认为无效
    const validContentRegex = /[a-zA-Z\u4e00-\u9fa5]/ // 包含字母或中文字符
    if (!validContentRegex.test(trimmedText)) {
      console.warn('buildVoiceUrl 文本不包含有效字符:', text)
      return ''
    }

    // 替换可能导致 URL 问题的特殊字符
    const cleanedText = trimmedText
      .replace(/\//g, '_') // 替换斜杠
      .replace(/\\/g, '_') // 替换反斜杠
      .replace(/\?/g, '_') // 替换问号
      .replace(/#/g, '_') // 替换井号
      .replace(/&/g, '_') // 替换和号
      .replace(/"/g, '_') // 替换双引号
      .replace(/'/g, '_') // 替换单引号

    const url = `https://tina-test.bfbdata.com/api/image-gen/voice/?prompt=${encodeURIComponent(cleanedText)}`
    console.log('构建语音URL:', {
      原文本: text,
      清理后: cleanedText,
      最终URL: url,
    })
    return url
  }

  // 验证内容是否有效
  const isValidContent = (text: string) => {
    if (!text || typeof text !== 'string') return false
    const trimmedText = text.trim()
    if (!trimmedText || trimmedText.length === 0) return false

    // 检查是否包含有效字符（字母或中文）
    const validContentRegex = /[a-zA-Z\u4e00-\u9fa5]/
    return validContentRegex.test(trimmedText)
  }

  // 播放剩余音频
  const playRemainingAudio = () => {
    if (remainingContent && isValidContent(remainingContent)) {
      console.log('开始播放剩余音频')
      const voiceUrl = buildVoiceUrl(remainingContent)
      if (voiceUrl) {
        const remainingAudioId = `${audioId}-remaining`
        globalAudioPlayer.play(remainingAudioId, voiceUrl, {
          onPlay: () => {
            // 不需要在这里设置状态，因为 useEffect 会自动检查
            setHasBeenPlayed(true)
          },
          onPause: () => {
            // 状态会在 useEffect 中自动更新
          },
          onEnded: () => {
            // 状态会在 useEffect 中自动更新
          },
          onError: (error) => {
            console.error('剩余音频播放失败:', error)
            // 状态会在 useEffect 中自动更新
          },
        })
      }
    }
  }

  // 点击语音处理函数
  const handleVoiceClick = () => {
    if (streamState === 'creating' || isLoading) return

    // 检查是否有音频正在播放
    const mainPlaying = globalAudioPlayer.isPlaying(audioId)
    const remainingPlaying = globalAudioPlayer.isPlaying(`${audioId}-remaining`)

    // 如果有音频正在播放，则暂停
    if (mainPlaying || remainingPlaying) {
      globalAudioPlayer.pause()
      return
    }

    // 如果没有音频在播放，则开始播放第一段
    const content = firstSegment || originalStt
    if (!content || !isValidContent(content)) {
      console.error('无效的音频内容')
      return
    }

    const voiceUrl = buildVoiceUrl(content)
    if (!voiceUrl) {
      console.error('无法构建音频URL')
      return
    }

    // 使用全局音频播放器播放
    globalAudioPlayer.play(audioId, voiceUrl, {
      onLoadStart: () => {
        setIsLoading(true)
      },
      onLoadEnd: () => {
        setIsLoading(false)
      },
      onPlay: () => {
        setIsPlaying(true)
        setHasBeenPlayed(true)
      },
      onPause: () => {
        setIsPlaying(false)
      },
      onEnded: () => {
        setIsPlaying(false)
        setHasBeenPlayed(true)
        // 如果有剩余内容，播放剩余部分
        if (remainingContent && isValidContent(remainingContent)) {
          setTimeout(() => {
            playRemainingAudio()
          }, 300)
        }
      },
      onError: (error) => {
        console.error('音频播放失败:', error)
        setIsPlaying(false)
        setIsLoading(false)
      },
    })
  }

  // 决定是否显示红点：未读且未播放过
  const shouldShowRedDot = !isRead && !hasBeenPlayed && role === 'friend'

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    if (streamState === 'creating') {
      return (
        <div className='flex items-center'>
          <SyncOutlined
            spin
            className='mr-2 text-xs text-black'
            style={{
              transformOrigin: 'center center',
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
          <span className='self-center text-sm'>生成中...</span>
        </div>
      )
    }

    if (isPlaying) {
      return <span className='self-center text-sm'>{duration}″</span>
    }

    return <span className='self-center text-sm'>{duration}″</span>
  }

  return (
    <>
      <CommonBlock
        senderId={senderId}
        upperText={upperText}
        innerBlockClassName='w-full group-[.friend]:bg-white group-[.mine]:bg-[#8CE97F] group-[.friend]:before:bg-white group-[.mine]:before:bg-[#8CE97F]'
        blockStyle={{
          width: `${duration <= 10 ? 30 + duration * 2.2 : 52}%`,
          maxWidth: '52%',
        }}
        extraElement={
          shouldShowRedDot && (
            <div className='absolute -right-5 top-1/2 -translate-y-1/2'>
              <div className='h-2 w-2 rounded-full bg-wechatRed-3' />
            </div>
          )
        }
      >
        <div
          className={twJoin(
            'flex h-6',
            role === 'mine' ? 'flex-row-reverse' : 'flex-row',
            streamState !== 'creating' && !isLoading && 'cursor-pointer',
          )}
          onClick={handleVoiceClick}
        >
          {isLoading ? (
            <SyncOutlined
              spin
              className='self-center text-sm text-black'
              style={{
                transformOrigin: 'center center',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '28px',
                height: '28px',
              }}
            />
          ) : isPlaying ? (
            <VoiceingSVG
              width={28}
              height={28}
              className={twJoin(
                'self-center text-blue-500 transition-colors',
                role === 'mine' && 'rotate-180',
              )}
            />
          ) : (
            <VoiceSVG
              width={28}
              height={28}
              className={twJoin(
                'self-center transition-colors',
                role === 'mine' && 'rotate-180',
                streamState === 'creating' && 'opacity-50',
              )}
            />
          )}
          {renderStatusIndicator()}
        </div>
      </CommonBlock>
      {showStt && !!stt && (
        <CommonBlock
          hideAvatar
          upperText={undefined}
          senderId={senderId}
          innerBlockClassName='bg-white'
          blockClassName='!mt-1'
        >
          {stt}
        </CommonBlock>
      )}
    </>
  )
}

export default memo(Voice)
