import { memo, useRef, useState } from 'react'
import type { IStateProfile } from '@/stateV2/profile'
import ReactMarkdown from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import { h } from '@/components/HashAssets'
import WebViewer from '@/components/WebViewer'
import CommonBlock from './CommonBlock'

type Props = {
  title: string
  description: string
  imageUrl: string
  source: string
  url: string
  upperText: string
  senderId: IStateProfile['id']
}

const News = ({
  title,
  description,
  imageUrl,
  source,
  url,
  upperText,
  senderId,
}: Props) => {
  const [isWebViewerOpen, setIsWebViewerOpen] = useState(false)
  const [isClicked, setIsClicked] = useState(false)
  const newsRef = useRef<HTMLDivElement>(null)

  const getImageSrc = () => {
    let originalSrc = imageUrl // 直接使用原始imageUrl，不使用缓存

    // 如果是外部URL（http/https开头），使用跨域代理服务
    if (
      originalSrc.startsWith('http://') ||
      originalSrc.startsWith('https://')
    ) {
      return `https://tina-test.bfbdata.com/api/image-gen/forward?url=${encodeURIComponent(originalSrc)}`
    }

    return originalSrc
  }

  const handleClick = () => {
    // 添加点击放大动画效果
    setIsClicked(true)

    // 短暂延迟后打开 WebViewer，让用户看到放大反馈
    setTimeout(() => {
      setIsWebViewerOpen(true)
      // 保持放大状态直到 WebViewer 开始显示
      setTimeout(() => {
        setIsClicked(false)
      }, 100)
    }, 200)
  }

  const handleCloseWebViewer = () => {
    // 关闭时先显示放大的 News 组件
    setIsClicked(true)
    // 短暂延迟后恢复正常大小
    setTimeout(() => {
      setIsWebViewerOpen(false)
      setTimeout(() => {
        setIsClicked(false)
      }, 200)
    }, 100)
  }

  return (
    <>
      <div ref={newsRef}>
        <CommonBlock
          upperText={upperText}
          senderId={senderId}
          blockClassName='w-4/5'
          innerBlockClassName={`w-full bg-white before:bg-white p-0 cursor-pointer hover:bg-gray-50 transition-all duration-200 ease-out transform ${
            isClicked ? 'scale-200' : 'scale-100'
          }`}
          onClick={handleClick}
        >
          {/* 上部：标题 */}
          <div className='px-3 pb-2 pt-3'>
            <div className='line-clamp-2 text-sm font-medium leading-tight text-black'>
              {title}
            </div>
          </div>

          {/* 中部：描述和图片 */}
          <div className='flex px-3 pb-2'>
            {/* 左侧描述 */}
            <div className='flex-1 pr-3'>
              <div className='line-clamp-3 text-xs leading-tight text-gray-500'>
                <ReactMarkdown
                  skipHtml={false}
                  rehypePlugins={[rehypeRaw, rehypeHighlight]}
                >
                  {description}
                </ReactMarkdown>
              </div>
            </div>

            {/* 右侧正方形图片 */}
            <div className='h-12 w-12 flex-shrink-0'>
              <h.img
                src={getImageSrc()}
                className='h-full w-full object-cover'
              />
            </div>
          </div>

          {/* 底部：来源（样式与个人名片相同） */}
          <div className='border-t border-gray-100 bg-gray-50 px-3 py-1.5'>
            <div className='flex items-center text-xs text-gray-400'>
              <span>{source}</span>
            </div>
          </div>
        </CommonBlock>
      </div>

      {/* 全屏网页查看器 */}
      <WebViewer
        url={url}
        title={title}
        isOpen={isWebViewerOpen}
        onClose={handleCloseWebViewer}
      />
    </>
  )
}

export default memo(News)
