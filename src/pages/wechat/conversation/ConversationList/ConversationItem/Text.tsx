import { memo, useState } from 'react'
import type { IConversationTypeText } from '@/stateV2/conversation'
import type { IStateProfile } from '@/stateV2/profile'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import <PERSON>Viewer from '@/components/WebViewer'
import MarkdownViewer from '@/components/MarkdownViewer'
import { useConversationAPI } from '../../context'
import CommonBlock from './CommonBlock'
import TextReference from './TextReference'

type Props = {
  conversationItemId: IConversationTypeText['id']
  upperText: IConversationTypeText['upperText']
  senderId: IStateProfile['id']
  textContent: IConversationTypeText['textContent']
  referenceId: IConversationTypeText['referenceId']
  sendStatus?: IConversationTypeText['sendStatus']
  role: IConversationTypeText['role']
}

const Text = ({
  upperText,
  senderId,
  textContent,
  referenceId,
  conversationItemId,
  sendStatus,
  role,
}: Props) => {
  const { resendMessage } = useConversationAPI()
  const [webViewerUrl, setWebViewerUrl] = useState<string>('')
  const [isWebViewerOpen, setIsWebViewerOpen] = useState(false)
  const [isMarkdownViewerOpen, setIsMarkdownViewerOpen] = useState(false)

  const handleResend = () => {
    // 使用resendMessage函数，它会删除原消息并重发
    resendMessage(conversationItemId)
  }

  const handleLinkClick = (href: string) => {
    setWebViewerUrl(href)
    setIsWebViewerOpen(true)
  }

  const handleCloseWebViewer = () => {
    setIsWebViewerOpen(false)
    setWebViewerUrl('')
  }

  const handleDoubleClick = () => {
    setIsMarkdownViewerOpen(true)
  }

  const handleCloseMarkdownViewer = () => {
    setIsMarkdownViewerOpen(false)
  }

  // 将Descendant[]转换为纯文本字符串
  const markdownText = textContent
    .map((descendant) => {
      // 处理Slate格式的paragraph节点
      if (
        'type' in descendant &&
        descendant.type === 'paragraph' &&
        'children' in descendant
      ) {
        return descendant.children
          .map((child: any) => {
            if ('text' in child) {
              return child.text
            }
            return ''
          })
          .join('')
      }
      // 处理简单的文本节点
      else if ('text' in descendant) {
        return descendant.text
      }
      return ''
    })
    .join('')

  return (
    <>
      <CommonBlock
        upperText={upperText}
        senderId={senderId}
        innerBlockClassName='group-[.friend]:bg-white group-[.mine]:bg-[#8CE97F] group-[.friend]:before:bg-white group-[.mine]:before:bg-[#8CE97F]'
        extraElement={
          role === 'mine' && sendStatus === 'failed' ? (
            <button
              onClick={handleResend}
              className='relative z-10 -ml-8 flex cursor-pointer items-center justify-center bg-transparent px-1 text-gray-500 hover:text-gray-600'
              title='重发消息'
            >
              <i className='fa-solid fa-rotate-right text-base' />
            </button>
          ) : undefined
        }
      >
        <div 
          className='leading-relaxed cursor-pointer' 
          style={{ lineHeight: '1.618' }}
          onDoubleClick={handleDoubleClick}
          title="双击查看完整内容"
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              a: ({ href, children, ...props }) => (
                <a
                  href='#'
                  onClick={(e) => {
                    e.preventDefault()
                    if (href) {
                      handleLinkClick(href)
                    }
                  }}
                  className='inline-flex cursor-pointer items-center gap-1 text-blue-600 hover:text-blue-800'
                  {...props}
                >
                  <span className={'break-all'}>
                    {children}
                    <svg
                      className='inline-block h-3 w-3'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </span>
                </a>
              ),
              code: ({ node, className, children, ...props }) => {
                // 通过检查 className 是否包含 language- 前缀来判断是否为代码块
                const isCodeBlock =
                  className && className.startsWith('language-')

                if (!isCodeBlock) {
                  // 内联代码，使用 span 元素
                  return (
                    <span
                      className='rounded py-0.5 font-mono text-sm text-gray-800'
                      {...props}
                    >
                      {children}
                    </span>
                  )
                } else {
                  // 代码块，直接使用 span（由 pre 组件提供块级容器）
                  return (
                    <span
                      className='bg-transparent font-mono text-sm'
                      {...props}
                    >
                      {children}
                    </span>
                  )
                }
              },
              pre: ({ children, ...props }) => (
                <pre
                  className='my-2 overflow-x-auto whitespace-pre-wrap border-l-4 border-gray-300 bg-gray-100 p-3 font-mono text-sm text-gray-700'
                  {...props}
                >
                  {children}
                </pre>
              ),
              // 表格支持
              table: ({ children, ...props }) => (
                <div className='my-4 overflow-x-auto'>
                  <table
                    className='min-w-full border-collapse border border-gray-300'
                    {...props}
                  >
                    {children}
                  </table>
                </div>
              ),
              thead: ({ children, ...props }) => (
                <thead className='bg-gray-100' {...props}>
                  {children}
                </thead>
              ),
              tbody: ({ children, ...props }) => (
                <tbody {...props}>{children}</tbody>
              ),
              tr: ({ children, ...props }) => (
                <tr className='border-b border-gray-200' {...props}>
                  {children}
                </tr>
              ),
              th: ({ children, ...props }) => (
                <th
                  className='border border-gray-300 px-4 py-2 text-left font-semibold'
                  {...props}
                >
                  {children}
                </th>
              ),
              td: ({ children, ...props }) => (
                <td className='border border-gray-300 px-4 py-2' {...props}>
                  {children}
                </td>
              ),
              // 引用块
              blockquote: ({ children, ...props }) => (
                <blockquote
                  className='my-4 border-l-4 border-blue-300 bg-blue-50 p-4 italic text-gray-700'
                  {...props}
                >
                  {children}
                </blockquote>
              ),
              // 标题
              h1: ({ children, ...props }) => (
                <h1
                  className='mb-4 mt-6 text-2xl font-bold text-gray-900'
                  {...props}
                >
                  {children}
                </h1>
              ),
              h2: ({ children, ...props }) => (
                <h2
                  className='mb-3 mt-5 text-xl font-bold text-gray-900'
                  {...props}
                >
                  {children}
                </h2>
              ),
              h3: ({ children, ...props }) => (
                <h3
                  className='mb-2 mt-4 text-lg font-semibold text-gray-900'
                  {...props}
                >
                  {children}
                </h3>
              ),
              h4: ({ children, ...props }) => (
                <h4
                  className='mb-2 mt-3 text-base font-semibold text-gray-900'
                  {...props}
                >
                  {children}
                </h4>
              ),
              h5: ({ children, ...props }) => (
                <h5
                  className='mb-1 mt-2 text-sm font-semibold text-gray-900'
                  {...props}
                >
                  {children}
                </h5>
              ),
              h6: ({ children, ...props }) => (
                <h6
                  className='mb-1 mt-2 text-xs font-semibold text-gray-900'
                  {...props}
                >
                  {children}
                </h6>
              ),
              // 列表
              ul: ({ children, ...props }) => (
                <ul className='ml-6 list-disc space-y-1' {...props}>
                  {children}
                </ul>
              ),
              ol: ({ children, ...props }) => (
                <ol className='ml-6 list-decimal space-y-1' {...props}>
                  {children}
                </ol>
              ),
              li: ({ children, ...props }) => (
                <li className='text-gray-800' {...props}>
                  {children}
                </li>
              ),
              // 段落
              p: ({ children, ...props }) => <p {...props}>{children}</p>,
              // 强调
              strong: ({ children, ...props }) => (
                <strong className='font-bold text-gray-900' {...props}>
                  {children}
                </strong>
              ),
              em: ({ children, ...props }) => (
                <em className='italic text-gray-800' {...props}>
                  {children}
                </em>
              ),
              // 分隔线
              hr: ({ ...props }) => (
                <hr className='my-6 border-t border-gray-300' {...props} />
              ),
            }}
          >
            {markdownText}
          </ReactMarkdown>
        </div>
      </CommonBlock>
      {referenceId && (
        <TextReference
          referenceId={referenceId}
          conversationItemId={conversationItemId}
        />
      )}

      {/* WebViewer for links */}
      <WebViewer
        url={webViewerUrl}
        title='链接内容'
        isOpen={isWebViewerOpen}
        onClose={handleCloseWebViewer}
      />

      {/* MarkdownViewer for double-click */}
      <MarkdownViewer
        content={markdownText}
        isOpen={isMarkdownViewerOpen}
        onClose={handleCloseMarkdownViewer}
      />
    </>
  )
}

export default memo(Text)
