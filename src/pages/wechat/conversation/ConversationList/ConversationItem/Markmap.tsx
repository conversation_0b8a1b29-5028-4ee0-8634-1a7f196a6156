import { memo, useState, useRef, useEffect } from 'react'
import type { IStateProfile } from '@/stateV2/profile'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import { h } from '@/components/HashAssets'
import MarkmapViewer from '@/components/MarkmapViewer'
import CommonBlock from './CommonBlock'

interface Props {
  title: string
  content: string
  upperText?: string
  senderId: IStateProfile['id']
}

export const MarkmapComponent = memo(
  ({ title, content, upperText, senderId }: Props) => {
    const [isViewerOpen, setIsViewerOpen] = useState(false)
    const [isClicked, setIsClicked] = useState(false)
    const svgRef = useRef<SVGSVGElement>(null)
    const markmapRef = useRef<Markmap | null>(null)

    // 初始化 Markmap
    useEffect(() => {
      if (svgRef.current && content) {
        const transformer = new Transformer()
        const { root } = transformer.transform(content)

        if (markmapRef.current) {
          markmapRef.current.destroy()
        }

        markmapRef.current = Markmap.create(
          svgRef.current,
          {
            duration: 0,
            maxWidth: 0,
            initialExpandLevel: 2,
          },
          root,
        )
      }

      return () => {
        if (markmapRef.current) {
          markmapRef.current.destroy()
          markmapRef.current = null
        }
      }
    }, [content])

    const handleClick = () => {
      setIsClicked(true)
      setTimeout(() => setIsClicked(false), 150)
      setIsViewerOpen(true)
    }

    const handleCloseViewer = () => {
      setIsViewerOpen(false)
    }

    return (
      <>
        <CommonBlock
          upperText={upperText}
          senderId={senderId}
          onClick={handleClick}
          blockStyle={{
            transform: isClicked ? 'scale(0.98)' : 'scale(1)',
            transition: 'transform 0.15s ease-out',
            cursor: 'pointer',
          }}
        >
          {/* 主体：思维导图预览 */}
          <div
            style={{
              width: '100%',
              height: '200px', // 增加高度
              backgroundColor: '#f8f9fa',
              borderRadius: '12px',
              overflow: 'hidden',
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg
              ref={svgRef}
              style={{
                width: '100%',
                height: '100%',
              }}
            />
            {/* 悬浮提示 */}
            <div
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                opacity: 0.8,
              }}
            >
              点击查看完整思维导图
            </div>
          </div>

          {/* 底部：标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginTop: '8px',
              fontSize: '13px',
              color: '#8e8e93',
            }}
          >
            <span>{title}</span>
          </div>
        </CommonBlock>

        <MarkmapViewer
          content={content}
          title={title}
          isOpen={isViewerOpen}
          onClose={handleCloseViewer}
        />
      </>
    )
  },
)
