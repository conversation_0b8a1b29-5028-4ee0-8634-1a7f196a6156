import { useCallback, useEffect, useState } from 'react'
import { TaskPanel } from '@/pages/task-panel'
import { useTaskPanelIntegration } from '@/pages/task-panel/hooks/useTaskPanelIntegration'
import { conversationListAtom, EConversationType } from '@/stateV2/conversation'
import { historyMessageService } from '@/tina/services/history-message'
import {
  IonContent,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
} from '@ionic/react'
import { useAtom } from 'jotai'
import { twJoin } from 'tailwind-merge'
import { useConversationAPI } from '../context'
import ConversationItem from './ConversationItem'
import ScrollToBottomButton from './ScrollToBottomButton'
import { useScrollToBottom } from './useScrollToBottom'

const ConversationList = () => {
  const { listRef, conversationId, loadHistoryMessages } = useConversationAPI()
  const [conversationList] = useAtom(conversationListAtom(conversationId))
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [hasMoreHistory, setHasMoreHistory] = useState(true)
  // 防止在滚动位置恢复过程中触发加载的标志
  const [isRestoringPosition, setIsRestoringPosition] = useState(false)

  // 任务面板集成
  const {
    taskPanelRef,
    handleScroll: handleTaskPanelScroll,
    shouldShowTaskPanel,
  } = useTaskPanelIntegration(conversationId)

  // 滚动到底部功能
  const {
    showScrollButton,
    handleScroll: handleScrollEvent,
    scrollToBottom,
    autoScrollIfAtBottom,
  } = useScrollToBottom({
    listRef,
  })

  // 合并滚动事件处理
  const handleScroll = useCallback(() => {
    handleScrollEvent()
    handleTaskPanelScroll()
  }, [handleScrollEvent, handleTaskPanelScroll])

  // 滚动到底部按钮点击处理
  const handleScrollToBottomClick = useCallback(() => {
    scrollToBottom()
  }, [scrollToBottom])

  // 将 autoScrollIfAtBottom 方法和恢复状态暴露给 listRef，供 context 调用
  useEffect(() => {
    if (listRef.current) {
      // 为 IonContent 添加自定义方法和状态
      ;(listRef.current as any).autoScrollIfAtBottom = autoScrollIfAtBottom
      ;(listRef.current as any).isRestoringPosition = isRestoringPosition
    }
  }, [autoScrollIfAtBottom, listRef, isRestoringPosition])

  // 处理历史消息加载 - 带滚动位置保持
  const handleLoadMore = useCallback(async () => {
    console.log('🔄 [ConversationList] 开始加载历史消息')
    setIsLoadingHistory(true)

    // 记录加载前的滚动位置信息
    let scrollPositionInfo: {
      scrollTop: number
      scrollHeight: number
      clientHeight: number
    } | null = null

    try {
      // 获取当前滚动位置
      if (listRef.current) {
        const scrollElement = await listRef.current.getScrollElement()
        if (scrollElement) {
          scrollPositionInfo = {
            scrollTop: scrollElement.scrollTop,
            scrollHeight: scrollElement.scrollHeight,
            clientHeight: scrollElement.clientHeight,
          }
          console.log(
            '📍 [ConversationList] 记录加载前滚动位置:',
            scrollPositionInfo,
          )
        }
      }

      const success = await loadHistoryMessages?.()
      if (!success) {
        setHasMoreHistory(false)
        return
      }

      // 恢复滚动位置 - 使用正确的 Ionic 方法和时机
      if (scrollPositionInfo && listRef.current) {
        setIsRestoringPosition(true)

        // 等待下一个事件循环，确保 React 状态更新完成
        setTimeout(async () => {
          try {
            if (!listRef.current) {
              setIsRestoringPosition(false)
              return
            }

            // 使用 Ionic 的 getScrollElement 方法
            const scrollElement = await listRef.current.getScrollElement()
            if (!scrollElement) {
              setIsRestoringPosition(false)
              return
            }

            // 等待 DOM 完全更新
            await new Promise((resolve) => requestAnimationFrame(resolve))

            const newScrollHeight = scrollElement.scrollHeight
            const heightDifference =
              newScrollHeight - scrollPositionInfo!.scrollHeight

            // 只有当高度确实发生变化时才恢复位置
            if (heightDifference > 0) {
              const targetScrollTop =
                scrollPositionInfo!.scrollTop + heightDifference
              const maxScrollTop = newScrollHeight - scrollElement.clientHeight
              const safeScrollTop = Math.min(
                Math.max(0, targetScrollTop),
                maxScrollTop,
              )

              console.log('📍 [ConversationList] 恢复滚动位置 (Ionic 方法):', {
                原始位置: scrollPositionInfo!.scrollTop,
                高度差异: heightDifference,
                目标位置: targetScrollTop,
                最大滚动位置: maxScrollTop,
                安全位置: safeScrollTop,
                原始高度: scrollPositionInfo!.scrollHeight,
                新高度: newScrollHeight,
              })

              // Ionic 的 scrollToPoint 似乎有问题，直接使用原生 DOM 方法
              console.log('📍 [ConversationList] 使用原生 DOM 设置滚动位置')
              // scrollElement.scrollTop = safeScrollTop
              await listRef.current.scrollToPoint(0, safeScrollTop, 0)

              // 立即验证并修正
              setTimeout(async () => {
                const verifyElement = await listRef.current!.getScrollElement()
                const actualScrollTop = verifyElement.scrollTop
                console.log('📍 [ConversationList] 滚动验证 (原生 DOM):', {
                  期望位置: safeScrollTop,
                  实际位置: actualScrollTop,
                  差异: Math.abs(actualScrollTop - safeScrollTop),
                })

                // 如果位置不对，再次尝试修正
                if (Math.abs(actualScrollTop - safeScrollTop) > 10) {
                  console.log('📍 [ConversationList] 位置不准确，再次修正')
                  verifyElement.scrollTop = safeScrollTop

                  // 最终验证
                  setTimeout(() => {
                    const finalScrollTop = verifyElement.scrollTop
                    console.log('📍 [ConversationList] 最终验证:', {
                      期望位置: safeScrollTop,
                      最终位置: finalScrollTop,
                      差异: Math.abs(finalScrollTop - safeScrollTop),
                    })
                  }, 50)
                }
              }, 50)
            } else {
              console.log('📍 [ConversationList] 无需恢复位置，高度未变化')
            }

            // 重置标志
            setIsRestoringPosition(false)
          } catch (error) {
            console.warn('📍 [ConversationList] 恢复滚动位置失败:', error)
            setIsRestoringPosition(false)
          }
        }, 0) // 使用 0 延迟，让 React 状态更新完成
      } else {
        // 没有滚动位置信息时，也要确保重置标志
        setIsRestoringPosition(false)
      }
    } catch (error) {
      console.error('📜 [ConversationList] 加载历史消息失败:', error)
    } finally {
      setIsLoadingHistory(false)
    }
  }, [loadHistoryMessages])

  // 监听历史消息服务状态
  useEffect(() => {
    setHasMoreHistory(historyMessageService.hasMoreHistory())
  }, [conversationList])

  // 当对话ID变化时重置历史消息服务
  useEffect(() => {
    historyMessageService.reset()
    setHasMoreHistory(true)
    setIsLoadingHistory(false)
  }, [conversationId])

  return (
    <>
      <IonContent
        className='flex flex-1 flex-col overflow-auto'
        ref={listRef}
        scrollEvents={true}
        onIonScroll={handleScroll}
      >
        {/* 只有当存在任务时才显示任务面板 */}
        {shouldShowTaskPanel && <TaskPanel ref={taskPanelRef} slot={'fixed'} />}
        <div
          className='flex flex-1 flex-col overflow-auto bg-[#F5F5F5] p-3 pb-8 pt-10'
          id='conversation-list'
        >
          {/* 使用条件渲染而不是 disabled 属性，避免 Ionic 框架的 disabled 恢复问题 */}
          {hasMoreHistory && !isLoadingHistory && !isRestoringPosition && (
            <IonInfiniteScroll
              position='top'
              threshold='20%'
              onIonInfinite={async (event) => {
                console.log('🔄 [IonInfiniteScroll] 触发顶部加载', {
                  hasMoreHistory,
                  isLoadingHistory,
                  isRestoringPosition,
                })
                await handleLoadMore()
                setTimeout(() => event.target.complete(), 500)
              }}
            >
              <IonInfiniteScrollContent></IonInfiniteScrollContent>
            </IonInfiniteScroll>
          )}

          <div className='space-y-4 pb-10'>
            {conversationList.map((item, index) => {
              // 计算是否为最后一条实际对话消息
              // 排除不应该影响userMaybeSay显示的消息类型
              const isLastActualMessage = (() => {
                // 如果当前消息不是userMaybeSay，则不需要特殊判断
                if (item.type !== 'userMaybeSay') {
                  return false
                }

                // 查找当前消息之后是否还有实际的对话消息
                // 实际对话消息指：text, image, video, voice, transfer, redPacket等
                // 排除：notification, centerText等辅助消息，这些消息不应该影响userMaybeSay的显示
                const excludedTypes = ['notification', 'centerText']
                const remainingMessages = conversationList.slice(index + 1)
                const hasActualMessageAfter = remainingMessages.some(
                  (msg) => !excludedTypes.includes(msg.type),
                )

                return !hasActualMessageAfter
              })()

              // 只有当 userMaybeSay 消息是对话列表中的最后一条消息时才显示
              if (
                !isLastActualMessage &&
                item.type === EConversationType.userMaybeSay
              ) {
                return null
              }

              return (
                <div
                  className={twJoin('group flex flex-col space-y-4', item.role)}
                  key={item.id}
                  data-conversation-id={item.id}
                >
                  <ConversationItem data={item} />
                </div>
              )
            })}
          </div>
        </div>
        {/* 滚动到底部按钮 - 放在 IonContent 外部 */}
        <ScrollToBottomButton
          show={showScrollButton}
          onClick={handleScrollToBottomClick}
        />
      </IonContent>
    </>
  )
}

export default ConversationList
