import type { RefObject } from 'react'
import { useCallback, useState } from 'react'

interface UseScrollToBottomOptions {
  listRef: RefObject<HTMLIonContentElement>
}

export const useScrollToBottom = ({ listRef }: UseScrollToBottomOptions) => {
  const [isAtBottom, setIsAtBottom] = useState(true)

  // 检测是否在底部 - 基于 Ionic scrollToBottom() 的逻辑，使用实际 DOM 元素数据
  const checkIsAtBottom = useCallback(async () => {
    if (!listRef.current) return false

    try {
      const scrollElement = await listRef.current.getScrollElement()
      if (!scrollElement) return false

      // 从实际 DOM 元素读取当前状态
      const scrollTop = scrollElement.scrollTop
      const scrollHeight = scrollElement.scrollHeight
      const clientHeight = scrollElement.clientHeight

      // Ionic scrollToBottom() 的目标位置
      const ionicBottomPosition = scrollHeight - clientHeight

      const threshold = 10
      const distanceFromIonicBottom = Math.abs(scrollTop - ionicBottomPosition)
      return distanceFromIonicBottom <= threshold
    } catch (error) {
      console.warn('检查滚动位置失败:', error)
      return false
    }
  }, [listRef])

  // 滚动事件处理
  const handleScroll = useCallback(() => {
    checkIsAtBottom()
      .then((atBottom) => {
        // 只在状态真正改变时更新
        setIsAtBottom((prev) => (prev !== atBottom ? atBottom : prev))
      })
      .catch((error) => {
        console.warn('滚动位置检查失败:', error)
      })
  }, [checkIsAtBottom])

  // 用户点击按钮滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!listRef.current) return

    // 立即更新状态，避免按钮闪烁
    setIsAtBottom(true)

    // 滚动到底部
    listRef.current.scrollToBottom()
  }, [listRef])

  // 自动滚动（流式消息）
  const autoScrollIfAtBottom = useCallback(() => {
    if (!isAtBottom || !listRef.current) return
    // 快速滚动，减少干扰
    listRef.current.scrollToBottom()
  }, [isAtBottom, listRef])

  return {
    isAtBottom,
    showScrollButton: !isAtBottom,
    handleScroll,
    scrollToBottom,
    autoScrollIfAtBottom,
  }
}
