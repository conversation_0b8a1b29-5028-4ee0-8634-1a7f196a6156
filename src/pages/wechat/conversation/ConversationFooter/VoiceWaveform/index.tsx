import { useEffect, useState, useRef } from 'react'
import SiriWave from 'siriwave'

interface VoiceWaveformProps {
  isRecording: boolean
  audioLevels?: number[] // 真实音频级别数据
}

const VoiceWaveform = ({ isRecording, audioLevels = [] }: VoiceWaveformProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const siriWaveRef = useRef<SiriWave | null>(null)

  // 初始化 SiriWave
  useEffect(() => {
    if (containerRef.current && !siriWaveRef.current) {
      // 清空容器内容
      containerRef.current.innerHTML = ''
      
      const containerWidth = containerRef.current.offsetWidth || 280
      const containerHeight = containerRef.current.offsetHeight || 120
      
      siriWaveRef.current = new SiriWave({
        container: containerRef.current,
        width: containerWidth,
        height: containerHeight,
        style: 'ios9',
        amplitude: 1,
        speed: 0.01,
        color: '#3b82f6', // blue-500
        cover: true, // 改回 true 以确保正确填充容器
        curveDefinition: [
          { color: "255,255,255", supportLine: true },
          { color: "15, 82, 169" }, // blue
          { color: "173, 57, 76" }, // red
          { color: "173, 82, 169" }, // green
                ],
      })
    }

    return () => {
      if (siriWaveRef.current) {
        siriWaveRef.current.stop()
        // 清理 DOM
        if (containerRef.current) {
          containerRef.current.innerHTML = ''
        }
        siriWaveRef.current = null
      }
    }
  }, [])

  // 控制 SiriWave 的播放状态和参数
  useEffect(() => {
    if (!siriWaveRef.current) return

    if (isRecording) {
      siriWaveRef.current.start()
      
      // 如果有真实音频数据，根据音频级别调整振幅和速度
      if (audioLevels.length > 0) {
        const latestLevel = audioLevels[audioLevels.length - 1] || 0
        const averageLevel = audioLevels.reduce((sum, level) => sum + level, 0) / audioLevels.length
        const minLevel = Math.min(...audioLevels)
        const maxLevel = Math.max(...audioLevels)
        const levelVariance = maxLevel - minLevel
        
        // 检测静音状态：所有值都相等或变化很小（小于5）
        const isSilent = levelVariance < 5 || averageLevel < 25
        
        let amplitude, speed
        
        if (isSilent) {
          // 静音状态使用最小值
          amplitude = 0.3
          speed = 0.01
        } else {
          // 将音频级别映射到合适的振幅范围 (1 - 4)，调高有声音时的范围
          amplitude = Math.max(1.5, Math.min(6, averageLevel / 14))
          // 将音频级别映射到速度范围 (0.05 - 0.15)，调高有声音时的范围
          speed = Math.max(0.2, Math.min(0.5, latestLevel / 100))
        }
        
        siriWaveRef.current.setAmplitude(amplitude)
        siriWaveRef.current.setSpeed(speed)
      } else {
        // 没有音频数据时使用默认动画
        siriWaveRef.current.setAmplitude(1.5)
        siriWaveRef.current.setSpeed(0.05)
      }
    } else {
      siriWaveRef.current.stop()
      siriWaveRef.current.setAmplitude(0.3)
      siriWaveRef.current.setSpeed(0.01)
    }
  }, [isRecording, audioLevels])

  return (
    <div className='w-full max-w-sm'>
      {/* 主面板 */}
      <div className='relative overflow-hidden rounded-3xl bg-white/90 shadow-xl backdrop-blur-lg border border-gray-200/50 p-6'>
        {/* 录音指示器 */}
        {isRecording && (
          <div className='absolute right-4 top-4 z-10'>
            <div className='flex items-center space-x-2'>
              <div className='h-3 w-3 animate-pulse rounded-full bg-red-500'></div>
              <span className='text-sm font-semibold text-red-500'>REC</span>
            </div>
          </div>
        )}
        
        {/* 状态文字 - 顶部 */}
        <div className='text-center mb-4'>
          <p className={`text-lg font-semibold transition-colors duration-200 ${
            isRecording 
              ? 'text-blue-600' 
              : 'text-gray-700'
          }`}>
            {isRecording ? '语音识别中...' : '轻触开始录音'}
          </p>
        </div>

        {/* SiriWave 波形显示区域 - 固定高度 */}
        <div className='rounded-2xl bg-gradient-to-r from-blue-50 to-indigo-50 h-36 mb-4 overflow-hidden'>
          <div 
            ref={containerRef}
            className='w-full h-full'
          />
        </div>

        {/* 底部提示 */}
        <div className='text-center'>
        {isRecording && (
            <p className='text-sm text-gray-500'>
              上滑取消，松开识别并发送
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

export default VoiceWaveform 