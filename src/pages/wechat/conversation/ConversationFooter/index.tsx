import React, { useRef, useState, useEffect } from 'react'
import { isMobileOnly } from 'react-device-detect'
import EmojiSmileSVG from '@/assets/emoji-smile.svg?react'
import KeyboardOutlinedSVG from '@/assets/keyboard-outlined.svg?react'
import MicSVG from '@/assets/mic.svg?react'
import PlusCircleSVG from '@/assets/plus-circle.svg?react'
import { useConversationAPI } from '../context'
import BottomPopup from './BottomPopup'
import EmojiPanel from './EmojiPanel'
import Input from './Input'
import VoiceInput from './VoiceInput'
import { showToast } from '@/wechatComponents/Toast'

// 本地存储键名
const VOICE_MODE_STORAGE_KEY = 'conversation_voice_mode'

// 检查是否支持语音输入（HTTPS或本地环境）
const isVoiceInputSupported = (): boolean => {
  // HTTPS 环境总是支持
  if (location.protocol === 'https:') {
    return true
  }
  
  // HTTP 环境下，只有本地地址支持
  if (location.protocol === 'http:') {
    const hostname = location.hostname
    return hostname === 'localhost' || 
           hostname === '127.0.0.1' || 
           hostname.startsWith('192.168.') || 
           hostname.startsWith('10.') || 
           hostname.startsWith('172.')
  }
  
  return false
}

// 从本地存储获取语音模式设置，默认为 true（如果支持的话）
const getVoiceModeFromStorage = (): boolean => {
  try {
    const stored = localStorage.getItem(VOICE_MODE_STORAGE_KEY)
    const defaultMode = isVoiceInputSupported() // 只有在支持时才默认使用语音模式
    return stored !== null ? JSON.parse(stored) : defaultMode
  } catch (error) {
    console.warn('读取语音模式设置失败:', error)
    return isVoiceInputSupported() // 只有在支持时才默认使用语音模式
  }
}

// 保存语音模式设置到本地存储
const saveVoiceModeToStorage = (isVoiceMode: boolean): void => {
  try {
    localStorage.setItem(VOICE_MODE_STORAGE_KEY, JSON.stringify(isVoiceMode))
  } catch (error) {
    console.warn('保存语音模式设置失败:', error)
  }
}

const ConversationFooter = () => {
  const [showEmojiPanel, setShowEmojiPanel] = useState(false)
  const [isVoiceMode, setIsVoiceMode] = useState(getVoiceModeFromStorage)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { sendImageMessage, sendTextMessage } = useConversationAPI()

  // 监听 isVoiceMode 变化，保存到本地存储
  useEffect(() => {
    saveVoiceModeToStorage(isVoiceMode)
  }, [isVoiceMode])

  const inputComponentProps = isMobileOnly
    ? {
        showEmojiPanel,
        setShowEmojiPanel,
      }
    : {}

  const handleImageSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64 = e.target?.result as string
        sendImageMessage(base64, file)
      }
      reader.readAsDataURL(file)
    }
    // 清空文件输入，允许重复选择同一文件
    event.target.value = ''
  }

  // 切换输入模式（文本/语音）
  const toggleInputMode = () => {
    // 检查是否支持语音输入
    if (!isVoiceMode && !isVoiceInputSupported()) {
      // 当前是文本模式，尝试切换到语音模式，但不支持语音输入
      showToast({
        type: 'error',
        content: '语音功能仅支持 HTTPS 安全环境'
      })
      return
    }
    
    setIsVoiceMode(!isVoiceMode)
    // 切换到文本模式时关闭表情面板
    if (isVoiceMode) {
      setShowEmojiPanel(false)
    }
  }

  // 处理语音识别结果
  const handleVoiceResult = (text: string) => {
    console.log('收到语音识别结果:', text)
    if (text.trim()) {
      sendTextMessage('<voice_message>'+text.trim()+'</voice_message>')
      console.log('发送语音消息:', text.trim())
    } else {
      // 识别结果为空，显示提示
      showToast({
        type: 'error',
        content: '未识别到内容'
      })
    }
  }

  // 处理语音错误
  const handleVoiceError = (error: string) => {
    console.error('语音录音错误:', error)
    // 可以在这里显示错误提示
  }

  return (
    <div className='flex flex-col'>
      <div className='flex flex-col border-t bg-[#F6F6F6] p-2'>
        <div className='flex w-full items-end space-x-1'>
          {/* 语音/键盘切换按钮 */}
          {isVoiceMode && isVoiceInputSupported() ? (
            <KeyboardOutlinedSVG
              fill='#000'
              className='h-8 w-8 cursor-pointer p-1'
              onClick={toggleInputMode}
            />
          ) : (
            <MicSVG 
              fill='#000' 
              className='h-8 w-8 cursor-pointer p-1.5' 
              onClick={toggleInputMode}
            />
          )}
          
          {/* 输入区域 */}
          {isVoiceMode && isVoiceInputSupported() ? (
            <VoiceInput
              onResult={handleVoiceResult}
              onError={handleVoiceError}
            />
          ) : (
            <Input {...inputComponentProps} />
          )}
          
          {/* 表情和添加按钮 */}
          {!(isVoiceMode && isVoiceInputSupported()) ? (
            // 文本模式：显示表情和添加按钮
            <>
              {showEmojiPanel ? (
                <KeyboardOutlinedSVG
                  fill='#000'
                  className='h-8 w-8 cursor-pointer p-0.5'
                  onClick={() => setShowEmojiPanel((v) => !v)}
                />
              ) : (
                <EmojiSmileSVG
                  fill='#000'
                  className='h-8 w-8 cursor-pointer p-1'
                  onClick={() => setShowEmojiPanel((v) => !v)}
                />
              )}
              <PlusCircleSVG
                fill='#000'
                className='h-8 w-8 cursor-pointer p-1'
                onClick={handleImageSelect}
              />
            </>
          ) : (
            // 语音模式：只显示添加按钮
            <PlusCircleSVG
              fill='#000'
              className='h-8 w-8 cursor-pointer p-1'
              onClick={handleImageSelect}
            />
          )}
          
          <input
            ref={fileInputRef}
            type='file'
            accept='image/*'
            style={{ display: 'none' }}
            onChange={handleFileChange}
          />
        </div>
      </div>
      
      {/* 表情面板 - 仅在文本模式显示 */}
      {!(isVoiceMode && isVoiceInputSupported()) && (
        <BottomPopup show={showEmojiPanel}>
          <EmojiPanel
            showEmojiPanel={showEmojiPanel}
            setShowEmojiPanel={setShowEmojiPanel}
          />
        </BottomPopup>
      )}
    </div>
  )
}

export default ConversationFooter
