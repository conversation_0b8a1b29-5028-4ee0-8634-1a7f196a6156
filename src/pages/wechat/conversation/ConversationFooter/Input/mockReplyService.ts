import type { IDemoService } from '../../demoService'

// Mock 数据
const MOCK_TEXT_REPLIES = [
  '好的，我知道了！',
  '这个想法很不错呢～',
  '让我想想...',
  '哈哈哈，太有趣了！',
  '我觉得可以试试看',
  '嗯嗯，有道理',
  '这样啊，学到了！',
  '确实是这样的',
  '我也是这么想的',
  '太棒了！',
]

const MOCK_VOICE_REPLIES = [
  { duration: 3, text: '好的好的' },
  { duration: 5, text: '我觉得这个想法很不错' },
  { duration: 2, text: '哈哈' },
  { duration: 4, text: '让我想想看' },
  { duration: 6, text: '这个确实很有意思呢' },
]

const MOCK_IMAGES = [
  { src: '/src/assets/user-avatar-1.jpg' },
  { src: '/src/assets/user-avatar-4.jpg' },
  { src: '/src/assets/user-avatar-5.jpg' },
  { src: '/src/assets/user-avatar-6.jpg' },
]

const MOCK_RED_PACKETS = [
  { amount: '8.88', note: '恭喜发财' },
  { amount: '66.6', note: '六六大顺' },
  { amount: '188', note: '一路发发' },
  { amount: '520', note: '我爱你' },
]

const MOCK_TRANSFERS = [
  { amount: '100', note: '还钱' },
  { amount: '50', note: '午饭钱' },
  { amount: '200', note: '生活费' },
  { amount: '88', note: '红包' },
]

const MOCK_PERSONAL_CARDS = [
  {
    nickname: '小明',
    avatarInfo: { src: '/src/assets/user-avatar-7.jpg' },
  },
  {
    nickname: '小红',
    avatarInfo: { src: '/src/assets/user-avatar-8.jpg' },
  },
  {
    nickname: '小李',
    avatarInfo: { src: '/src/assets/user-avatar-10.jpg' },
  },
]

const MOCK_NEWS_REPLIES = [
  {
    title: 'AI技术新突破：ChatGPT-4发布',
    description: 'OpenAI发布了最新的ChatGPT-4模型，在多项任务上表现出色...',
    imageUrl: '/src/assets/user-avatar-1.jpg',
    source: '科技日报',
    url: 'https://example.com/news1',
  },
  {
    title: '新能源汽车销量创新高',
    description: '2024年第一季度新能源汽车销量同比增长45%...',
    imageUrl: '/src/assets/user-avatar-4.jpg',
    source: '财经新闻',
    url: 'https://example.com/news2',
  },
]

const MOCK_MARKDOWN_REPLIES = [
  {
    markdownContent: `# 今日学习总结

## 主要内容
- **React Hooks**: 学习了useState和useEffect的使用
- **TypeScript**: 掌握了基本的类型定义
- **CSS**: 练习了Flexbox布局

## 收获
今天的学习让我对前端开发有了更深的理解！

\`\`\`javascript
const [count, setCount] = useState(0);
\`\`\``,
    summary: '今日学习总结',
  },
  {
    markdownContent: `# 周末计划

## 学习计划
1. 完成React项目
2. 阅读技术文档
3. 练习算法题

## 娱乐安排
- 看电影
- 和朋友聚餐
- 户外运动

> 劳逸结合，效率更高！`,
    summary: '周末计划安排',
  },
]

/**
 * 模拟回复服务
 * 根据用户输入的第一个字符生成不同类型的模拟回复
 */
export class MockReplyService {
  constructor(
    private demoService: IDemoService,
    private sendNotification: (
      notificationId: string,
      text: string,
      icon?: string,
      hideAfter?: number,
    ) => void,
    private updateNotification: (
      notificationId: string,
      text: string,
      icon?: string,
    ) => void,
  ) {}

  /**
   * 生成模拟回复,
   * @param userMessage 用户输入的消息 '/ + 数字', 如 /1
   * @returns {boolean} 是否生成了模拟回复
   */
  generateMockReply(userMessage: string, history): boolean {
    // 正则匹配斜杠加数字，如果匹配上数字，则继续，否则返回
    const match = /^\/(\d)$/.exec(userMessage)
    if (!match) return false
    const delay = Math.random() * 1000 + 10 // 1-2秒随机延迟

    setTimeout(() => {
      const num = match[1]
      switch (num) {
        case '0':
          // Markdown 回复
          const markdownReply =
            MOCK_MARKDOWN_REPLIES[
              Math.floor(Math.random() * MOCK_MARKDOWN_REPLIES.length)
            ]
          this.demoService.sendFriendMarkdown(
            markdownReply.markdownContent,
            markdownReply.summary,
          )
          break

        case '1':
          // 文本回复
          const textReply =
            MOCK_TEXT_REPLIES[
              Math.floor(Math.random() * MOCK_TEXT_REPLIES.length)
            ]
          this.demoService.sendFriendMessage(textReply)
          break

        case '2':
          // 语音回复
          const voiceReply =
            MOCK_VOICE_REPLIES[
              Math.floor(Math.random() * MOCK_VOICE_REPLIES.length)
            ]
          this.demoService.sendFriendVoice(voiceReply.duration, voiceReply.text)
          break

        case '3':
          // 图片回复
          const imageReply =
            MOCK_IMAGES[Math.floor(Math.random() * MOCK_IMAGES.length)]
          this.demoService.sendFriendImage(imageReply.src)
          break

        case '4':
          // 红包回复
          const redPacketReply =
            MOCK_RED_PACKETS[
              Math.floor(Math.random() * MOCK_RED_PACKETS.length)
            ]
          this.demoService.sendFriendRedPacket(
            redPacketReply.amount,
            redPacketReply.note,
          )
          break

        case '5':
          // 转账回复
          const transferReply =
            MOCK_TRANSFERS[Math.floor(Math.random() * MOCK_TRANSFERS.length)]
          this.demoService.sendFriendTransfer(
            transferReply.amount,
            transferReply.note,
          )
          break

        case '6':
          // 个人名片回复
          const cardReply =
            MOCK_PERSONAL_CARDS[
              Math.floor(Math.random() * MOCK_PERSONAL_CARDS.length)
            ]
          this.demoService.sendFriendPersonalCard(
            cardReply.nickname,
            cardReply.avatarInfo,
          )
          break

        case '7':
          // 新闻回复
          const newsReply =
            MOCK_NEWS_REPLIES[
              Math.floor(Math.random() * MOCK_NEWS_REPLIES.length)
            ]
          this.demoService.sendFriendNews(
            newsReply.title,
            newsReply.description,
            newsReply.imageUrl,
            newsReply.source,
            newsReply.url,
          )
          break

        case '8':
          // 通知状态回复
          this.sendNotificationReply()
          break
        case '9':
          // 跳转到 /task
          break
        default:
          // 默认文本回复
          this.sendNotificationReply()
          break
      }
    }, delay)
    return true
  }

  /**
   * 发送通知回复
   */
  sendNotificationReply() {
    const notificationId = `notification-${Date.now()}`
    this.sendNotification(
      notificationId,
      '小天正在思考..',
      'fa-solid fa-user-astronaut',
      3000,
    )

    // 2秒后更新通知
    setTimeout(() => {
      this.updateNotification(
        notificationId,
        '小天正在输入..',
        'fa-solid fa-pen',
      )
    }, 1500)
  }
}
