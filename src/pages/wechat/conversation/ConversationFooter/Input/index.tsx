import {
  type Dispatch,
  memo,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { isMobileOnly } from 'react-device-detect'
import { useHistory } from 'react-router-dom'
import { useAtom } from 'jotai'
import { useConversationAPI } from '../../context'
import { MockReplyService } from './mockReplyService'
import buttonSendModeAtom from '@/stateV2/buttonSendModeAtom'

type Props = {
  showEmojiPanel?: boolean
  setShowEmojiPanel?: Dispatch<SetStateAction<boolean>>
  value?: string
  onChange?: (value: string) => void
  onSend?: () => void
}

const Input = ({ showEmojiPanel, setShowEmojiPanel, value, onChange, onSend }: Props) => {
  const [inputValue, setInputValue] = useState('')
  const [buttonSendMode] = useAtom(buttonSendModeAtom)

  // Use external value if provided, otherwise use internal state
  const currentValue = value !== undefined ? value : inputValue
  const handleValueChange = onChange || setInputValue

  // 自动调整textarea高度
  const adjustTextareaHeight = useCallback(() => {
    const textarea = inputRef.current
    if (textarea) {
      // 先重置高度为auto，让浏览器计算实际需要的高度
      textarea.style.height = 'auto'
      // 计算实际需要的高度
      const scrollHeight = textarea.scrollHeight
      // 设置高度，最小32px，最大120px
      const newHeight = Math.max(32, Math.min(scrollHeight, 120))
      textarea.style.height = `${newHeight}px`
    }
  }, [])

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleValueChange(e.target.value)
    // 延迟调整高度，确保DOM更新完成
    setTimeout(() => adjustTextareaHeight(), 0)
  }, [handleValueChange, adjustTextareaHeight])

  // 监听currentValue变化，自动调整高度
  useEffect(() => {
    adjustTextareaHeight()
  }, [currentValue, adjustTextareaHeight])
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const {
    sendTextMessage,
    sendNotification,
    updateNotification,
    demoService,
    listRef,
  } = useConversationAPI()

  // 收起键盘的辅助函数
  const dismissKeyboard = useCallback(() => {
    inputRef.current?.blur()
  }, [])

  // 创建模拟回复服务实例
  const mockReplyService = useMemo(() => {
    if (!demoService) return null
    return new MockReplyService(
      demoService,
      sendNotification,
      updateNotification,
    )
  }, [demoService, sendNotification, updateNotification])

  const history = useHistory()

  const handleSend = useCallback(() => {
    // Use external onSend if provided, otherwise use internal logic
    if (onSend) {
      onSend()
      dismissKeyboard()
      // 重置textarea高度
      setTimeout(() => adjustTextareaHeight(), 0)
      return
    }

    let content = currentValue.trim()
    if (content) {
      // 模拟回复
      if (!mockReplyService?.generateMockReply(content, history)) {
        // 发送用户消息
        sendTextMessage(content)
        mockReplyService?.sendNotificationReply()
      }

      // 清空输入框
      handleValueChange('')
      dismissKeyboard()
      // 重置textarea高度
      setTimeout(() => adjustTextareaHeight(), 0)
    }
  }, [
    currentValue,
    onSend,
    mockReplyService,
    history,
    sendTextMessage,
    dismissKeyboard,
    handleValueChange,
    adjustTextareaHeight,
  ])

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter') {
        if (buttonSendMode) {
          // 按钮发送模式：回车键换行，不发送
          return
        } else {
          // 回车发送模式：回车键发送
          event.preventDefault()
          handleSend()
        }
      }
    },
    [buttonSendMode, handleSend],
  )

  const handleFocus = () => {
    if (isMobileOnly && showEmojiPanel) {
      setShowEmojiPanel?.(false)
    }
    // 移动设备：使用两阶段滚动策略
    // 第一阶段：立即滚动，确保消息可见
    listRef.current?.scrollToBottom(100)

    // 第二阶段：延迟滚动，等待键盘收起后再次调整位置
    setTimeout(() => {
      if (listRef.current) {
        // 检查是否仍然需要滚动
        const checkAndScroll = async () => {
          try {
            const scrollElement = await listRef.current!.getScrollElement()
            const scrollTop = scrollElement.scrollTop
            const scrollHeight = scrollElement.scrollHeight
            const clientHeight = scrollElement.clientHeight
            const ionicBottomPosition = scrollHeight - clientHeight
            const distanceFromBottom = Math.abs(scrollTop - ionicBottomPosition)

            // 如果不在底部，再次滚动
            if (distanceFromBottom > 10) {
              listRef.current!.scrollToBottom(200)
            }
          } catch (error) {
            console.warn('键盘收起后滚动检查失败:', error)
          }
        }

        checkAndScroll()
      }
    }, 250) // 等待键盘收起动画完成
  }

  return (
    <div className='flex min-w-0 flex-1 items-center'>
      <textarea
        ref={inputRef}
        id='conversation-input'
        value={currentValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        className='w-full resize-none rounded bg-white px-2 py-1 caret-wechatBrand-3 focus:outline-none scrollbar-hide'
        placeholder='输入消息...'
        rows={1}
        style={{ minHeight: '32px', maxHeight: '120px' }}
      />
    </div>
  )
}

export default memo(Input)
