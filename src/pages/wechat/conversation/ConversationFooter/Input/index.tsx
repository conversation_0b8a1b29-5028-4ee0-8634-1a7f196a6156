import {
  type Dispatch,
  memo,
  type SetStateAction,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react'
import { isMobileOnly } from 'react-device-detect'
import { useHistory } from 'react-router-dom'
import { useConversationAPI } from '../../context'
import { MockReplyService } from './mockReplyService'

type Props = {
  showEmojiPanel?: boolean
  setShowEmojiPanel?: Dispatch<SetStateAction<boolean>>
}

const Input = ({ showEmojiPanel, setShowEmojiPanel }: Props) => {
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const {
    sendTextMessage,
    sendNotification,
    updateNotification,
    demoService,
    listRef,
  } = useConversationAPI()

  // 收起键盘的辅助函数
  const dismissKeyboard = useCallback(() => {
    inputRef.current?.blur()
  }, [])

  // 创建模拟回复服务实例
  const mockReplyService = useMemo(() => {
    if (!demoService) return null
    return new MockReplyService(
      demoService,
      sendNotification,
      updateNotification,
    )
  }, [demoService, sendNotification, updateNotification])

  const history = useHistory()

  const handleSend = useCallback(() => {
    let content = inputValue.trim()
    if (content) {
      // 模拟回复
      if (!mockReplyService?.generateMockReply(content, history)) {
        // 发送用户消息
        sendTextMessage(content)
        mockReplyService.sendNotificationReply()
      }

      // 清空输入框
      setInputValue('')
      dismissKeyboard()
    }
  }, [
    inputValue,
    mockReplyService,
    history,
    sendTextMessage,
    dismissKeyboard,
    listRef,
  ])

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault()
        handleSend()
      }
    },
    [handleSend],
  )

  const handleFocus = () => {
    if (isMobileOnly && showEmojiPanel) {
      setShowEmojiPanel?.(false)
    }
    // 移动设备：使用两阶段滚动策略
    // 第一阶段：立即滚动，确保消息可见
    listRef.current?.scrollToBottom(100)

    // 第二阶段：延迟滚动，等待键盘收起后再次调整位置
    setTimeout(() => {
      if (listRef.current) {
        // 检查是否仍然需要滚动
        const checkAndScroll = async () => {
          try {
            const scrollElement = await listRef.current!.getScrollElement()
            const scrollTop = scrollElement.scrollTop
            const scrollHeight = scrollElement.scrollHeight
            const clientHeight = scrollElement.clientHeight
            const ionicBottomPosition = scrollHeight - clientHeight
            const distanceFromBottom = Math.abs(scrollTop - ionicBottomPosition)

            // 如果不在底部，再次滚动
            if (distanceFromBottom > 10) {
              listRef.current!.scrollToBottom(200)
            }
          } catch (error) {
            console.warn('键盘收起后滚动检查失败:', error)
          }
        }

        checkAndScroll()
      }
    }, 250) // 等待键盘收起动画完成
  }

  return (
    <div className='min-w-0 flex-1'>
      <input
        ref={inputRef}
        id='conversation-input'
        type='text'
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        className='w-full rounded bg-white px-2 py-1 caret-wechatBrand-3 focus:outline-none'
        placeholder='输入消息...'
        enterKeyHint='send'
      />
    </div>
  )
}

export default memo(Input)
