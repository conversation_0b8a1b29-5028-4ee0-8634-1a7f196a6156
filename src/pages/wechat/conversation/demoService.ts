import { fromLastGenerateUpperText } from '@/stateV2/conversation/helpers'
import type { TConversationItem } from '@/stateV2/conversation/typing'
import { EConversationType } from '@/stateV2/conversation/typing'
import { nanoid } from 'nanoid'

/**
 * 演示服务 - 用于开发和演示目的的消息发送方法
 * 这些方法不是核心功能，主要用于测试和演示不同类型的消息
 */

export interface IDemoService {
  sendFriendMessage: (text: string) => void
  sendFriendVoice: (duration: number, stt?: string) => void
  sendFriendImage: (imageInfo: any) => void
  sendFriendRedPacket: (amount: string, note: string) => void
  sendFriendTransfer: (amount: string, note: string) => void
  sendFriendPersonalCard: (nickname: string, avatarInfo: any) => void
  sendTickleText: (friendId: string) => void
  sendTransfer: (
    data: Omit<any, 'id' | 'sendTimestamp' | 'upperText' | 'type'>,
  ) => void
  sendRedPacketAcceptedReply: (redPacketId: string) => void
  sendFriendNews: (
    title: string,
    description: string,
    imageUrl: string,
    source: string,
    url: string,
  ) => void
  sendFriendMarkdown: (markdownContent: string, summary?: string) => void
  sendFriendMarkmap: (title: string, content: string) => void
}

/**
 * 创建演示服务实例
 * @param setConversationList 设置对话列表的函数
 * @param scrollConversationListToBtm 滚动到底部的函数
 * @returns 演示服务实例
 */
export function createDemoService(
  setConversationList: React.Dispatch<
    React.SetStateAction<TConversationItem[]>
  >,
  scrollConversationListToBtm: () => void,
): IDemoService {
  const addMessageToList = (
    messageFactory: (prev: TConversationItem[]) => TConversationItem,
  ) => {
    setConversationList((prev: TConversationItem[]) => {
      const newMessage = messageFactory(prev)
      return [...prev, newMessage]
    })
    scrollConversationListToBtm()
  }

  const sendFriendMessage = (text: string) => {
    if (!text.trim()) return

    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.text,
        role: 'friend',
        textContent: [
          {
            text: text.trim(),
          },
        ],
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendVoice = (duration: number, stt?: string) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.voice,
        role: 'friend',
        duration,
        isRead: false,
        showStt: !!stt,
        stt,
        originalStt: stt,
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendImage = (imageInfo: any) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.image,
        role: 'friend',
        imageInfo,
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendRedPacket = (amount: string, note: string) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.redPacket,
        role: 'friend',
        amount,
        note,
        redPacketStatus: 'awaiting',
        originalSender: 'friend',
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendTransfer = (amount: string, note: string) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.transfer,
        role: 'friend',
        amount,
        note,
        transferStatus: 'awaiting',
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendPersonalCard = (nickname: string, avatarInfo: any) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.personalCard,
        role: 'friend',
        nickname,
        avatarInfo,
        id: nanoid(8),
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendTickleText = (friendId: string) => {
    // 这个方法比较复杂，暂时保留简化版本
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.centerText,
        id: nanoid(8),
        role: 'mine',
        simpleContent: '你拍了拍对方',
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendTransfer = (data: Parameters<IDemoService['sendTransfer']>[0]) => {
    setConversationList((prev: TConversationItem[]) => {
      const currentTimestamp = new Date().toISOString()
      return [
        ...prev,
        {
          type: EConversationType.transfer,
          id: nanoid(8),
          timestamp: currentTimestamp,
          upperText: fromLastGenerateUpperText(
            currentTimestamp,
            prev[prev.length - 1]?.timestamp,
          ),
          ...data,
        },
      ] as TConversationItem[]
    })
  }

  const sendRedPacketAcceptedReply = (redPacketId: string) => {
    setConversationList((prev: TConversationItem[]) => {
      const currentTimestamp = new Date().toISOString()
      return [
        ...prev,
        {
          type: EConversationType.redPacketAcceptedReply,
          id: nanoid(8),
          timestamp: currentTimestamp,
          upperText: fromLastGenerateUpperText(
            currentTimestamp,
            prev[prev.length - 1]?.timestamp,
          ),
          redPacketId,
        },
      ] as TConversationItem[]
    })
  }

  const sendFriendNews = (
    title: string,
    description: string,
    imageUrl: string,
    source: string,
    url: string,
  ) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.news,
        role: 'friend',
        title,
        description,
        imageUrl,
        source,
        url,
        id: `news-${Date.now()}`,
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendMarkdown = (markdownContent: string, summary?: string) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.markdown,
        role: 'friend',
        markdownContent,
        summary,
        id: `markdown-${Date.now()}`,
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  const sendFriendMarkmap = (title: string, content: string) => {
    addMessageToList((prev) => {
      const currentTimestamp = new Date().toISOString()
      const previousMessage = prev[prev.length - 1]
      return {
        type: EConversationType.markmap,
        role: 'friend',
        title,
        content,
        id: `markmap-${Date.now()}`,
        timestamp: currentTimestamp,
        upperText: fromLastGenerateUpperText(
          currentTimestamp,
          previousMessage?.timestamp,
        ),
      } as TConversationItem
    })
  }

  return {
    sendFriendMessage,
    sendFriendVoice,
    sendFriendImage,
    sendFriendRedPacket,
    sendFriendTransfer,
    sendFriendPersonalCard,
    sendTickleText,
    sendTransfer,
    sendRedPacketAcceptedReply,
    sendFriendNews,
    sendFriendMarkdown,
    sendFriendMarkmap,
  }
}
