import {
  createContext,
  type Dispatch,
  type H<PERSON>LAttributes,
  type PropsWithChildren,
  type RefObject,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTaskMessageHandler } from '@/pages/task-panel/hooks/useTaskMessageHandler'
import {
  conversationListAtom,
  EConversationType,
  fromLastGenerateUpperText,
  type TConversationItem,
} from '@/stateV2/conversation'
import { type IStateProfile } from '@/stateV2/profile'
import { StreamMessage } from '@/tina/lib/EmotionMindClient.browser.ts'
import {
  performUnifiedLogout,
  shouldPerformUnifiedLogout,
} from '@/tina/lib/auth'
import {
  generateUserMaybeSay,
  parseUserMaybeSayContent,
} from '@/tina/lib/user-maybe-say'
import { chatServiceManager } from '@/tina/services/chat-service-manager'
import { historyMessageService } from '@/tina/services/history-message'
import { useAuthStore } from '@/tina/stores/authStore'
import {
  convertStreamOutputToConversationItem,
  getToolNotificationConfig,
  updateMessageContent,
} from '@/tina/utils/stream-message-converter'
import { showToast } from '@/wechatComponents/Toast'
import { useAtom } from 'jotai'
import { nanoid } from 'nanoid'
import { useParams } from 'react-router-dom'
import { Descendant } from 'slate'
import { TextStreamParser } from '@/utils/TextStreamParser'
import { createDemoService } from './demoService'

type InputMode = HTMLAttributes<HTMLDivElement>['inputMode']

interface IConversationAPIContext {
  conversationId: IStateProfile['id']
  listRef: RefObject<HTMLIonContentElement>
  scrollConversationListToBtm: () => void
  sendTextMessage: (text: string) => void
  sendImageMessage: (base64: string, file: File) => void
  resendMessage: (messageId: string) => void
  mobileInputMode: InputMode
  setMobileInputMode: Dispatch<SetStateAction<InputMode>>
  previousMobileInputMode: InputMode
  sendNotification: (
    notificationId: string,
    text: string,
    icon?: string,
    hideAfter?: number,
    extraClassName?: string,
  ) => void
  updateNotification: (
    notificationId: string,
    text: string,
    icon?: string,
  ) => void
  hideNotification: (notificationId: string) => void
  connectionStatus: string
  loadHistoryMessages: (beforeTimestamp?: number) => Promise<boolean>
  // 演示服务 - 仅用于开发和演示
  demoService?: import('./demoService').IDemoService
}

const ConversationAPIContext = createContext<IConversationAPIContext | null>(
  null,
)

export const ConversationAPIProvider = ({ children }: PropsWithChildren) => {
  // 为每个Context实例生成唯一标识符，用于调试多实例问题
  const instanceId = useMemo(() => nanoid(6), [])
  console.log(`🏗️ [ConversationAPIProvider] 实例创建: ${instanceId}`)

  const { id: conversationId } = useParams<{ id: string }>()
  const [conversationList, setConversationList] = useAtom(
    conversationListAtom(conversationId ?? ''),
  )
  const listRef = useRef<HTMLIonContentElement>(null)
  const [mobileInputMode, setMobileInputMode] = useState<InputMode>('text')
  const [previousMobileInputMode, setPreviousMobileInputMode] =
    useState<InputMode>('text')
  const [connectionStatus, setConnectionStatus] =
    useState<string>('disconnected')

  // 创建文本流解析器实例的Map，每个消息ID对应一个解析器
  const textParsersRef = useRef<Map<string, TextStreamParser>>(new Map())

  // 跟踪当前流式消息ID，用于检测新消息开始
  const currentStreamingMessageRef = useRef<string | null>(null)

  // 智能自动滚动：只在底部时才滚动
  const scrollConversationListToBtm = useCallback(() => {
    const autoScrollIfAtBottom = (listRef.current as any)?.autoScrollIfAtBottom
    if (autoScrollIfAtBottom) {
      autoScrollIfAtBottom()
    }
  }, [])

  // 添加消息到聊天的函数
  const addMessageToChat = useCallback(
    (message: TConversationItem) => {
      setConversationList((prev: TConversationItem[]) => {
        const previousMessage = prev[prev.length - 1]
        const messageWithUpperText = {
          ...message,
          upperText: fromLastGenerateUpperText(
            message.timestamp || new Date().toISOString(),
            previousMessage?.timestamp,
          ),
        }
        return [...prev, messageWithUpperText]
      })

      // 用户发送消息时强制滚动到底部
      setTimeout(() => {
        listRef.current?.scrollToBottom(300)
      }, 50)
    },
    [setConversationList, scrollConversationListToBtm],
  )

  // 任务消息处理器
  const { handleTaskMessage } = useTaskMessageHandler(addMessageToChat)

  // 获取或创建解析器
  const getOrCreateParser = useCallback((messageId: string) => {
    let parser = textParsersRef.current.get(messageId)
    if (!parser) {
      parser = new TextStreamParser(messageId)
      textParsersRef.current.set(messageId, parser)
    }
    return parser
  }, [])

  // 提取消息内容的工具函数
  const extractMessageContent = useCallback(
    (conversationItem: TConversationItem): string => {
      switch (conversationItem.type) {
        case EConversationType.text:
          return (conversationItem.textContent?.[0] as any)?.text || ''

        case EConversationType.markdown:
          return conversationItem.markdownContent || ''

        case EConversationType.voice:
          return conversationItem.stt || ''

        default:
          return ''
      }
    },
    [],
  )

  // 通用的消息内容更新工具函数
  const updateOrAddMessage = useCallback(
    (conversationItem: TConversationItem) => {
      setConversationList((prev: TConversationItem[]) => {
        const existingIndex = prev.findIndex(
          (item: TConversationItem) => item.id === conversationItem.id,
        )

        if (existingIndex !== -1) {
          // 更新现有消息
          const newList = [...prev]
          const messageContent = extractMessageContent(conversationItem)
          newList[existingIndex] = updateMessageContent(
            newList[existingIndex],
            messageContent,
          )
          return newList
        } else {
          // 添加新消息
          return [...prev, conversationItem]
        }
      })

      // 延迟滚动，确保DOM更新完成
      setTimeout(() => {
        scrollConversationListToBtm()
      }, 50)
    },
    [setConversationList, scrollConversationListToBtm, extractMessageContent],
  )

  // 定义 sendNotification 函数（需要在 useEffect 之前定义）
  const sendNotification = useCallback(
    (
      notificationId: string,
      text: string,
      icon?: string,
      hideAfter?: number,
      extraClassName?: string,
    ) => {
      setConversationList((prev: TConversationItem[]) => {
        const currentTimestamp = new Date().toISOString()
        const previousMessage = prev[prev.length - 1]
        const newMessage: TConversationItem = {
          type: EConversationType.notification,
          id: nanoid(8),
          role: 'friend',
          timestamp: currentTimestamp,
          upperText: fromLastGenerateUpperText(
            currentTimestamp,
            previousMessage?.timestamp,
          ),
          notificationId,
          text,
          icon,
          hideAfter,
          extraClassName,
        }
        return [...prev, newMessage]
      })
      scrollConversationListToBtm()
    },
    [setConversationList, scrollConversationListToBtm],
  )

  // 检查消息是否包含 user maybe say 内容
  const checkForUserMaybeSay = useCallback((outputs: any[]): boolean => {
    return outputs.some((output) => {
      const conversationItem = convertStreamOutputToConversationItem(output)
      return conversationItem?.type === EConversationType.userMaybeSay
    })
  }, [])

  // 主动获取 user maybe say 内容
  const fetchUserMaybeSay = useCallback(
    async (messageId: string, timestamp: string) => {
      try {
        const auth = useAuthStore.getState().auth
        const userId = auth.userId

        if (!userId) {
          console.warn(
            '🚨 [fetchUserMaybeSay] 用户未登录，无法获取 user maybe say',
          )
          return
        }

        console.log('🔄 [fetchUserMaybeSay] 主动获取 user maybe say:', {
          messageId,
          userId,
        })

        const response = await generateUserMaybeSay(userId)

        if (response.data.generated && response.data.user_maybe_say) {
          console.log(
            '✅ [fetchUserMaybeSay] 成功获取 user maybe say:',
            response.data.user_maybe_say,
          )

          // 解析建议内容
          const suggestions = parseUserMaybeSayContent(
            response.data.user_maybe_say,
          )

          if (suggestions.length > 0) {
            // 创建 user maybe say 消息
            const userMaybeSayMessage: TConversationItem = {
              id: `${messageId}-user-maybe-say`,
              type: EConversationType.userMaybeSay,
              role: 'friend',
              suggestions,
              timestamp,
            }

            // 添加到对话列表
            updateOrAddMessage(userMaybeSayMessage)
            console.log(
              '✅ [fetchUserMaybeSay] 已添加 user maybe say 消息到对话列表',
            )
          }
        }
      } catch (error) {
        console.error('🚨 [fetchUserMaybeSay] 获取 user maybe say 失败:', error)
        // 静默处理错误，不影响主要功能
      }
    },
    [updateOrAddMessage],
  )

  // 使用useCallback包装回调函数，避免每次渲染时重新创建
  const handleLLMResponse = useCallback(
    (message: StreamMessage) => {
      // 只处理流式响应（finished = false），忽略完成消息（finished = true）
      if (message.finished) {
        console.log('callbacks  onLLMResponse:', message)
        if (message.content == '') {
          sendNotification(
            message.message_id,
            '小天正在找工具..',
            'fa-solid fa-screwdriver-wrench',
            -1,
          )
        }

        // 流式消息结束时，调用 finalize() 处理剩余内容
        const originalMessageId = `llm-${message.message_id}`
        const parser = textParsersRef.current.get(originalMessageId)
        if (parser) {
          const finalOutputs = parser.finalize()

          // 处理 finalize 输出的剩余内容
          for (const output of finalOutputs) {
            const conversationItem =
              convertStreamOutputToConversationItem(output)
            if (conversationItem) {
              conversationItem.timestamp = message.timestamp
              updateOrAddMessage(conversationItem)
            }
          }

          // 清理解析器
          // textParsersRef.current.delete(originalMessageId)
        }
        if (message.tool_called) {
          console.log('🔍 [handleLLMResponse]tool called 忽略 user maybe say')
          return
        }
        // 检查是否包含 user maybe say 内容，如果没有则主动获取
        const tempParser = new TextStreamParser(message.message_id)
        const tempOutputs = tempParser.processText(message.content, true)
        const hasUserMaybeSay = checkForUserMaybeSay(tempOutputs)
        if (!hasUserMaybeSay) {
          console.log(
            '🔍 [handleLLMResponse] 消息中未检测到 user maybe say，主动获取',
          )
          fetchUserMaybeSay(originalMessageId, message.timestamp)
        }

        return
      }

      if (message.content && message.delta) {
        const originalMessageId = `llm-${message.message_id}`

        // 检测是否是新的流式消息开始
        if (currentStreamingMessageRef.current !== originalMessageId) {
          console.log('🔄 [Context] 检测到新的流式消息开始')
          currentStreamingMessageRef.current = originalMessageId
        }

        // 获取或创建解析器
        const parser = getOrCreateParser(originalMessageId)

        // 使用同步方法处理文本
        const outputs = parser.processText(message.content)

        // 处理每个输出
        for (const output of outputs) {
          // 尝试使用新的转换器处理
          const conversationItem = convertStreamOutputToConversationItem(output)
          // 如果转换器无法处理，记录日志并跳过
          if (!conversationItem) {
            // console.warn('转换器无法处理的输出:', output)
            return
          }

          conversationItem.timestamp = message.timestamp

          // 使用转换器成功处理的消息
          updateOrAddMessage(conversationItem)
        }
      }
    },
    [
      conversationId,
      getOrCreateParser,
      sendNotification,
      updateOrAddMessage,
      checkForUserMaybeSay,
      fetchUserMaybeSay,
    ],
  )

  const handleTaskResponse = useCallback(
    (message: StreamMessage) => {
      console.log('📨 [Context] 收到任务响应消息:', message)
      handleTaskMessage(message)
    },
    [handleTaskMessage],
  )

  // 全局防重复处理机制 - 使用window对象存储，避免多个Context实例重复处理
  const getGlobalProcessedMessages = () => {
    if (!(window as any).__processedUserMessages) {
      ;(window as any).__processedUserMessages = new Set<string>()
    }
    return (window as any).__processedUserMessages as Set<string>
  }

  const handleUserMessage = useCallback(
    (message: StreamMessage) => {
      console.log('🔍 [handleUserMessage] 收到用户消息:', {
        messageId: message.message_id,
        content: message.content,
        timestamp: message.timestamp,
        callStack: new Error().stack?.split('\n').slice(1, 3).join('\n'),
      })

      if (!message.content) {
        return
      }

      // 创建消息的唯一标识符（基于消息ID和内容）
      const messageKey = `${message.message_id}-${message.content.trim()}`

      // 使用全局防重复机制
      const globalProcessedMessages = getGlobalProcessedMessages()

      // 检查是否已经处理过这条消息
      if (globalProcessedMessages.has(messageKey)) {
        console.log('🚫 [handleUserMessage] 消息已处理过，跳过:', messageKey)
        return
      }

      // 标记消息为已处理
      globalProcessedMessages.add(messageKey)

      // 清理旧的消息记录（保留最近100条）
      if (globalProcessedMessages.size > 100) {
        const entries = Array.from(globalProcessedMessages)
        const toKeep = entries.slice(-50) // 保留最近50条
        ;(window as any).__processedUserMessages = new Set(toKeep)
      }
      // 当收到真实的用户消息时，查找并替换对应的临时消息
      const messageContent = message.content.trim()
      const messageTimestamp = message.timestamp || new Date().toISOString()
      if (message.content.includes('user_online')) {
        sendNotification(
          message.message_id,
          '用户上线了',
          'fa-solid fa-screwdriver-wrench',
          -1,
        )
        return
      }

      setConversationList((prev: TConversationItem[]) => {
        // 只查找'sending'状态的消息进行替换
        // 失败的消息不应该被服务器回调替换，因为服务器不会为失败的消息发送回调
        const tempMessageIndex = prev.findIndex((item) => {
          return (
            item.type === EConversationType.text &&
            item.role === 'mine' &&
            item.sendStatus === 'sending' &&
            item.textContent &&
            // 比较消息内容 - 提取完整的文本内容进行比较
            (() => {
              const tempMessageText = item.textContent
                .map((descendant: any) => {
                  if ('text' in descendant) {
                    return descendant.text
                  }
                  return ''
                })
                .join('')
                .trim()
              // 比较时都去除首尾空白字符，处理服务器返回内容可能包含换行符的情况
              return tempMessageText === messageContent.trim()
            })()
          )
        })

        if (tempMessageIndex !== -1) {
          // 找到了对应的临时消息，替换为真实消息
          const newList = [...prev]
          const tempMessage = newList[tempMessageIndex]

          // 确保是文本类型消息才进行替换
          if (tempMessage.type === EConversationType.text) {
            // 创建真实消息，保留临时消息的ID和其他属性
            const realMessage: TConversationItem = {
              ...tempMessage,
              timestamp: messageTimestamp,
              sendStatus: 'sent' as const,
              // 更新upperText以反映真实的时间戳
              upperText: fromLastGenerateUpperText(
                messageTimestamp,
                tempMessageIndex > 0
                  ? newList[tempMessageIndex - 1]?.timestamp
                  : undefined,
              ),
            }

            newList[tempMessageIndex] = realMessage

            console.log('✅ 成功替换临时消息为真实消息:', {
              tempMessageId: tempMessage.id,
              content: messageContent,
              timestamp: messageTimestamp,
            })

            return newList
          }
        } else {
          // 没找到对应的临时消息，直接添加为新消息（支持多设备同步）
          console.log(
            '📱 未找到对应临时消息，添加为新消息（可能来自其他设备）:',
            {
              content: messageContent,
              timestamp: messageTimestamp,
            },
          )

          const currentTimestamp = messageTimestamp
          const previousMessage = prev[prev.length - 1]
          const newMessage: TConversationItem = {
            type: EConversationType.text,
            role: 'mine',
            textContent: [
              {
                text: messageContent,
              },
            ],
            id: `user-${message.message_id}`,
            timestamp: currentTimestamp,
            upperText: fromLastGenerateUpperText(
              currentTimestamp,
              previousMessage?.timestamp,
            ),
            sendStatus: 'sent' as const,
          }

          return [...prev, newMessage]
        }

        return prev
      })
    },
    [setConversationList],
  )

  const handleToolMessage = useCallback(
    (message: any) => {
      const notificationId = `notification-${Date.now()}`
      const config = getToolNotificationConfig(message.tool_name)

      sendNotification(
        notificationId,
        config.text,
        config.icon,
        config.hideAfter,
      )
    },
    [sendNotification],
  )

  const handleToolCallback = useCallback((message: any) => {
    console.log('callbacks  onToolCallback:', message)
  }, [])

  const handleError = useCallback((error: any) => {
    console.error('🚪 [ConversationAPI] callbacks onError:', error)
    // 退出导致的错误，不提示
    if (error !== 'BodyStreamBuffer was aborted') {
      showToast({ content: error.message || error, type: 'error' })
    }
    // 检查是否需要执行统一登出
    if (shouldPerformUnifiedLogout(undefined, error)) {
      console.log('🚪 [ConversationAPI] 检测到需要登出的错误')
      // 异步执行登出，不阻塞当前错误处理
      performUnifiedLogout(
        `ConversationAPI Error: ${error.message || error}`,
      ).catch((logoutError) => {
        console.error('🚪 [ConversationAPI] 统一登出执行失败:', logoutError)
      })
    }
  }, [])

  // 简化的连接状态处理 - 只负责更新 UI 状态
  const handleConnectionStatusChange = useCallback((status: string) => {
    console.log('callbacks  onConnectionStatusChange:', status)
    setConnectionStatus(status)
  }, [])

  // 使用 ref 来确保在 StrictMode 下只初始化一次
  const isInitializedRef = useRef(false)

  // 设置ChatServiceManager回调 - 确保在 StrictMode 下只执行一次
  useEffect(() => {
    // 在 StrictMode 下，useEffect 会被调用两次，使用 ref 来防止重复初始化
    if (isInitializedRef.current) {
      console.log(
        `🚫 [ConversationAPIProvider:${instanceId}] 已初始化，跳过重复初始化`,
      )
      return
    }

    const callbacks = {
      onTask: handleTaskResponse,
      onLLMResponse: handleLLMResponse,
      onUserMessage: handleUserMessage,
      onToolMessage: handleToolMessage,
      onToolCallback: handleToolCallback,
      onError: handleError,
      onConnectionStatusChange: handleConnectionStatusChange,
    }

    console.log(
      `🔗 [ConversationAPIProvider:${instanceId}] 初始化 ChatServiceManager`,
    )
    chatServiceManager.initialize(callbacks)
    isInitializedRef.current = true

    // 清理函数：组件卸载时重置标志
    return () => {
      console.log(
        `🧹 [ConversationAPIProvider:${instanceId}] 组件卸载，重置初始化标志`,
      )
      isInitializedRef.current = false
    }
  }, [
    instanceId, // 只依赖 instanceId，确保每个实例只初始化一次
    handleTaskResponse,
    handleLLMResponse,
    handleUserMessage,
    handleToolMessage,
    handleToolCallback,
    handleError,
    handleConnectionStatusChange,
  ])

  // 加载历史消息函数
  const loadHistoryMessages = useCallback(async (): Promise<boolean> => {
    try {
      console.log('📜 [ConversationContext] 开始加载历史消息', {})

      // 调用历史消息服务
      const historyMessages =
        await historyMessageService.loadHistoryMessages(20)

      if (historyMessages.length === 0) {
        console.log('📜 [ConversationContext] 没有更多历史消息')
        return false
      }

      // 将历史消息添加到对话列表的开头
      setConversationList((prev: TConversationItem[]) => {
        // 为历史消息添加upperText（时间显示）
        const messagesWithUpperText = historyMessages.map((msg, index) => {
          // 对于历史消息，前一条消息是数组中的前一个元素
          const previousMessage =
            index > 0 ? historyMessages[index - 1] : undefined
          return {
            ...msg,
            upperText: fromLastGenerateUpperText(
              msg.timestamp || '',
              previousMessage?.timestamp,
            ),
          }
        })

        // 合并历史消息和现有消息，历史消息在前，并去重
        const existingIds = new Set(prev.map((item) => item.id))
        const uniqueHistoryMessages = messagesWithUpperText.filter(
          (msg) => !existingIds.has(msg.id),
        )

        console.log('📜 [ConversationContext] 去重后的历史消息数量:', {
          原始历史消息: messagesWithUpperText.length,
          去重后历史消息: uniqueHistoryMessages.length,
          现有消息: prev.length,
        })

        return [...uniqueHistoryMessages, ...prev]
      })

      console.log(
        '📜 [ConversationContext] 成功加载历史消息',
        historyMessages.length,
      )
      return true
    } catch (error) {
      console.error('📜 [ConversationContext] 加载历史消息失败:', error)

      // 检查是否需要执行统一登出
      if (shouldPerformUnifiedLogout(undefined, error)) {
        console.log(
          '🚪 [ConversationContext] 历史消息加载失败，检测到需要登出的错误',
        )
        // 异步执行登出，不阻塞当前错误处理
        performUnifiedLogout(
          `History Message Error: ${error.message || error}`,
        ).catch((logoutError) => {
          console.error(
            '🚪 [ConversationContext] 统一登出执行失败:',
            logoutError,
          )
        })
      }

      return false
    }
  }, [setConversationList])

  // 初始化时自动加载一些历史消息
  useEffect(() => {
    const loadInitialHistory = async () => {
      // 只在对话列表为空或很少时加载初始历史消息
      if (conversationList.length <= 2) {
        try {
          console.log('📜 [ConversationContext] 自动加载初始历史消息')
          await loadHistoryMessages()
          requestAnimationFrame(async () => {
            await listRef.current.scrollToBottom()
          })
        } catch (error) {
          console.error(
            '📜 [ConversationContext] 自动加载初始历史消息失败:',
            error,
          )
        }
      }
    }

    // 延迟一点时间，确保聊天服务已经初始化
    const timeoutId = setTimeout(loadInitialHistory, 1000)

    return () => clearTimeout(timeoutId)
  }, [conversationId, loadHistoryMessages, conversationList.length]) // 依赖项

  const sendTextMessage = useCallback(
    (text: string) => {
      const messageId = nanoid(8)

      var displayText = text.trim()
      if (text.includes('<voice_message>')) {
        displayText = text
          .replace('<voice_message>', '')
          .replace('</voice_message>', '')
      }

      // 添加消息到列表，初始状态为sending
      setConversationList((prev: TConversationItem[]) => {
        const currentTimestamp = new Date().toISOString()
        const previousMessage = prev[prev.length - 1]
        const newMessage = {
          type: EConversationType.text,
          role: 'mine',
          textContent: [
            {
              text: displayText,
            },
          ],
          id: messageId,
          timestamp: currentTimestamp,
          upperText: fromLastGenerateUpperText(
            currentTimestamp,
            previousMessage?.timestamp,
          ),
          sendStatus: 'sending' as const,
        } as TConversationItem

        return [...prev, newMessage]
      })

      // 发送消息到ChatServiceManager
      chatServiceManager
        .sendMessage(text)
        .then(() => {
          // 发送成功，但不立即更新状态为'sent'
          // 因为真实的用户消息会通过 handleUserMessage 回调处理
          // 这里只是确保消息已经发送到服务器
          console.log('✅ 消息已发送到服务器，等待服务器确认:', messageId)
        })
        .catch((error) => {
          console.error('发送消息失败:', error)
          // 发送失败，更新状态
          setConversationList((prev: TConversationItem[]) => {
            return prev.map((item) =>
              item.id === messageId && item.type === EConversationType.text
                ? { ...item, sendStatus: 'failed' as const }
                : item,
            )
          })
        })

      listRef.current?.scrollToBottom()
    },
    [conversationId, setConversationList, scrollConversationListToBtm],
  )

  const sendImageMessage = useCallback(
    (base64: string, file: File) => {
      const messageId = nanoid(8)

      // 添加图片消息到列表，初始状态为sending，使用base64显示
      setConversationList((prev: TConversationItem[]) => {
        const currentTimestamp = new Date().toISOString()
        const previousMessage = prev[prev.length - 1]
        const newMessage: TConversationItem = {
          type: EConversationType.image,
          role: 'mine',
          imageInfo: base64, // 先使用base64显示
          id: messageId,
          timestamp: currentTimestamp,
          upperText: fromLastGenerateUpperText(
            currentTimestamp,
            previousMessage?.timestamp,
          ),
          sendStatus: 'sending' as const,
        }

        return [...prev, newMessage]
      })

      // 使用真实的上传接口
      chatServiceManager
        .uploadFile(file, 'chat-images')
        .then((uploadResponse: any) => {
          console.log('📤 上传响应结构:', uploadResponse)

          // 检查响应结构并获取正确的URL
          const imageUrl =
            uploadResponse?.accessUrl ||
            uploadResponse?.url ||
            uploadResponse?.data?.accessUrl ||
            uploadResponse?.data?.url

          if (!imageUrl) {
            console.error('❌ 上传响应中没有找到图片URL:', uploadResponse)
            throw new Error('上传响应中没有找到图片URL')
          }

          // 上传成功，更新图片URL和状态
          setConversationList((prev: TConversationItem[]) => {
            return prev.map((item) =>
              item.id === messageId && item.type === EConversationType.image
                ? {
                    ...item,
                    imageInfo: imageUrl, // 使用上传后的URL
                    sendStatus: 'sent' as const,
                  }
                : item,
            )
          })
          console.log('✅ 图片上传成功:', imageUrl)
        })
        .catch((error) => {
          console.error('图片上传失败:', error)
          // 上传失败，更新状态
          setConversationList((prev: TConversationItem[]) => {
            return prev.map((item) =>
              item.id === messageId && item.type === EConversationType.image
                ? { ...item, sendStatus: 'failed' as const }
                : item,
            )
          })
        })

      scrollConversationListToBtm()
    },
    [conversationId, setConversationList, scrollConversationListToBtm],
  )

  const resendMessage = useCallback(
    (originalMessageId: string) => {
      // 查找原消息
      const originalMessage = conversationList.find(
        (item) => item.id === originalMessageId,
      )
      if (!originalMessage || originalMessage.type !== EConversationType.text) {
        return
      }

      // 从Descendant[]中提取文本内容
      const messageText = originalMessage.textContent
        ?.map((descendant: Descendant) => {
          // 处理Slate格式的paragraph节点
          if (
            'type' in descendant &&
            descendant.type === 'paragraph' &&
            'children' in descendant
          ) {
            return (descendant as any).children
              .map((child: any) => {
                if ('text' in child) {
                  return child.text
                }
                return ''
              })
              .join('')
          }
          // 处理简单的文本节点
          else if ('text' in descendant) {
            return (descendant as any).text
          }
          return ''
        })
        .join('')

      if (!messageText) {
        return
      }

      // 删除原消息
      setConversationList((prev: TConversationItem[]) => {
        return prev.filter((item) => item.id !== originalMessageId)
      })

      // 重新发送消息
      sendTextMessage(messageText)
    },
    [conversationList, setConversationList, sendTextMessage],
  )

  // 创建演示服务实例
  const demoService = useMemo(
    () => createDemoService(setConversationList, scrollConversationListToBtm),
    [setConversationList, scrollConversationListToBtm],
  )

  const updateNotification = useCallback(
    (notificationId: string, text: string, icon?: string) => {
      setConversationList((prev: TConversationItem[]) => {
        const existingIndex = prev.findIndex(
          (item: TConversationItem) =>
            item.type === EConversationType.notification &&
            item.notificationId === notificationId,
        )
        if (existingIndex !== -1) {
          const newList = [...prev]
          const existingMessage = newList[existingIndex]
          if (existingMessage.type === EConversationType.notification) {
            newList[existingIndex] = {
              ...existingMessage,
              text,
              icon,
            }
          }
          return newList
        }
        return prev
      })
      scrollConversationListToBtm()
    },
    [conversationId, setConversationList, scrollConversationListToBtm],
  )

  const hideNotification = useCallback(
    (notificationId: string) => {
      setConversationList((prev: TConversationItem[]) => {
        return prev.filter(
          (item: TConversationItem) =>
            !(
              item.type === EConversationType.notification &&
              item.notificationId === notificationId
            ),
        )
      })
      scrollConversationListToBtm()
    },
    [conversationId, setConversationList, scrollConversationListToBtm],
  )

  const value: IConversationAPIContext = useMemo(() => {
    return {
      conversationId,
      listRef,
      scrollConversationListToBtm,
      sendTextMessage,
      sendImageMessage,
      resendMessage,
      mobileInputMode,
      setMobileInputMode,
      previousMobileInputMode,
      sendNotification,
      updateNotification,
      hideNotification,
      connectionStatus,
      loadHistoryMessages,
      demoService,
    }
  }, [
    conversationId,
    scrollConversationListToBtm,
    mobileInputMode,
    sendTextMessage,
    sendImageMessage,
    resendMessage,
    sendNotification,
    updateNotification,
    hideNotification,
    connectionStatus,
    loadHistoryMessages,
    demoService,
  ])

  return (
    <ConversationAPIContext.Provider value={value}>
      {children}
    </ConversationAPIContext.Provider>
  )
}

export const useConversationAPI = () => useContext(ConversationAPIContext)!
