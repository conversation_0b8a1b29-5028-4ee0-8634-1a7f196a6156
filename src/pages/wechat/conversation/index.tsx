import { useEffect, useState } from 'react'
import { TaskCenter } from '@/pages/task-center/task-center.tsx'
import { IonPage } from '@ionic/react'
import ConfirmationDialogManager from '@/components/ConfirmationDialogManager'
import ConversationFooter from './ConversationFooter'
import ConversationHeader from './ConversationHeader'
import ConversationList from './ConversationList'
import { ConversationAPIProvider } from './context'

// 内部组件，用于渲染对话内容
const ConversationContent = () => {
  return (
    <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
      <ConversationHeader />
      <ConversationList />
      <ConversationFooter />
    </div>
  )
}

const Conversation = () => {
  return (
    <>
      <TaskCenter className='mx-auto sm:max-w-md' />
      <IonPage id='main-content'>
        <ConversationAPIProvider>
          <ConversationContent />
          {/* 独立的确认对话框管理器 */}
          <ConfirmationDialogManager />
        </ConversationAPIProvider>
      </IonPage>
    </>
  )
}

export default Conversation
