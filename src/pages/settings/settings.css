/* 设置页面自定义样式 */

/* 自定义 IonToggle 样式，使其更符合原型设计 */
.settings-toggle {
  --background: #e5e7eb;
  --background-checked: linear-gradient(to right, #3b82f6, #f97316);
  --handle-background: #ffffff;
  --handle-background-checked: #ffffff;
  --handle-spacing: 2px;
  --handle-width: 20px;
  --handle-height: 20px;
  --track-width: 44px;
  --track-height: 24px;
  --border-radius: 12px;
}

/* 通知设置的渐变色 */
.settings-toggle.notifications {
  --background-checked: linear-gradient(to right, #3b82f6, #f97316);
}

/* 语音输入的渐变色 */
.settings-toggle.voice-input {
  --background-checked: linear-gradient(to right, #f97316, #eab308);
}

/* 确保开关在移动端有合适的大小 */
ion-toggle {
  --width: 44px;
  --height: 24px;
}

/* 个人信息卡片的编辑按钮样式优化 */
.edit-profile-btn {
  --background: linear-gradient(to right, #3b82f6, #1d4ed8);
  --background-hover: linear-gradient(to right, #1d4ed8, #1e40af);
  --color: white;
  --border-radius: 50%;
  --padding-start: 0;
  --padding-end: 0;
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
}

/* 用户头像的渐变背景优化 */
.user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 设置项图标容器的阴影效果 */
.setting-icon-container {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.setting-icon-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 设置项悬停效果优化 */
.setting-item:hover {
  background-color: rgba(249, 250, 251, 0.8);
  transition: background-color 0.2s ease;
}

/* 退出登录按钮的悬停效果 */
.logout-btn {
  min-height: 56px; /* 增加最小高度，符合移动端触摸标准 */
  padding: 16px 24px; /* 增加内边距 */
}

.logout-btn:hover {
  background-color: rgba(254, 242, 242, 0.8);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.logout-btn:active {
  transform: translateY(0);
  background-color: rgba(254, 226, 226, 0.9);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .setting-item {
    padding: 16px 24px;
  }

  .user-info-card {
    margin: 16px;
    padding: 20px;
  }
}
