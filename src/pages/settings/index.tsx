import { useState } from 'react'
import { performUnifiedLogout, signupAndLogin, loginWithUsernameAndPassword } from '@/tina/lib/auth.ts'
import {
  IonAvatar,
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonPage,
  IonTitle,
  IonToggle,
  IonToolbar,
} from '@ionic/react'
import {
  chatbubbleOutline,
  chevronForwardOutline,
  colorPaletteOutline,
  createOutline,
  helpCircleOutline,
  informationCircleOutline,
  logOutOutline,
  micOutline,
  notificationsOutline,
  shieldCheckmarkOutline,
  timeOutline,
  addCircleOutline,
  swapHorizontalOutline,
  sendOutline,
} from 'ionicons/icons'
import BetaInfoDialog from '@/components/BetaInfoDialog'
import WechatDialog from '../../components/WechatDialog'
import { HeaderBackButton } from '../../components/ui/back-button'
import useAuth from '../../tina/stores/authStore'
import './settings.css'
import showAvatar<PERSON>tom from '@/stateV2/showAvatarAtom'
import buttonSendMode<PERSON>tom from '@/stateV2/buttonSendModeAtom'
import { useAtom } from 'jotai'
import {
  generateRandomUsername,
  generateRandomPassword,
  createTestAccount,
  saveTestAccount,
  getTestAccounts,
  type TestAccount
} from '@/utils/testAccountManager'
import { useHistory } from 'react-router-dom'
import { conversationListAtom } from '@/stateV2/conversation'
import { mainStore } from '@/stateV2/store'
import { navigateToHome, navigateToOnboarding } from '@/utils/navigationHelper'

/**
 * 移动端设置页面组件
 * 包含用户信息、通用设置、隐私安全、关于帮助等功能
 */
export default function MobileSettings() {
  const auth = useAuth()
  const history = useHistory()
  const [showAvatar, setShowAvatar] = useAtom(showAvatarAtom)
  const [buttonSendMode, setButtonSendMode] = useAtom(buttonSendModeAtom)

  const [settings, setSettings] = useState({
    notifications: true,
    voiceInput: true,
    darkMode: false,
    showAvatar,
    buttonSendMode,
  })
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [showBetaInfo, setShowBetaInfo] = useState(false)

  // 开发测试相关状态
  const [showRegisterDialog, setShowRegisterDialog] = useState(false)
  const [isRegistering, setIsRegistering] = useState(false)
  const [showAccountSwitchDialog, setShowAccountSwitchDialog] = useState(false)
  const [testAccounts, setTestAccounts] = useState<TestAccount[]>([])
  const [isSwitchingAccount, setIsSwitchingAccount] = useState(false)

  // 从 authStore 获取用户信息
  const user = auth.user
  const userName = user?.displayName || user?.name || '用户'
  const name = user?.name || ''
  const userAvatar = userName.charAt(0).toUpperCase()

  const handleToggleChange = (key: keyof typeof settings) => {
    setSettings((prev) => {
      const newSettings = {
        ...prev,
        [key]: !prev[key],
      }
      if (key === 'showAvatar') {
        setShowAvatar(newSettings.showAvatar)
      } else if (key === 'buttonSendMode') {
        setButtonSendMode(newSettings.buttonSendMode)
      }
      return newSettings
    })
  }

  const handleLogout = () => {
    setShowLogoutDialog(true)
  }

  const handleLogoutConfirm = async () => {
    console.log('🚪 [Settings] 开始注销流程')
    setIsLoggingOut(true)

    try {
      // 使用统一登出逻辑
      await performUnifiedLogout('用户手动登出')
    } catch (error) {
      console.error('🚪 [Settings] 注销失败:', error)
      // 即使统一登出失败，也要确保页面状态正确
    }
    // 注意：不要在这里设置 setIsLoggingOut(false)，因为页面即将跳转
  }

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false)
  }

  const handleEditProfile = () => {
    // TODO: 实现编辑个人资料功能
    // console.log('编辑个人资料')
  }

  const handleCloseBetaInfo = () => {
    setShowBetaInfo(false)
  }

  const handleNavigationClick = (title: string) => {
    if (title === '关于 Tina Chat') {
      setShowBetaInfo(true)
    } else if (title === '一键注册') {
      setShowRegisterDialog(true)
    } else if (title === '账户切换') {
      const accounts = getTestAccounts()
      setTestAccounts(accounts)
      setShowAccountSwitchDialog(true)
    } else {
      // TODO: 实现其他导航功能
      // console.log(`Navigate to ${title}`)
    }
  }

  // 清除对话列表的工具函数
  const clearConversationLists = () => {
    try {
      console.log('🔧 [DevTest] 清除对话列表')

      // 清除当前用户的对话列表
      if (auth.user?.id) {
        mainStore.set(conversationListAtom(auth.user.id), [])
        console.log('🔧 [DevTest] 已清除用户对话列表:', auth.user.id)
      }

      // 清除所有可能的对话列表缓存
      // 遍历localStorage中所有以conversationList-开头的键并清除
      const keysToRemove: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('conversationList-')) {
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key)
        console.log('🔧 [DevTest] 已清除localStorage对话列表:', key)
      })

    } catch (error) {
      console.error('🔧 [DevTest] 清除对话列表失败:', error)
    }
  }

  // 处理一键注册
  const handleOneClickRegister = async () => {
    console.log('🔧 [DevTest] 开始一键注册流程')
    setIsRegistering(true)

    try {
      // 1. 生成随机用户名和密码
      const username = generateRandomUsername()
      const password = generateRandomPassword()

      console.log('🔧 [DevTest] 生成测试账户:', { username, password })

      // 2. 先清除当前认证状态（不执行完整登出流程）
      if (auth.isLoggedIn()) {
        console.log('🔧 [DevTest] 清除当前认证状态')
        auth.reset()
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      // 3. 清除对话列表
      clearConversationLists()

      // 4. 注册新用户
      console.log('🔧 [DevTest] 开始注册新用户')
      const { token, userId, user } = await signupAndLogin(username, password)

      // 5. 保存到测试账户列表
      const testAccount = createTestAccount(username, password)
      saveTestAccount(testAccount)

      // 6. 设置认证信息
      auth.setToken(token)
      auth.setUserId(userId)
      auth.setUser(user)

      console.log('🔧 [DevTest] 注册成功，跳转到引导页面')

      // 7. 跳转到引导页面
      history.push('/onboarding')

    } catch (error) {
      console.error('🔧 [DevTest] 一键注册失败:', error)

      // 提供更详细的错误信息
      let errorMessage = '注册失败'
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      } else if (error && typeof error === 'object') {
        errorMessage = JSON.stringify(error)
      }

      console.error('🔧 [DevTest] 详细错误信息:', errorMessage)
      alert(`注册失败: ${errorMessage}`)
    } finally {
      setIsRegistering(false)
      setShowRegisterDialog(false)
    }
  }

  // 处理账户切换
  const handleAccountSwitch = async (account: TestAccount) => {
    console.log('🔧 [DevTest] 开始账户切换:', account.username)
    setIsSwitchingAccount(true)

    try {
      // 1. 清除当前认证状态（不执行完整登出流程）
      if (auth.isLoggedIn()) {
        console.log('🔧 [DevTest] 清除当前认证状态')
        auth.reset()
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      // 2. 清除对话列表
      clearConversationLists()

      // 3. 登录到指定账户
      console.log('🔧 [DevTest] 登录到账户:', account.username)
      const { token, userId, user } = await loginWithUsernameAndPassword(
        account.username,
        account.password
      )

      // 4. 设置认证信息
      auth.setToken(token)
      auth.setUserId(userId)
      auth.setUser(user)

      console.log('🔧 [DevTest] 账户切换成功，跳转到主页')

      // 5. 关闭对话框
      setShowAccountSwitchDialog(false)

      // 6. 等待状态更新完成
      await new Promise((resolve) => setTimeout(resolve, 200))

      // 7. 使用通用导航方法跳转到主页
      navigateToHome(true)

    } catch (error) {
      console.error('🔧 [DevTest] 账户切换失败:', error)

      // 提供更详细的错误信息
      let errorMessage = '切换失败'
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      } else if (error && typeof error === 'object') {
        errorMessage = JSON.stringify(error)
      }

      console.error('🔧 [DevTest] 详细错误信息:', errorMessage)
      alert(`切换失败: ${errorMessage}`)
    } finally {
      setIsSwitchingAccount(false)
    }
  }

  // 定义设置项类型
  interface SettingItem {
    icon: string
    title: string
    subtitle: string
    type: 'toggle' | 'navigation'
    key?: keyof typeof settings
    color: string
    badge?: string
  }

  interface SettingGroup {
    group: string
    items: SettingItem[]
  }

  const settingsItems: SettingGroup[] = [
    {
      group: '通用设置',
      items: [
        {
          icon: notificationsOutline,
          title: '通知设置',
          subtitle: '管理推送通知和提醒',
          type: 'toggle',
          key: 'notifications',
          color: 'primary',
        },
        {
          icon: micOutline,
          title: '语音输入',
          subtitle: '启用语音识别功能',
          type: 'toggle',
          key: 'voiceInput',
          color: 'warning',
        },
        {
          icon: colorPaletteOutline,
          title: '主题设置',
          subtitle: '选择应用主题和外观',
          type: 'navigation',
          color: 'secondary',
        },
        {
          icon: informationCircleOutline,
          title: '显示头像',
          subtitle: '聊天列表和气泡显示头像',
          type: 'toggle',
          key: 'showAvatar',
          color: 'success',
        },
        {
          icon: sendOutline,
          title: '按钮发送',
          subtitle: '回车键为换行，点击按钮发送内容，否则 回车键自动发送',
          type: 'toggle',
          key: 'buttonSendMode',
          color: 'primary',
        },
      ],
    },
    {
      group: '隐私与安全',
      items: [
        {
          icon: shieldCheckmarkOutline,
          title: '数据与隐私',
          subtitle: '管理数据使用和隐私设置',
          type: 'navigation',
          color: 'success',
        },
        {
          icon: timeOutline,
          title: '聊天记录',
          subtitle: '管理和清理聊天记录',
          type: 'navigation',
          color: 'warning',
        },
      ],
    },
    {
      group: '开发测试',
      items: [
        {
          icon: addCircleOutline,
          title: '一键注册',
          subtitle: '快速创建测试账户并跳转引导页',
          type: 'navigation',
          color: 'tertiary',
        },
        {
          icon: swapHorizontalOutline,
          title: '账户切换',
          subtitle: '切换到已保存的测试账户',
          type: 'navigation',
          color: 'warning',
        },
      ],
    },
    {
      group: '关于与帮助',
      items: [
        {
          icon: helpCircleOutline,
          title: '帮助中心',
          subtitle: '常见问题和使用指南',
          type: 'navigation',
          color: 'tertiary',
        },
        {
          icon: chatbubbleOutline,
          title: '反馈建议',
          subtitle: '向我们反馈问题和建议',
          type: 'navigation',
          color: 'danger',
        },
        {
          icon: informationCircleOutline,
          title: '关于 Tina Chat',
          subtitle: import.meta.env.MODE,
          type: 'navigation',
          color: 'medium',
          badge: 'v0.0.1 0716',
        },
      ],
    },
  ]

  return (
    <IonPage className={'m-auto sm:max-w-md'}>
      <IonHeader>
        <IonToolbar
          style={{
            '--background': '#FFFFFF',
            '--border-width': '0 0 1px 0',
            '--border-color': '#E5E5E5',
          }}
        >
          <IonButtons slot='start'>
            <HeaderBackButton defaultHref='/conversation/1' />
          </IonButtons>
          <IonTitle
            style={{ color: '#191919', fontSize: '17px', fontWeight: '600' }}
          >
            设置
          </IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent
        style={{
          '--background': '#F3F3F3',
        }}
      >
        {/* 个人信息卡片 */}
        <div
          style={{
            position: 'relative',
            margin: '12px 16px',
            borderRadius: '8px',
            backgroundColor: '#FFFFFF',
            padding: '16px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
            border: '1px solid #E5E5E5',
          }}
        >
          {/* 编辑按钮 - 右上角 */}
          <IonButton
            fill='clear'
            size='small'
            style={{
              position: 'absolute',
              right: '8px',
              top: '8px',
              '--color': '#8E8E93',
            }}
            onClick={handleEditProfile}
          >
            <IonIcon icon={createOutline} style={{ fontSize: '16px' }} />
          </IonButton>

          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* 用户头像 */}
            <IonAvatar style={{ width: '60px', height: '60px' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100%',
                  height: '100%',
                  borderRadius: '50%',
                  backgroundColor: '#07C160',
                  fontSize: '24px',
                  fontWeight: '500',
                  color: '#FFFFFF',
                }}
              >
                {userAvatar}
              </div>
            </IonAvatar>

            <div style={{ marginLeft: '16px', flex: 1 }}>
              <h3
                style={{
                  fontSize: '17px',
                  fontWeight: '600',
                  color: '#191919',
                  margin: '0 0 4px 0',
                }}
              >
                {userName}
              </h3>

              <div style={{ fontSize: '14px', color: '#8E8E93' }}>{name}</div>
            </div>
          </div>
        </div>

        {/* 设置列表 */}
        <div style={{ flex: 1, paddingBottom: '16px' }}>
          {settingsItems.map((group, groupIndex) => (
            <div key={groupIndex} style={{ marginTop: '12px' }}>
              {/* 分组标题 */}
              <div
                style={{
                  paddingLeft: '16px',
                  paddingRight: '16px',
                  paddingTop: '12px',
                  paddingBottom: '8px',
                }}
              >
                <h4
                  style={{
                    fontSize: '13px',
                    fontWeight: '400',
                    color: '#8E8E93',
                    margin: 0,
                    textTransform: 'none',
                  }}
                >
                  {group.group}
                </h4>
              </div>

              {/* 设置项容器 */}
              <div
                style={{
                  backgroundColor: '#FFFFFF',
                  margin: '0 16px',
                  borderRadius: '8px',
                  border: '1px solid #E5E5E5',
                  overflow: 'hidden',
                }}
              >
                {group.items.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '16px',
                      borderBottom:
                        itemIndex < group.items.length - 1
                          ? '1px solid #F3F3F3'
                          : 'none',
                      cursor:
                        item.type === 'navigation' ? 'pointer' : 'default',
                      transition: 'all 0.2s ease',
                    }}
                    onClick={
                      item.type === 'navigation'
                        ? () => handleNavigationClick(item.title)
                        : undefined
                    }
                    onMouseDown={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#F5F5F5'
                      }
                    }}
                    onMouseUp={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#FFFFFF'
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (item.type === 'navigation') {
                        e.currentTarget.style.backgroundColor = '#FFFFFF'
                      }
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '28px',
                          height: '28px',
                          borderRadius: '6px',
                          backgroundColor:
                            item.color === 'primary'
                              ? '#E3F2FD'
                              : item.color === 'warning'
                                ? '#FFF3E0'
                                : item.color === 'secondary'
                                  ? '#F3E5F5'
                                  : item.color === 'success'
                                    ? '#E8F5E9'
                                    : item.color === 'tertiary'
                                      ? '#E0F2F1'
                                      : item.color === 'danger'
                                        ? '#FFEBEE'
                                        : item.color === 'medium'
                                          ? '#F3E5F5'
                                          : '#F5F5F5',
                          marginRight: '12px',
                        }}
                      >
                        <IonIcon
                          icon={item.icon}
                          style={{
                            fontSize: '16px',
                            color:
                              item.color === 'primary'
                                ? '#2196F3'
                                : item.color === 'warning'
                                  ? '#FF9500'
                                  : item.color === 'secondary'
                                    ? '#9C27B0'
                                    : item.color === 'success'
                                      ? '#07C160'
                                      : item.color === 'tertiary'
                                        ? '#009688'
                                        : item.color === 'danger'
                                          ? '#F44336'
                                          : item.color === 'medium'
                                            ? '#9C27B0'
                                            : '#8E8E93',
                          }}
                        />
                      </div>
                      <div style={{ textAlign: 'left' }}>
                        <p
                          style={{
                            fontSize: '16px',
                            fontWeight: '400',
                            color: '#191919',
                            margin: '0 0 2px 0',
                          }}
                        >
                          {item.title}
                        </p>
                        <p
                          style={{
                            fontSize: '13px',
                            color: '#8E8E93',
                            margin: 0,
                          }}
                        >
                          {item.subtitle}
                        </p>
                      </div>
                    </div>

                    {item.type === 'toggle' && item.key && (
                      <IonToggle
                        checked={settings[item.key as keyof typeof settings]}
                        onIonChange={() =>
                          handleToggleChange(item.key as keyof typeof settings)
                        }
                        style={{
                          '--background': '#E5E5E5',
                          '--background-checked': '#07C160',
                          '--handle-background': '#FFFFFF',
                          '--handle-background-checked': '#FFFFFF',
                        }}
                      />
                    )}

                    {item.type === 'navigation' && (
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        {item.badge && (
                          <span
                            style={{
                              fontSize: '13px',
                              color: '#8E8E93',
                              marginRight: '8px',
                            }}
                          >
                            {item.badge}
                          </span>
                        )}
                        <IonIcon
                          icon={chevronForwardOutline}
                          style={{ fontSize: '14px', color: '#C7C7CC' }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 退出登录 */}
        <div style={{ marginBottom: '16px', marginTop: '12px' }}>
          <div
            style={{
              backgroundColor: '#FFFFFF',
              margin: '0 16px',
              borderRadius: '8px',
              border: '1px solid #E5E5E5',
              overflow: 'hidden',
            }}
          >
            <button
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '16px',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
              }}
              onClick={handleLogout}
              onMouseDown={(e) => {
                e.currentTarget.style.backgroundColor = '#FFF5F5'
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <IonIcon
                  icon={logOutOutline}
                  style={{
                    fontSize: '18px',
                    color: '#FF3B30',
                    marginRight: '8px',
                  }}
                />
                <span
                  style={{
                    fontSize: '16px',
                    fontWeight: '400',
                    color: '#FF3B30',
                  }}
                >
                  退出登录
                </span>
              </div>
            </button>
          </div>
        </div>
      </IonContent>

      <WechatDialog
        isOpen={showLogoutDialog}
        title='确定注销当前用户？'
        content=' 注销不会导致数据丢失，但需要重新登录，注销完成后，将返回首页。'
        secondaryButtonText='取消'
        primaryButtonText='确定'
        onSecondaryClick={handleLogoutCancel}
        onPrimaryClick={handleLogoutConfirm}
        onClose={handleLogoutCancel}
        loading={isLoggingOut}
      />

      <BetaInfoDialog
        isOpen={showBetaInfo}
        onClose={handleCloseBetaInfo}
      />

      {/* 一键注册确认对话框 */}
      <WechatDialog
        isOpen={showRegisterDialog}
        title='确定创建新的测试账户？'
        content='将会注销当前用户，创建一个新的随机测试账户，并跳转到引导页面。新账户信息将保存到本地，方便后续切换使用。'
        secondaryButtonText='取消'
        primaryButtonText='确定'
        onSecondaryClick={() => setShowRegisterDialog(false)}
        onPrimaryClick={handleOneClickRegister}
        onClose={() => setShowRegisterDialog(false)}
        loading={isRegistering}
      />

      {/* 账户切换对话框 */}
      {showAccountSwitchDialog && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowAccountSwitchDialog(false)
            }
          }}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '16px',
              maxWidth: '320px',
              width: '100%',
              margin: '0 16px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
              maxHeight: '80vh',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* 标题 */}
            <div style={{ padding: '24px 24px 16px 24px' }}>
              <h3 style={{
                textAlign: 'center',
                fontSize: '18px',
                fontWeight: '500',
                color: '#191919',
                margin: 0
              }}>
                选择要切换的测试账户
              </h3>
            </div>

            {/* 内容区域 */}
            <div style={{
              padding: '0 24px',
              flex: 1,
              overflowY: 'auto',
              maxHeight: '400px'
            }}>
              {testAccounts.length === 0 ? (
                <p style={{
                  textAlign: 'center',
                  fontSize: '14px',
                  color: '#8E8E93',
                  lineHeight: '1.5',
                  margin: '0 0 24px 0'
                }}>
                  暂无保存的测试账户，请先使用"一键注册"创建测试账户。
                </p>
              ) : (
                <div style={{ marginBottom: '24px' }}>
                  <p style={{
                    textAlign: 'center',
                    fontSize: '14px',
                    color: '#8E8E93',
                    lineHeight: '1.5',
                    margin: '0 0 16px 0'
                  }}>
                    选择一个测试账户进行切换，将会注销当前用户并登录到选中的账户。
                  </p>
                  {testAccounts.map((account, index) => (
                    <div
                      key={account.id}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '12px 16px',
                        marginBottom: index < testAccounts.length - 1 ? '8px' : '0',
                        backgroundColor: '#F8F8F8',
                        borderRadius: '8px',
                        border: '1px solid #E5E5E5',
                        cursor: isSwitchingAccount ? 'not-allowed' : 'pointer',
                        transition: 'all 0.2s ease',
                        opacity: isSwitchingAccount ? 0.6 : 1,
                      }}
                      onClick={() => !isSwitchingAccount && handleAccountSwitch(account)}
                      onMouseDown={(e) => {
                        if (!isSwitchingAccount) {
                          e.currentTarget.style.backgroundColor = '#F0F0F0'
                        }
                      }}
                      onMouseUp={(e) => {
                        if (!isSwitchingAccount) {
                          e.currentTarget.style.backgroundColor = '#F8F8F8'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isSwitchingAccount) {
                          e.currentTarget.style.backgroundColor = '#F8F8F8'
                        }
                      }}
                    >
                      <div>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: '500',
                          color: '#191919',
                          marginBottom: '4px'
                        }}>
                          {account.displayName}
                        </div>
                        <div style={{
                          fontSize: '13px',
                          color: '#8E8E93'
                        }}>
                          {account.username} • {new Date(account.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <IonIcon
                        icon={chevronForwardOutline}
                        style={{ fontSize: '14px', color: '#C7C7CC' }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 按钮区域 */}
            <div style={{ borderTop: '1px solid #E5E5E5' }}>
              <button
                onClick={() => setShowAccountSwitchDialog(false)}
                disabled={isSwitchingAccount}
                style={{
                  width: '100%',
                  padding: '16px 24px',
                  border: 'none',
                  backgroundColor: 'transparent',
                  fontSize: '16px',
                  color: '#8E8E93',
                  cursor: isSwitchingAccount ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  opacity: isSwitchingAccount ? 0.6 : 1,
                }}
                onMouseDown={(e) => {
                  if (!isSwitchingAccount) {
                    e.currentTarget.style.backgroundColor = '#F5F5F5'
                  }
                }}
                onMouseUp={(e) => {
                  if (!isSwitchingAccount) {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSwitchingAccount) {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }
                }}
              >
                {isSwitchingAccount ? '切换中...' : '取消'}
              </button>
            </div>
          </div>
        </div>
      )}
    </IonPage>
  )
}
