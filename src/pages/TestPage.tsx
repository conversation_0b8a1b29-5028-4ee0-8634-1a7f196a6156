import { IonPage, IonContent } from '@ionic/react'
import { Link } from 'react-router-dom'

const TestPage = () => {
  // 获取本地存储数据
  const getLocalStorageData = () => {
    const data: Record<string, any> = {}
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        try {
          const value = localStorage.getItem(key)
          data[key] = value ? JSON.parse(value) : value
        } catch (e) {
          // 如果不是JSON，直接存储字符串值
          data[key] = localStorage.getItem(key)
        }
      }
    }
    return data
  }

  // 显示本地存储数据
  const showLocalStorageData = () => {
    const data = getLocalStorageData()
    const jsonString = JSON.stringify(data, null, 2)

    // 创建一个新窗口显示数据
    const newWindow = window.open('', '_blank', 'width=800,height=600')
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>本地存储数据</title>
            <style>
              body { font-family: monospace; padding: 20px; background: #f5f5f5; }
              .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              pre { background: #f8f8f8; padding: 15px; border-radius: 4px; overflow: auto; max-height: 500px; }
              .header { margin-bottom: 20px; }
              .download-btn { 
                background: #07c160; color: white; padding: 10px 20px; 
                border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;
              }
              .download-btn:hover { background: #06a552; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h2>本地存储数据 (localStorage)</h2>
                <button class="download-btn" onclick="downloadData()">下载 JSON 文件</button>
                <button class="download-btn" onclick="window.close()">关闭窗口</button>
              </div>
              <pre>${jsonString}</pre>
            </div>
            <script>
              function downloadData() {
                const data = ${JSON.stringify(jsonString)};
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'localStorage_data_' + new Date().toISOString().split('T')[0] + '.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              }
            </script>
          </body>
        </html>
      `)
    }
  }

  // 下载本地存储数据为JSON文件
  const downloadLocalStorageData = () => {
    const data = getLocalStorageData()
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `localStorage_data_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <IonPage>
      <IonContent>
        <div className='mx-auto min-h-screen w-full max-w-md bg-gray-100 p-4'>
          <div className='mx-auto w-full max-w-sm'>
            <div className='mb-8 pt-8 text-center'>
              <h1 className='mb-2 text-3xl font-bold text-blue-600'>
                测试页面
              </h1>
              <p className='text-gray-600'>tina-webchat 功能测试与调试</p>
            </div>

            <div className='space-y-4'>
              {/* 跳转到对话页面 */}
              <Link
                to='/conversation/1'
                className='block w-full rounded-lg bg-green-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-green-600'
              >
                🗣️ 进入对话界面
              </Link>

              {/* 返回首页链接 */}
              <Link
                to='/'
                className='block w-full rounded-lg bg-gray-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-gray-600'
              >
                🏠 返回首页
              </Link>

              {/* 返回按钮测试 */}
              <Link
                to='/back-button-test'
                className='block w-full rounded-lg bg-blue-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-blue-600'
              >
                🔙 返回按钮测试
              </Link>

              {/* Tina Task 测试页面 */}
              <Link
                to='/tina-task-test'
                className='block w-full rounded-lg bg-purple-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-purple-600'
              >
                🤖 Tina Task 测试页面
              </Link>

              {/* 确认对话框测试页面 */}
              <Link
                to='/test-confirmation-dialog'
                className='block w-full rounded-lg bg-indigo-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-indigo-600'
              >
                💬 确认对话框测试
              </Link>

              {/* Text 组件测试页面 */}
              <Link
                to='/text-component-test'
                className='block w-full rounded-lg bg-emerald-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-emerald-600'
              >
                📝 Text 组件测试
              </Link>

              {/* User Maybe Say API 测试页面 */}
              <Link
                to='/test-user-maybe-say'
                className='block w-full rounded-lg bg-orange-500 px-4 py-3 text-center font-medium text-white transition-colors hover:bg-orange-600'
              >
                🤔 User Maybe Say API 测试
              </Link>

              {/* 本地存储管理 */}
              <div className='rounded-lg bg-white p-4 shadow'>
                <h3 className='mb-3 text-center text-lg font-semibold text-gray-800'>
                  💾 本地存储管理
                </h3>

                <div className='space-y-2'>
                  <button
                    onClick={showLocalStorageData}
                    className='w-full rounded bg-orange-500 px-4 py-2 font-medium text-white transition-colors hover:bg-orange-600'
                  >
                    📋 查看本地存储数据
                  </button>

                  <button
                    onClick={downloadLocalStorageData}
                    className='w-full rounded bg-purple-500 px-4 py-2 font-medium text-white transition-colors hover:bg-purple-600'
                  >
                    💾 下载存储数据 (JSON)
                  </button>
                </div>

                <div className='mt-3 text-center text-xs text-gray-500'>
                  当前存储项目数量: {localStorage.length}
                </div>
              </div>

              {/* 测试信息 */}
              <div className='rounded-lg bg-white p-4 shadow'>
                <h3 className='mb-3 text-center text-lg font-semibold text-gray-800'>
                  🧪 项目信息
                </h3>
                <div className='space-y-1 text-sm text-gray-600'>
                  <p>基于 React + TypeScript + Tailwind</p>
                  <p>状态管理: Jotai | 富文本: Slate.js</p>
                  <p>项目已成功启动！</p>
                </div>
              </div>
            </div>

            {/* 测试滚动内容 */}
            <div className='mt-8 pb-8 text-center text-sm text-gray-500'>
              <div className='mt-4 space-y-2'>
                <p>测试滚动内容 1</p>
                <p>测试滚动内容 2</p>
                <p>测试滚动内容 3</p>
                <p>测试滚动内容 4</p>
                <p>测试滚动内容 5</p>
                <p>测试滚动内容 6</p>
                <p>测试滚动内容 7</p>
                <p>测试滚动内容 8</p>
                <p>测试滚动内容 9</p>
                <p>测试滚动内容 10</p>
                <p>测试滚动内容 11</p>
                <p>测试滚动内容 12</p>
                <p>测试滚动内容 13</p>
                <p>测试滚动内容 14</p>
                <p>测试滚动内容 15</p>
              </div>
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default TestPage
