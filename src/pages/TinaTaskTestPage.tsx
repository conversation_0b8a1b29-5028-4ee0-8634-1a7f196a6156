import React, { useState } from 'react'
import { parseXmlTinaTask, TinaTaskData } from '@/tina/lib/xml/xmlParser'
import {
  IonPage,
  IonContent,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButton,
  IonTextarea,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonIcon,
  IonList,
} from '@ionic/react'
import {
  chevronDownOutline,
  chevronUpOutline,
  timeOutline,
  checkmarkCircleOutline,
} from 'ionicons/icons'

interface SSEMessage {
  event: string
  data: any
  timestamp?: string
}

interface ParsedTinaTask extends TinaTaskData {
  originalMessage: SSEMessage
  displayTime: string
}

const TinaTaskTestPage: React.FC = () => {
  const [inputText, setInputText] = useState('')
  const [parsedTasks, setParsedTasks] = useState<ParsedTinaTask[]>([])
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set())

  // 解析 SSE 消息
  const parseSSEMessages = (text: string): SSEMessage[] => {
    const messages: SSEMessage[] = []
    const lines = text.split('\n')
    let currentMessage: Partial<SSEMessage> = {}

    for (const line of lines) {
      const trimmedLine = line.trim()

      if (trimmedLine.startsWith('event:')) {
        // 如果有未完成的消息，先保存
        if (currentMessage.event && currentMessage.data) {
          messages.push(currentMessage as SSEMessage)
        }
        // 开始新消息
        currentMessage = {
          event: trimmedLine.substring(6).trim(),
        }
      } else if (trimmedLine.startsWith('data:')) {
        const dataStr = trimmedLine.substring(5).trim()
        try {
          currentMessage.data = JSON.parse(dataStr)
        } catch (e) {
          currentMessage.data = dataStr
        }
      } else if (trimmedLine === '') {
        // 空行表示消息结束
        if (currentMessage.event && currentMessage.data) {
          messages.push(currentMessage as SSEMessage)
          currentMessage = {}
        }
      }
    }

    // 处理最后一个消息
    if (currentMessage.event && currentMessage.data) {
      messages.push(currentMessage as SSEMessage)
    }

    return messages
  }

  // 格式化时间显示
  const formatTime = (timestamp: string): string => {
    try {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    } catch (e) {
      return timestamp
    }
  }

  // 处理输入文本 - 自动解析
  const handleParse = (text: string) => {
    if (!text.trim()) {
      setParsedTasks([])
      return
    }

    const sseMessages = parseSSEMessages(text)
    const taskMessages = sseMessages.filter(
      (msg) =>
        msg.data && typeof msg.data === 'object' && msg.data.type === 'task',
    )

    const newTasks: ParsedTinaTask[] = []

    taskMessages.forEach((msg) => {
      if (msg.data.metadata && msg.data.metadata.content) {
        const xmlContent = msg.data.metadata.content
        const xmlType = msg.data.metadata.xml_type

        // 对于 change_plan 类型，直接使用 metadata 中的信息
        if (xmlType === 'change_plan') {
          newTasks.push({
            id:
              msg.data.metadata.session_id ||
              `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'change_plan',
            content: xmlContent,
            steps: [],
            originalMessage: msg,
            displayTime: formatTime(
              msg.data.timestamp || new Date().toISOString(),
            ),
          })
        } else {
          // 尝试解析 XML 内容
          const parsedTask = parseXmlTinaTask(xmlContent)

          if (parsedTask) {
            newTasks.push({
              ...parsedTask,
              id:
                parsedTask.id ||
                `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              originalMessage: msg,
              displayTime: formatTime(
                msg.data.timestamp || new Date().toISOString(),
              ),
            })
          } else {
            // 如果 XML 解析失败，创建一个基本的任务对象
            newTasks.push({
              id:
                msg.data.metadata.session_id ||
                `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              type: msg.data.metadata.xml_type || 'unknown',
              content: xmlContent,
              steps: [],
              originalMessage: msg,
              displayTime: formatTime(
                msg.data.timestamp || new Date().toISOString(),
              ),
            })
          }
        }
      }
    })

    setParsedTasks(newTasks)
  }

  // 处理输入文本变化，自动解析
  const handleInputChange = (text: string) => {
    setInputText(text)
    handleParse(text)
  }

  // 切换任务展开状态
  const toggleTaskExpanded = (index: number) => {
    const newExpanded = new Set(expandedTasks)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedTasks(newExpanded)
  }

  // 获取任务类型的显示文本和颜色
  const getTaskTypeInfo = (type: string) => {
    switch (type) {
      case 'plan':
        return { text: '计划', color: 'primary' }
      case 'tools_call':
        return { text: '工具调用', color: 'warning' }
      case 'task_completed':
        return { text: '任务完成', color: 'success' }
      case 'change_plan':
        return { text: '计划变更', color: 'tertiary' }
      default:
        return { text: `未知类型 (${type})`, color: 'medium' }
    }
  }

  // 按 session_id 分组任务
  const groupTasksBySession = (tasks: ParsedTinaTask[]) => {
    const groups: { [sessionId: string]: ParsedTinaTask[] } = {}

    tasks.forEach((task) => {
      const sessionId =
        task.originalMessage.data.metadata?.session_id || task.id
      if (!groups[sessionId]) {
        groups[sessionId] = []
      }
      groups[sessionId].push(task)
    })

    // 按时间排序每个组内的任务
    Object.keys(groups).forEach((sessionId) => {
      groups[sessionId].sort((a, b) => {
        const timeA = new Date(a.originalMessage.data.timestamp || 0).getTime()
        const timeB = new Date(b.originalMessage.data.timestamp || 0).getTime()
        return timeA - timeB
      })
    })

    return groups
  }

  // 清空数据
  const handleClear = () => {
    setInputText('')
    setParsedTasks([])
    setExpandedTasks(new Set())
  }

  // 加载示例数据
  const loadSampleData = () => {
    const sampleSSE = `event:message
data:{"content":"开始执行数据分析任务","metadata":{"content":"<tina_task>\\n<meta_data>\\n<id>task-data-analysis-001</id>\\n<type>plan</type>\\n</meta_data>\\n<plan>\\n<step index=\\"1\\">\\n<title>数据收集</title>\\n<tools_description>收集相关数据源</tools_description>\\n<tools>data_collector</tools>\\n</step>\\n<step index=\\"2\\">\\n<title>数据清洗</title>\\n<tools_description>清理和预处理数据</tools_description>\\n<tools>data_cleaner</tools>\\n</step>\\n</plan>\\n</tina_task>","session_id":"session-001","step":1,"timestamp":1751082300,"xml_type":"plan"},"timestamp":"2025-06-28T11:45:00+08:00","tool_name":"tina_task_create","type":"task"}

event:message
data:{"content":"正在执行数据收集","metadata":{"content":"<tina_task>\\n<meta_data>\\n<id>task-data-analysis-002</id>\\n<type>tools_call</type>\\n</meta_data>\\n<tools>\\n<step>1</step>\\n<tool>\\n<name>data_collector</name>\\n<parameters>\\n<param name=\\"source\\">database</param>\\n</parameters>\\n</tool>\\n</tools>\\n</tina_task>","session_id":"session-001","step":1,"timestamp":1751082350,"xml_type":"tools_call"},"timestamp":"2025-06-28T11:45:50+08:00","tool_name":"tina_task_execute","type":"task"}

event:message
data:{"content":"数据分析任务已完成","metadata":{"content":"<tina_task>\\n<meta_data>\\n<id>task-data-analysis-003</id>\\n<type>task_completed</type>\\n</meta_data>\\n<content>数据分析任务已完成</content>\\n<completed>true</completed>\\n</tina_task>","session_id":"session-001","step":-1,"timestamp":1751082400,"xml_type":"task_completed"},"timestamp":"2025-06-28T11:46:40+08:00","tool_name":"tina_task_query_status","type":"task"}

event:message
data:{"content":"开始新的报告生成任务","metadata":{"content":"<tina_task>\\n<meta_data>\\n<id>task-report-001</id>\\n<type>plan</type>\\n</meta_data>\\n<plan>\\n<step index=\\"1\\">\\n<title>收集数据</title>\\n<tools_description>从数据库收集报告数据</tools_description>\\n<tools>report_collector</tools>\\n</step>\\n</plan>\\n</tina_task>","session_id":"session-002","step":1,"timestamp":1751082500,"xml_type":"plan"},"timestamp":"2025-06-28T11:48:20+08:00","tool_name":"tina_task_create","type":"task"}

event:message
data:{"content":"需要修改当前计划，是否同意？","metadata":{"content":"需要修改当前计划，是否同意？","session_id":"session-002","step":-1,"timestamp":1751082550,"xml_type":"change_plan"},"timestamp":"2025-06-28T11:49:10+08:00","tool_name":"tina_task_query_status","type":"task"}

event:message
data:{"content":"未知类型的任务消息","metadata":{"content":"<tina_task>\\n<meta_data>\\n<id>task-unknown-001</id>\\n<type>custom_action</type>\\n</meta_data>\\n<content>这是一个未知类型的任务</content>\\n</tina_task>","session_id":"session-003","step":1,"timestamp":1751082600,"xml_type":"custom_action"},"timestamp":"2025-06-28T11:50:00+08:00","tool_name":"tina_task_custom","type":"task"}`

    handleInputChange(sampleSSE)
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Tina Task 测试页面</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent className='ion-padding'>
        {/* 输入区域 */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>SSE 消息输入</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonTextarea
              value={inputText}
              onIonInput={(e) => handleInputChange(e.detail.value!)}
              placeholder='请粘贴 SSE 消息内容...'
              rows={8}
              className='ion-margin-bottom'
            />
            <div className='ion-text-center'>
              <IonButton
                onClick={loadSampleData}
                color='secondary'
                className='ion-margin-end'
              >
                加载示例
              </IonButton>
              <IonButton onClick={handleClear} color='medium' fill='outline'>
                清空
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>

        {/* 解析结果 */}
        {parsedTasks.length > 0 && (
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                解析结果 (
                {
                  new Set(
                    parsedTasks
                      .map(
                        (task) =>
                          task.originalMessage.data.metadata?.session_id,
                      )
                      .filter(Boolean),
                  ).size
                }{' '}
                个任务，{parsedTasks.length} 个步骤)
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {(() => {
                const taskGroups = groupTasksBySession(parsedTasks)
                let globalIndex = 0

                return Object.entries(taskGroups).map(([sessionId, tasks]) => (
                  <div key={sessionId} style={{ marginBottom: '16px' }}>
                    {/* Session 标题 */}
                    <div
                      style={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: '#333',
                        marginBottom: '8px',
                        padding: '8px 12px',
                        backgroundColor: '#f0f0f0',
                        borderRadius: '6px',
                        borderLeft: '4px solid #007bff',
                      }}
                    >
                      📋 任务 Session: {sessionId}
                      <span
                        style={{
                          fontSize: '12px',
                          fontWeight: 'normal',
                          color: '#666',
                          marginLeft: '8px',
                        }}
                      >
                        ({tasks.length} 个步骤)
                      </span>
                    </div>

                    {/* Session 内的任务列表 */}
                    <IonList>
                      {tasks.map((task) => {
                        const currentIndex = globalIndex++
                        const isExpanded = expandedTasks.has(currentIndex)
                        const typeInfo = getTaskTypeInfo(task.type)

                        return (
                          <IonItem
                            key={`${task.id}-${currentIndex}`}
                            className='ion-margin-bottom'
                          >
                            <div style={{ width: '100%' }}>
                              {/* 任务头部信息 */}
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  cursor: 'pointer',
                                  padding: '8px 0',
                                }}
                                onClick={() => toggleTaskExpanded(currentIndex)}
                              >
                                <div style={{ flex: 1 }}>
                                  <div
                                    style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: '8px',
                                      marginBottom: '4px',
                                    }}
                                  >
                                    <IonIcon
                                      icon={
                                        task.type === 'task_completed'
                                          ? checkmarkCircleOutline
                                          : timeOutline
                                      }
                                      color={typeInfo.color}
                                    />
                                    <span
                                      style={{
                                        fontSize: '14px',
                                        fontWeight: '500',
                                        color: `var(--ion-color-${typeInfo.color})`,
                                      }}
                                    >
                                      {typeInfo.text}
                                      {/* 工具调用类型在标题位置显示步骤 */}
                                      {task.type === 'tools_call' &&
                                        task.tools_call && (
                                          <span
                                            style={{
                                              fontSize: '16px',
                                              fontWeight: 'bold',
                                              color: '#ff6b35',
                                              marginLeft: '8px',
                                              backgroundColor: '#fff3e0',
                                              padding: '2px 6px',
                                              borderRadius: '4px',
                                            }}
                                          >
                                            步骤 {task.tools_call.step}
                                          </span>
                                        )}
                                    </span>
                                    <span
                                      style={{
                                        fontSize: '12px',
                                        color: '#666',
                                      }}
                                    >
                                      {task.displayTime}
                                    </span>
                                  </div>
                                  <div
                                    style={{
                                      fontSize: '14px',
                                      color: '#333',
                                      lineHeight: '1.4',
                                    }}
                                  >
                                    {task.content || '无内容描述'}
                                  </div>

                                  {/* 折叠状态下显示的信息 */}
                                  {!isExpanded && (
                                    <div style={{ marginTop: '8px' }}>
                                      {/* 显示Session ID */}
                                      <div
                                        style={{
                                          fontSize: '11px',
                                          color: '#999',
                                          fontFamily: 'monospace',
                                          marginBottom: '4px',
                                        }}
                                      >
                                        Session:{' '}
                                        {task.originalMessage.data.metadata
                                          ?.session_id || task.id}
                                      </div>

                                      {/* Plan 类型显示步骤信息 */}
                                      {task.type === 'plan' &&
                                        task.steps.length > 0 && (
                                          <div
                                            style={{
                                              fontSize: '12px',
                                              color: '#666',
                                              marginBottom: '4px',
                                            }}
                                          >
                                            📋 包含 {task.steps.length} 个步骤:{' '}
                                            {task.steps
                                              .map((step) => step.title)
                                              .join(' → ')}
                                          </div>
                                        )}

                                      {/* Tools_call 类型显示工具调用信息 */}
                                      {task.type === 'tools_call' &&
                                        task.tools_call && (
                                          <div
                                            style={{
                                              fontSize: '12px',
                                              color: '#666',
                                              marginBottom: '4px',
                                            }}
                                          >
                                            🔧 工具: {task.tools_call.tool_name}
                                          </div>
                                        )}

                                      {/* Task_completed 类型显示完成信息 */}
                                      {task.type === 'task_completed' && (
                                        <div
                                          style={{
                                            fontSize: '12px',
                                            color: '#28a745',
                                            marginBottom: '4px',
                                          }}
                                        >
                                          ✅ 任务已完成
                                        </div>
                                      )}

                                      {/* Change_plan 类型显示变更信息 */}
                                      {task.type === 'change_plan' && (
                                        <div
                                          style={{
                                            fontSize: '12px',
                                            color: '#9c27b0',
                                            marginBottom: '4px',
                                          }}
                                        >
                                          🔄 需要确认计划变更
                                        </div>
                                      )}

                                      {/* 未知类型显示原始信息 */}
                                      {![
                                        'plan',
                                        'tools_call',
                                        'task_completed',
                                        'change_plan',
                                      ].includes(task.type) && (
                                        <div
                                          style={{
                                            fontSize: '12px',
                                            color: '#ff9800',
                                            marginBottom: '4px',
                                          }}
                                        >
                                          ⚠️ 未知类型，请查看详细信息
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                                <IonIcon
                                  icon={
                                    isExpanded
                                      ? chevronUpOutline
                                      : chevronDownOutline
                                  }
                                  style={{ marginLeft: '8px' }}
                                />
                              </div>

                              {/* 展开的详细内容 */}
                              {isExpanded && (
                                <div className='mt-3 rounded bg-gray-50 p-3'>
                                  <div className='space-y-3'>
                                    <div>
                                      <strong className='text-sm'>
                                        任务 ID:
                                      </strong>
                                      <div className='font-mono text-xs text-gray-600'>
                                        {task.id}
                                      </div>
                                    </div>

                                    {task.steps.length > 0 && (
                                      <div>
                                        <strong className='text-sm'>
                                          执行步骤:
                                        </strong>
                                        <div className='mt-1 space-y-2'>
                                          {task.steps.map((step, stepIndex) => (
                                            <div
                                              key={stepIndex}
                                              className='rounded border-l-2 border-blue-200 bg-white p-2 text-xs'
                                            >
                                              <div className='font-medium'>
                                                步骤 {step.index}: {step.title}
                                              </div>
                                              {step.tools_description && (
                                                <div className='mt-1 text-gray-600'>
                                                  {step.tools_description}
                                                </div>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {task.tools_call && (
                                      <div>
                                        <strong className='text-sm'>
                                          工具调用:
                                        </strong>
                                        <div className='mt-1 rounded bg-white p-2 text-xs'>
                                          <div>
                                            步骤: {task.tools_call.step}
                                          </div>
                                          <div>
                                            工具: {task.tools_call.tool_name}
                                          </div>
                                          {Object.keys(
                                            task.tools_call.parameters,
                                          ).length > 0 && (
                                            <div className='mt-1'>
                                              参数:{' '}
                                              {JSON.stringify(
                                                task.tools_call.parameters,
                                                null,
                                                2,
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                    <div>
                                      <strong className='text-sm'>
                                        原始消息:
                                      </strong>
                                      <pre className='mt-1 max-h-32 overflow-auto rounded bg-white p-2 text-xs'>
                                        {JSON.stringify(
                                          task.originalMessage,
                                          null,
                                          2,
                                        )}
                                      </pre>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </IonItem>
                        )
                      })}
                    </IonList>
                  </div>
                ))
              })()}
            </IonCardContent>
          </IonCard>
        )}

        {/* 空状态 */}
        {parsedTasks.length === 0 && inputText && (
          <IonCard>
            <IonCardContent className='ion-text-center'>
              <div className='text-gray-500'>未找到有效的 Tina Task 消息</div>
            </IonCardContent>
          </IonCard>
        )}
      </IonContent>
    </IonPage>
  )
}

export default TinaTaskTestPage
