import { useState, useEffect } from 'react'
import TypewriterText from '@/components/TypewriterText'

interface TextAreaProps {
  texts: string[]
  onComplete?: () => void
  className?: string
}

const TextArea = ({ texts, onComplete, className = '' }: TextAreaProps) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)

  // 当texts改变时重置索引
  useEffect(() => {
    setCurrentTextIndex(0)
  }, [texts])

  const handleTextComplete = () => {
    if (currentTextIndex < texts.length - 1) {
      // 还有更多文本段落，继续显示下一段
      setTimeout(() => {
        setCurrentTextIndex(prev => prev + 1)
      }, 800)
    } else {
      // 所有文本段落显示完成
      if (onComplete) {
        onComplete()
      }
    }
  }

  if (!texts || texts.length === 0) {
    return null
  }

  return (
    <div className={`space-y-4 text-center ${className}`}>
      {texts.map((text, index) => (
        <div key={index} className='min-h-[24px]'>
          {index <= currentTextIndex && (
            <TypewriterText
              text={text}
              speed={50}
              onComplete={
                index === currentTextIndex ? handleTextComplete : undefined
              }
              className='text-base text-gray-600'
            />
          )}
        </div>
      ))}
    </div>
  )
}

export default TextArea
