import { useState } from 'react'
import robotImage from '@/assets/robot.png'
import TypewriterText from '@/components/TypewriterText'

// 统一的消息数据结构
export interface MessageData {
  text: string
  think?: {
    segments: string[]
    startTime?: number
  }
}

interface ChatBubbleProps {
  message: MessageData
  onThinkingComplete?: (duration: number) => void
  onTextComplete?: () => void
  showText?: boolean // 控制是否显示正文内容
}

const ChatBubble = ({
  message,
  onThinkingComplete,
  onTextComplete,
  showText = true,
}: ChatBubbleProps) => {
  // Think 相关状态 - 内聚到 ChatBubble 内部
  const [currentThinkIndex, setCurrentThinkIndex] = useState(0)
  const [isThinkingCollapsed, setIsThinkingCollapsed] = useState(false)
  const [thinkingDuration, setThinkingDuration] = useState(0)
  const [thinkStartTime] = useState(message.think?.startTime || Date.now())
  const [isAllThinkingComplete, setIsAllThinkingComplete] = useState(false)

  const bubbleClasses =
    'relative max-w-[95%] break-words rounded-lg border border-gray-100 bg-white shadow-sm'
  const arrowClasses =
    'absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white'

  const hasThinking = message.think && message.think.segments.length > 0

  const handleThinkSegmentComplete = () => {
    if (!message.think) return

    if (currentThinkIndex < message.think.segments.length - 1) {
      setTimeout(() => setCurrentThinkIndex((prev) => prev + 1), 1000)
    } else {
      // 思考完成，立即停止计时
      const duration = Math.round((Date.now() - thinkStartTime) / 1000)
      setThinkingDuration(duration)

      // 同时设置完成和收起状态，避免中间展开
      setTimeout(() => {
        setIsAllThinkingComplete(true)
        setIsThinkingCollapsed(true)
        onThinkingComplete?.(duration)
      }, 1500)
    }
  }

  const handleThinkingClick = () => {
    // 只有当所有思考完成时才允许点击
    if (!isAllThinkingComplete) return
    setIsThinkingCollapsed((prevState) => !prevState)
  }

  const getThinkingTitle = () => {
    if (!hasThinking) return ''
    if (isAllThinkingComplete) {
      return isThinkingCollapsed
        ? `思考完毕（用时 ${thinkingDuration} 秒） >`
        : '主理人正在思考'
    }
    return '主理人正在思考'
  }

  const shouldShowTextBubble = showText && message.text.trim()

  return (
    <div className='friend group flex space-x-3'>
      {/* 头像 - 如果有 think 则下移一个 title 高度 */}
      <img
        src={robotImage}
        alt='机器人助手'
        className={`h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center ${
          hasThinking ? 'mt-5' : ''
        }`}
      />

      {/* 信息部分 - 上下结构：think title, think content, text */}
      <div className='flex max-w-[95%] flex-col'>
        {/* Think Title */}
        {hasThinking && (
          <div
            className='mb-1 cursor-pointer text-sm text-gray-500'
            onClick={handleThinkingClick}
          >
            {getThinkingTitle()}
          </div>
        )}

        {/* Think Content - 折叠后不显示气泡 */}
        {hasThinking && !isThinkingCollapsed && (
          <div
            className={`${bubbleClasses} mb-2 p-3 text-xs`}
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            {/* 如果所有思考已完成且用户点击展开，直接显示所有内容；否则显示打字效果 */}
            {isAllThinkingComplete && !isThinkingCollapsed ? (
              // 思考完成后，直接显示所有片段
              <div className='space-y-3'>
                {message.think!.segments.map((segment, index) => (
                  <div key={index} className='text-sm text-gray-700'>
                    <div className='mb-1 text-xs font-medium text-gray-500'>
                      思考片段 {index + 1}:
                    </div>
                    <div>{segment}</div>
                  </div>
                ))}
              </div>
            ) : (
              // 思考进行中，显示当前段落的打字效果
              !isAllThinkingComplete &&
              currentThinkIndex < (message.think?.segments.length || 0) && (
                <TypewriterText
                  text={message.think!.segments[currentThinkIndex]}
                  speed={40}
                  onComplete={handleThinkSegmentComplete}
                  className='text-sm text-gray-700'
                />
              )
            )}
          </div>
        )}

        {/* Text Content */}
        {shouldShowTextBubble && (
          <div className={`${bubbleClasses} p-3`}>
            <div className={arrowClasses} />
            <div className='relative z-10'>
              <TypewriterText
                text={message.text}
                speed={50}
                onComplete={onTextComplete}
                className='text-base text-gray-700'
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChatBubble
