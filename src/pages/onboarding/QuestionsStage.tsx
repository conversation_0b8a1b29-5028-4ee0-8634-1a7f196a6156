import { useEffect, useState } from 'react'
import { getRandomSummary } from '@/pages/onboarding/feecback.ts'
import robotImage from '@/assets/robot.png'
import TextArea from './TextArea'
import {
  onboardingQuestions,
  type QuestionOption,
  welcomeTexts,
} from './questionData'

interface QuestionsStageProps {
  currentQuestionIndex: number
  apiResponseText: string | null
  onQuestionComplete: (
    index: number,
    apiResponse: string,
    selectedOption: QuestionOption,
  ) => void
}

const QuestionsStage = ({
  currentQuestionIndex,
  apiResponseText,
  onQuestionComplete,
}: QuestionsStageProps) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentTexts, setCurrentTexts] = useState<string[]>([])
  const [textComplete, setTextComplete] = useState(false)

  const currentQuestion = onboardingQuestions[currentQuestionIndex]

  // 初始化当前题目的文本
  useEffect(() => {
    if (currentQuestionIndex === 0) {
      // 第一题显示welcome文本
      setCurrentTexts(welcomeTexts)
      setTextComplete(false)
    } else if (apiResponseText) {
      // 其他题目显示上一题的API响应文本
      setCurrentTexts([apiResponseText])
      setTextComplete(false)
    } else {
      // 没有API响应文本，直接显示题目
      setCurrentTexts([])
      setTextComplete(true)
    }
    setIsProcessing(false)
  }, [currentQuestionIndex, apiResponseText])

  const handleOptionSelect = (option: QuestionOption) => {
    if (isProcessing) return

    setIsProcessing(true)

    // 模拟API调用
    setTimeout(() => {
      let text = getRandomSummary(option.id)
      onQuestionComplete(currentQuestionIndex, text, option)
    }, 500)
  }

  const handleSkipClick = () => {
    const skipOption: QuestionOption = {
      id: `${currentQuestion.id}_skip`,
      text: '稍后再说',
    }
    handleOptionSelect(skipOption)
  }

  const handleTextAreaComplete = () => {
    // 所有题目，文本显示完成后都显示题目
    setTextComplete(true)
  }

  if (!currentQuestion) {
    return null
  }

  return (
    <div className='flex h-full w-full flex-col items-center space-y-6'>
      {/* 机器人头像 */}
      <div className='h-32 w-32'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {/* 文本区域 */}
      {currentTexts.length > 0 && (
        <TextArea texts={currentTexts} onComplete={handleTextAreaComplete} />
      )}

      {/* 问题区域 */}
      {textComplete && (
        <div className='flex w-full flex-1 items-center justify-center'>
          <div className='w-full max-w-sm rounded-lg border border-gray-200 bg-[#F6F4EE] p-6 shadow-lg'>
            <h3 className='mb-6 text-center text-lg font-medium text-gray-800'>
              {currentQuestion.text}
            </h3>

            <div className='grid grid-cols-2 gap-3'>
              {currentQuestion.options
                .filter((option) => !option.id.includes('skip'))
                .map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionSelect(option)}
                    disabled={isProcessing}
                    className='rounded-3xl border border-gray-200 bg-gray-50 px-3 py-1 text-center text-sm text-gray-700 shadow-lg transition-colors hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50'
                  >
                    {option.text}
                  </button>
                ))}
            </div>

            <div className='mt-4 text-center'>
              <button
                onClick={handleSkipClick}
                disabled={isProcessing}
                className='bg-transparent text-xs text-gray-400 transition-colors hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50'
              >
                稍后再说
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default QuestionsStage
