import { ChatServiceManager } from '@/tina/services/chat-service-manager'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { QuestionOption } from '../questionData'

// Mock fetch
global.fetch = vi.fn()

// Mock useAuthStore
const mockAuthStore = {
  getState: vi.fn(() => ({
    auth: {
      isLoggedIn: vi.fn(() => true),
      userId: 'test-user-id',
      token: 'test-token',
      user: { name: 'test-user', id: 'test-user-id' },
    },
  })),
}

vi.mock('@/tina/stores/authStore', () => ({
  useAuthStore: mockAuthStore,
}))

// Mock GATEWAY_URL
vi.mock('@/tina/lib/casdoor', () => ({
  GATEWAY_URL: 'https://tina-test.bfbdata.com/gateway',
}))

// Mock environment variable
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_API_URL: undefined, // 这样会使用 GATEWAY_URL
  },
})

// 模拟数据处理逻辑（从主页面提取的逻辑）
const processSelectedOptions = (selectedOptions: QuestionOption[]): string => {
  // 过滤掉值为 'skip' 的选项
  const validOptions = selectedOptions.filter(
    (option) => !option.id.includes('skip') && option.text !== '稍后再说',
  )

  // 将选项文字内容用分号连接
  return validOptions.map((option) => option.text).join(';')
}

describe('引导页面选项数据处理功能', () => {
  const mockSelectedOptions: QuestionOption[] = [
    { id: 'age_18_35', text: '18 - 35 岁' },
    { id: 'lifestyle_work', text: '💼 职场冲刺' },
    { id: 'interests_tech', text: '🚀 科技/创新' },
    { id: 'style_professional', text: '🤵专业严谨型' },
  ]

  const mockSelectedOptionsWithSkip: QuestionOption[] = [
    { id: 'age_18_35', text: '18 - 35 岁' },
    { id: 'lifestyle_skip', text: '稍后再说' },
    { id: 'interests_tech', text: '🚀 科技/创新' },
    { id: 'style_skip', text: '稍后再说' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock successful fetch response
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true }),
    })
  })

  it('应该正确过滤和格式化选项数据', () => {
    const result = processSelectedOptions(mockSelectedOptions)
    expect(result).toBe('18 - 35 岁;💼 职场冲刺;🚀 科技/创新;🤵专业严谨型')
  })

  it('应该过滤掉 skip 选项', () => {
    const result = processSelectedOptions(mockSelectedOptionsWithSkip)
    expect(result).toBe('18 - 35 岁;🚀 科技/创新')
  })

  it('当没有有效选项时应该返回空字符串', () => {
    const onlySkipOptions: QuestionOption[] = [
      { id: 'age_skip', text: '稍后再说' },
      { id: 'lifestyle_skip', text: '稍后再说' },
    ]

    const result = processSelectedOptions(onlySkipOptions)
    expect(result).toBe('')
  })

  it('应该在空选项数组时返回空字符串', () => {
    const result = processSelectedOptions([])
    expect(result).toBe('')
  })

  it('应该正确处理混合的有效和无效选项', () => {
    const mixedOptions: QuestionOption[] = [
      { id: 'age_18_35', text: '18 - 35 岁' },
      { id: 'lifestyle_skip', text: '稍后再说' },
      { id: 'interests_tech', text: '🚀 科技/创新' },
      { id: 'style_skip', text: '稍后再说' },
      { id: 'other_valid', text: '其他有效选项' },
    ]

    const result = processSelectedOptions(mixedOptions)
    expect(result).toBe('18 - 35 岁;🚀 科技/创新;其他有效选项')
  })

  it('应该正确识别包含skip的ID', () => {
    const skipIdOptions: QuestionOption[] = [
      { id: 'age_18_35', text: '18 - 35 岁' },
      { id: 'lifestyle_work_skip', text: '工作相关跳过' },
      { id: 'interests_skip_tech', text: '技术跳过' },
    ]

    const result = processSelectedOptions(skipIdOptions)
    expect(result).toBe('18 - 35 岁')
  })
})

describe('ChatServiceManager.sendProfileMessage 静态方法', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该成功发送用户档案消息', async () => {
    // Mock successful response
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true }),
    })

    await expect(
      ChatServiceManager.sendProfileMessage(
        '18 - 35 岁;💼 职场冲刺;🚀 科技/创新',
      ),
    ).resolves.not.toThrow()

    expect(global.fetch).toHaveBeenCalledWith(
      'https://tina-test.bfbdata.com/gateway/emotionmind/api/v1/message/send',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer test-token',
        },
        body: JSON.stringify({
          user_id: 'test-user-id',
          content: '18 - 35 岁;💼 职场冲刺;🚀 科技/创新',
          message_type: 'profile',
        }),
      },
    )
  })

  it('应该在用户未登录时抛出错误', async () => {
    // Mock unauthenticated state
    vi.mocked(
      vi.mocked(require('@/tina/stores/authStore')).useAuthStore.getState,
    ).mockReturnValue({
      auth: {
        isLoggedIn: vi.fn(() => false),
        userId: null,
        token: null,
        user: null,
      },
    })

    await expect(
      ChatServiceManager.sendProfileMessage('test content'),
    ).rejects.toThrow('用户未登录')
  })

  it('应该在API请求失败时抛出错误', async () => {
    // Mock failed response
    ;(global.fetch as any).mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    })

    await expect(
      ChatServiceManager.sendProfileMessage('test content'),
    ).rejects.toThrow('HTTP 500: Internal Server Error')
  })
})
