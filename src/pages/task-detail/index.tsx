import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { fetchSessionSSE, ServerMessage } from '@/tina/lib/session.ts'
import { parseXmlTinaTask, TinaTaskData } from '@/tina/lib/xml/xmlParser.ts'
import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonPage,
  IonTitle,
  IonToolbar,
} from '@ionic/react'
import { send } from 'ionicons/icons'
import { useHistory, useParams } from 'react-router-dom'
import MarkdownRenderer from '../../components/MarkdownRenderer'
import { PlanMessage } from '../../components/conversation/plan-message'
import { HeaderBackButton } from '../../components/ui/back-button'
import { Skeleton } from '../../components/ui/skeleton'
import useAuth from '../../tina/stores/authStore'

const ValidTags = ['ToolCallBlock', 'ServerError', 'ShowToolResult']

interface PlanMessageData extends TinaTaskData {
  currentStepIndex: number | null
  isRunning: boolean
  showQuickSuggest: boolean
}

interface ParsedMessage {
  role: 'user' | 'assistant'
  content: string | PlanMessageData
  timestamp?: number
}

interface MobileTaskDetail2Props {
  taskId?: string | null
  onClose?: () => void
}

export default function TaskDetail({
  taskId: propTaskId,
}: MobileTaskDetail2Props) {
  const { taskId: paramTaskId } = useParams<{ taskId: string }>()
  const taskId = propTaskId || paramTaskId

  const auth = useAuth()
  const token = auth.token || ''
  const [messages, setMessages] = useState<ServerMessage[]>([])

  // 取第一条用户消息
  const firstUserMessage = useMemo(() => {
    return messages.find((msg) => msg.role === 'user')?.content || ''
  }, [messages])

  // parsedMessages 排除第一条用户消息
  const parsedMessages = useMemo(() => {
    const result: ParsedMessage[] = []
    let currentPlan: PlanMessageData | null = null
    let firstUserSkipped = false

    for (const msg of messages) {
      if (msg.role === 'user') {
        if (!firstUserSkipped) {
          firstUserSkipped = true
          continue // 跳过第一条用户消息
        }
        result.push({
          role: 'user',
          content: msg.content,
          timestamp: msg.timestamp,
        })
        if (currentPlan) {
          currentPlan.showQuickSuggest = false
        }
        continue
      }
      // assistant 消息
      if (msg.xml_content) {
        const taskData = parseXmlTinaTask(msg.xml_content)
        if (taskData?.type === 'plan') {
          if (currentPlan) {
            currentPlan.isRunning = false
            handleStepStatus(currentPlan)
          }
          currentPlan = {
            ...taskData,
            steps: taskData.steps.map((s) => ({
              ...s,
              content: s.content || '',
            })),
            isRunning: true,
            currentStepIndex: null,
            showQuickSuggest: false, // task 不显示建议
          }
          result.push({
            role: 'assistant',
            content: currentPlan,
            timestamp: msg.timestamp,
          })
          continue
        }
        if (currentPlan && currentPlan.isRunning) {
          if (taskData?.type === 'tools_call') {
            // 记录当前 step 编号
            currentPlan.currentStepIndex = taskData.tools_call?.step ?? null
            handleStepStatus(currentPlan)
          } else if (taskData?.type === 'task_completed') {
            // 结束 plan 模式
            currentPlan.isRunning = false
            handleStepStatus(currentPlan, true)
            currentPlan = null
          }
          continue
        }
      }
      // plan 模式下，追加内容到当前 runningStep
      if (currentPlan && currentPlan.isRunning) {
        if (currentPlan.currentStepIndex == null) {
          currentPlan.currentStepIndex = 1
        }
        const step = currentPlan.steps.find(
          (s) => s.index === currentPlan?.currentStepIndex,
        )
        if (step) {
          // tool call block 拼接到独立区域
          const isContentContainsValidTag = ValidTags.some((tag) =>
            msg.content.includes(tag),
          )
          if (isContentContainsValidTag) {
            step.toolCallBlocks =
              (step.toolCallBlocks || '') +
              (msg.content ? `\n${msg.content}` : '')
          } else {
            step.content += msg.content || ''
          }
        }

        // 接受到 error message 时的处理， 结束 plan
        if (msg.content.includes('ServerError')) {
          currentPlan.isRunning = false
          handleStepStatus(currentPlan)
          currentPlan = null
        }
        continue
      }
      // 非 plan 模式下，assistant 消息合并
      if (!currentPlan) {
        const last = result[result.length - 1]
        if (
          last &&
          last.role === 'assistant' &&
          typeof last.content === 'string'
        ) {
          last.content += msg.content || ''
        } else {
          result.push({
            role: 'assistant',
            content: msg.content,
            timestamp: msg.timestamp,
          })
        }
      }
    }
    return result
  }, [messages])

  // 用于显示的消息，只显示最后一个 plan，并将其移动到最前面
  const displayMessages = useMemo(() => {
    let lastPlanMessage: ParsedMessage | null = null

    // 找到最后一个 plan 消息
    for (let i = parsedMessages.length - 1; i >= 0; i--) {
      const msg = parsedMessages[i]
      if (msg.role === 'assistant' && typeof msg.content !== 'string') {
        lastPlanMessage = msg
        break
      }
    }

    // 过滤掉所有 plan 消息
    const messagesWithoutPlans = parsedMessages.filter((msg) => {
      if (msg.role === 'assistant' && typeof msg.content !== 'string') {
        return false // 移除所有 plan 消息
      }
      return true
    })

    // 如果有最后一个 plan，将其放在最前面
    if (lastPlanMessage) {
      return [lastPlanMessage, ...messagesWithoutPlans]
    }

    return messagesWithoutPlans
  }, [parsedMessages])

  function handleStepStatus(plan: PlanMessageData, complete = false) {
    if (!plan.steps) return
    if (complete) {
      plan.steps.forEach((step) => {
        step.status = 'success'
      })
      return
    }
    if (plan.currentStepIndex !== null) {
      plan.steps.forEach((step) => {
        if (step.index < plan.currentStepIndex!) step.status = 'success'
        else if (step.index === plan.currentStepIndex)
          step.status = plan.isRunning ? 'running' : 'success'
        else step.status = 'pending'
      })
    } else {
      plan.steps.forEach((step) => {
        step.status = 'pending'
      })
    }
  }

  const history = useHistory()

  useEffect(() => {
    if (!taskId || !token) return
    setMessages([])
    const stop = fetchSessionSSE(taskId, token, (msg: ServerMessage) => {
      setMessages((prev) => [...prev, msg])
    })
    return () => {
      stop()
    }
  }, [taskId, token])

  // stepper 展开项与交互控制
  // 用 planId-stepIndex 作为 key，planId 用 timestamp
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set())
  const userInteractedSteps = useRef<Set<string>>(new Set())

  // 获取所有 running plan 的当前 step key
  const runningStepKeys = useMemo(() => {
    return displayMessages
      .filter(
        (msg) => msg.role === 'assistant' && typeof msg.content !== 'string',
      )
      .flatMap((msg) => {
        const plan = msg.content as PlanMessageData
        if (!plan.steps || !plan.isRunning) return []
        if (
          plan.currentStepIndex === null ||
          plan.currentStepIndex === undefined
        )
          return []
        // 用 timestamp 作为 planId
        return [`${msg.timestamp}-${plan.currentStepIndex}`]
      })
  }, [displayMessages])

  // 自动展开所有 running plan 的当前 step（未被用户干预过的）
  useEffect(() => {
    setExpandedSteps((prev) => {
      const next = new Set(prev)
      runningStepKeys.forEach((key) => {
        if (!userInteractedSteps.current.has(key)) {
          next.add(key)
        }
      })
      return next
    })
  }, [runningStepKeys])

  // Stepper 手动展开/收起
  const handleToggleStep = useCallback(
    (planId: string, step: number, expanded: boolean) => {
      const key = `${planId}-${step}`
      userInteractedSteps.current.add(key)
      setExpandedSteps((prev) => {
        const next = new Set(prev)
        if (expanded) next.add(key)
        else next.delete(key)
        return next
      })
    },
    [],
  )

  // Stepper 展开项变化
  const handleExpandedStepsChange = useCallback(
    (planId: string, steps: Set<number>) => {
      setExpandedSteps((prev) => {
        const next = new Set(prev)
        // 先移除当前 planId 的所有 step
        for (const k of next) {
          if (k.startsWith(`${planId}-`)) next.delete(k)
        }
        // 再加上新的
        steps.forEach((step) => next.add(`${planId}-${step}`))
        return next
      })
    },
    [],
  )

  // 处理返回按钮点击
  const handleBackClick = () => {
    history.goBack()
  }

  return (
    <IonPage className={'m-auto sm:max-w-md'}>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot='start'>
            <HeaderBackButton defaultHref='/task' />
          </IonButtons>
          <IonTitle>任务详情</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <div className='h-full overflow-y-auto overflow-x-hidden'>
          <main>
            <div className='mx-auto flex flex-col items-center px-4 py-6 sm:px-6 md:max-w-3xl lg:max-w-4xl xl:max-w-5xl'>
              {/* 用户问题 展示区 */}
              <div className='w-full'>
                <div className='border-gray-200 bg-white p-4'>
                  <div className='flex items-start space-x-4'>
                    <div className='flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-700'>
                      <div className='relative h-8 w-8 rounded-full'>
                        <div className='absolute left-1.5 top-1 h-1.5 w-1.5 rounded-full bg-gray-700'></div>
                        <div className='absolute right-1.5 top-1 h-1.5 w-1.5 rounded-full bg-gray-700'></div>
                        <div className='absolute bottom-1.5 left-1/2 h-1 w-1.5 -translate-x-1/2 transform rounded-full bg-red-400'></div>
                      </div>
                    </div>
                    <div className='flex-1'>
                      <p className='text text-gray-600'>{firstUserMessage}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 消息流区 */}
              <div className='bg-card min-h-[300px] w-full rounded-lg p-4 sm:p-6'>
                <div className='space-y-4'>
                  {displayMessages.length === 0 && (
                    <div className='flex items-center justify-center'>
                      <div className='flex flex-col items-center space-y-2'>
                        <Skeleton className='h-4 w-24' />
                        <Skeleton className='h-4 w-32' />
                        <Skeleton className='h-4 w-20' />
                      </div>
                    </div>
                  )}
                  {displayMessages.map((msg, idx) => (
                    <div
                      key={idx}
                      className={
                        msg.role === 'user'
                          ? 'flex justify-end'
                          : msg.role === 'assistant' &&
                              typeof msg.content === 'string'
                            ? 'flex justify-start'
                            : ''
                      }
                    >
                      {/* 用户消息 */}
                      {msg.role === 'user' && (
                        <div className='bg-primary text-primary-foreground mb-1 max-w-[70%] break-words rounded-lg px-4 py-2 shadow-md'>
                          {msg.content as string}
                        </div>
                      )}
                      {/* assistant 普通消息 */}
                      {msg.role === 'assistant' &&
                        typeof msg.content === 'string' && (
                          <div className='bg-muted text-muted-foreground mb-1 max-w-[70%] break-words rounded-lg px-4 py-2 shadow'>
                            <MarkdownRenderer
                              content={msg.content}
                              variant='compact'
                              className='text-sm sm:text-base'
                            />
                          </div>
                        )}
                      {/* assistant plan 消息 */}
                      {msg.role === 'assistant' &&
                        typeof msg.content !== 'string' && (
                          <PlanMessage
                            plan={msg.content as TinaTaskData}
                            planId={String(msg.timestamp)}
                            expandedSteps={expandedSteps}
                            handleExpandedStepsChange={
                              handleExpandedStepsChange
                            }
                            handleToggleStep={handleToggleStep}
                          />
                        )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </main>
        </div>
        <div slot={'fixed'} className={'bottom-0 left-0 right-0 bg-white'}>
          <IonButton shape='round' expand='full' onClick={handleBackClick}>
            <span className={'mr-2'}>关闭 </span> <IonIcon icon={send} />
          </IonButton>
        </div>
      </IonContent>
    </IonPage>
  )
}
