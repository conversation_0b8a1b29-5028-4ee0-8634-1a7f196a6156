import React, { useState } from 'react'
import { loginWithUsernameAndPassword } from '@/tina/lib/auth'
import useAuth from '@/tina/stores/authStore'
import { Button, Form, Input } from 'antd'

interface LoginFormProps {
  onSuccess: () => void
  onSwitchToRegister: () => void
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  onSwitchToRegister,
}) => {
  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const auth = useAuth()

  const handleSubmit = async (values: {
    username: string
    password: string
  }) => {
    setIsLoading(true)
    setErrorMessage('')

    try {
      const { token, userId, user } = await loginWithUsernameAndPassword(
        values.username,
        values.password,
      )
      auth.setToken(token)
      auth.setUserId(userId)
      auth.setUser(user)

      onSuccess()
    } catch (e: any) {
      setErrorMessage(e.message || '登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className='mx-auto flex w-full max-w-md flex-col'
      style={{
        backgroundColor: 'transparent',
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      }}
    >
      {/* 下半部分 - 表单区域 */}
      <div className='flex-1 px-4 py-6'>
        {errorMessage && (
          <div
            className='mb-4 rounded-lg p-3 text-center'
            style={{
              backgroundColor: '#FFEBEE',
              color: '#C62828',
              fontSize: '14px',
            }}
          >
            {errorMessage}
          </div>
        )}

        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          size='large'
        >
          {/* 用户名输入框 */}
          <Form.Item
            name='username'
            rules={[{ required: true, message: '请输入用户名' }]}
            style={{ marginBottom: '20px' }}
          >
            <div
              className='flex items-center px-4 py-3'
              style={{
                backgroundColor: 'transparent',
                borderBottom: '1px solid #E5E5E5',
              }}
            >
              <span
                style={{
                  color: '#999999',
                  fontSize: '16px',
                  marginRight: '12px',
                  minWidth: '60px',
                }}
              >
                用户名
              </span>
              <Input
                placeholder='请输入用户名'
                variant='borderless'
                style={{
                  fontSize: '16px',
                  padding: 0,
                  backgroundColor: 'transparent',
                }}
              />
            </div>
          </Form.Item>

          {/* 密码输入框 */}
          <Form.Item
            name='password'
            rules={[{ required: true, message: '请输入密码' }]}
            style={{ marginBottom: '40px' }}
          >
            <div
              className='flex items-center px-4 py-3'
              style={{
                backgroundColor: 'transparent',
                borderBottom: '1px solid #E5E5E5',
              }}
            >
              <span
                style={{
                  color: '#999999',
                  fontSize: '16px',
                  marginRight: '12px',
                  minWidth: '60px',
                }}
              >
                密码
              </span>
              <Input.Password
                placeholder='请输入密码'
                variant='borderless'
                style={{
                  fontSize: '16px',
                  padding: 0,
                  backgroundColor: 'transparent',
                }}
              />
            </div>
          </Form.Item>

          {/* 登录按钮 */}
          <Form.Item style={{ marginBottom: '20px' }}>
            <Button
              type='primary'
              htmlType='submit'
              loading={isLoading}
              block
              style={{
                backgroundColor: '#000',
                borderRadius: '8px',
                height: '50px',
                fontSize: '17px',
                fontWeight: '400',
                boxShadow: 'none',
              }}
            >
              {isLoading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>

          {/* 册链接 */}
          <div className='text-center'>
            <Button
              type='link'
              onClick={onSwitchToRegister}
              disabled={isLoading}
              style={{
                color: '#576B95',
                fontSize: '16px',
                padding: 0,
                height: 'auto',
                textDecoration: 'none',
              }}
            >
              没有账号？立即注册
            </Button>
          </div>
        </Form>
      </div>

      {/* 底部协议 */}
      <div className='px-4 pb-8'>
        <div className='text-center'>
          <span style={{ color: '#999999', fontSize: '12px' }}>
            登录即表示同意
          </span>
          <Button
            type='link'
            style={{
              color: '#576B95',
              fontSize: '12px',
              padding: 0,
              height: 'auto',
              textDecoration: 'underline',
            }}
          >
            《软件许可及服务协议》
          </Button>
        </div>
      </div>
    </div>
  )
}

export default LoginForm
