import React, { useState } from 'react'

interface WechatInputDialogProps {
  isOpen: boolean
  title: string
  content: string
  placeholder?: string
  secondaryButtonText?: string
  primaryButtonText: string
  onSecondaryClick?: () => void
  onPrimaryClick: (input: string) => void
  onClose?: () => void
  loading?: boolean
  requireInput?: boolean
}

const WechatInputDialog: React.FC<WechatInputDialogProps> = ({
  isOpen,
  title,
  content,
  placeholder = '请输入您的回复...',
  secondaryButtonText = '取消',
  primaryButtonText,
  onSecondaryClick,
  onPrimaryClick,
  onClose,
  loading = false,
  requireInput = false,
}) => {
  const [inputValue, setInputValue] = useState('')

  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && onClose) {
      onClose()
    }
  }

  const handlePrimaryClick = () => {
    if (requireInput && !inputValue.trim()) {
      // 如果需要输入但没有输入内容，不执行操作
      return
    }
    onPrimaryClick(inputValue.trim())
    setInputValue('') // 清空输入
  }

  const handleSecondaryClick = () => {
    if (onSecondaryClick) {
      onSecondaryClick()
    }
    setInputValue('') // 清空输入
  }

  const handleClose = () => {
    if (onClose) {
      onClose()
    }
    setInputValue('') // 清空输入
  }

  return (
    <div
      className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'
      onClick={handleBackdropClick}
    >
      <div className='mx-4 w-full max-w-sm overflow-hidden rounded-2xl bg-white shadow-lg'>
        {/* 标题 */}
        <div className='px-6 pb-4 pt-6'>
          <h3 className='text-center text-lg font-medium text-gray-900'>
            {title}
          </h3>
        </div>

        {/* 内容 */}
        <div className='px-6 pb-4'>
          <p className='text-center text-sm leading-relaxed text-gray-600'>
            {content}
          </p>
        </div>

        {/* 输入框 */}
        <div className='px-6 pb-6'>
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={placeholder}
            rows={3}
            className='w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500'
          />
          {requireInput && (
            <p className='mt-1 text-xs text-gray-500'>* 此项为必填</p>
          )}
        </div>

        {/* 按钮区域 */}
        <div className='border-t border-gray-200'>
          {onSecondaryClick ? (
            <div className='grid grid-cols-2'>
              <button
                onClick={handleSecondaryClick}
                disabled={loading}
                className='border-r border-gray-200 px-6 py-4 text-base text-gray-900 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                {secondaryButtonText}
              </button>
              <button
                onClick={handlePrimaryClick}
                disabled={loading || (requireInput && !inputValue.trim())}
                className='flex items-center justify-center px-6 py-4 text-base font-medium text-blue-600 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                {loading ? (
                  <div className='flex items-center'>
                    <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent'></div>
                    处理中...
                  </div>
                ) : (
                  primaryButtonText
                )}
              </button>
            </div>
          ) : (
            <button
              onClick={handlePrimaryClick}
              disabled={loading || (requireInput && !inputValue.trim())}
              className='flex w-full items-center justify-center px-6 py-4 text-base font-medium text-blue-600 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
            >
              {loading ? (
                <div className='flex items-center'>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent'></div>
                  处理中...
                </div>
              ) : (
                primaryButtonText
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default WechatInputDialog
