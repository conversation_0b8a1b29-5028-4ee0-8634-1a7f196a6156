import React from 'react'
import { TinaTaskData } from '@/tina/lib/xml/xmlParser.ts'
import MarkdownRenderer from '../MarkdownRenderer'
import {
  Stepper,
  StepperDescription,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '../ui/stepper'

interface PlanMessageProps {
  plan: TinaTaskData
  planId: string
  expandedSteps: Set<string>
  handleExpandedStepsChange: (planId: string, steps: Set<number>) => void
  handleToggleStep: (planId: string, step: number, expanded: boolean) => void
}

export function PlanMessage({
  plan,
  planId,
  expandedSteps,
  handleExpandedStepsChange,
  handleToggleStep,
}: PlanMessageProps) {
  // 计算当前 plan 下已展开的 step index
  const planExpandedSteps = React.useMemo(() => {
    const set = new Set<number>()
    expandedSteps.forEach((key) => {
      if (key.startsWith(`${planId}-`)) {
        const idx = Number(key.split('-')[1])
        if (!isNaN(idx)) set.add(idx)
      }
    })
    return set
  }, [expandedSteps, planId])

  // 处理展开状态变化
  const onExpandedStepsChange = React.useCallback(
    (steps: Set<number>) => {
      handleExpandedStepsChange(planId, steps)
    },
    [planId, handleExpandedStepsChange],
  )

  // 处理单个步骤切换
  const onToggleStep = React.useCallback(
    (step: number, expanded: boolean) => {
      handleToggleStep(planId, step, expanded)
    },
    [planId, handleToggleStep],
  )

  return (
    <div className='bg-muted/40 mt-1 w-full rounded-lg p-1 sm:p-2'>
      <Stepper
        defaultValue={0}
        orientation='vertical'
        expandedSteps={planExpandedSteps}
        onExpandedStepsChange={onExpandedStepsChange}
        onToggleStep={onToggleStep}
      >
        {plan.steps.map((step, idx) => {
          let status: 'pending' | 'running' | 'success' | 'error' = 'pending'
          if (step.status) status = step.status
          return (
            <StepperItem
              key={step.index}
              step={step.index}
              status={status}
              className='relative items-start [&:not(:last-child)]:flex-1'
              data-step={step.index}
            >
              <StepperTrigger className='items-start pb-1 last:pb-0'>
                <StepperIndicator />
                <div className='mt-0.5 space-y-1 px-2 text-left'>
                  <StepperTitle className='text-sm'>{step.title}</StepperTitle>
                  <StepperDescription>
                    <MarkdownRenderer
                      content={step.content || step.tools_description || ''}
                      variant='compact'
                      className='max-w-full overflow-x-auto text-xs sm:text-sm'
                    />
                    <MarkdownRenderer
                      content={step.toolCallBlocks || ''}
                      variant='compact'
                      className='max-w-full overflow-x-auto whitespace-pre-wrap break-all text-xs sm:text-sm'
                    />
                  </StepperDescription>
                </div>
              </StepperTrigger>
              {idx < plan.steps.length - 1 && (
                <StepperSeparator className='bg-muted-foreground/40 absolute inset-y-0 left-3 top-[calc(1.5rem+0.125rem)] -order-1 m-0 -translate-x-1/2 group-data-[orientation=vertical]/stepper:h-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:w-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:flex-none' />
              )}
            </StepperItem>
          )
        })}
      </Stepper>
    </div>
  )
}
