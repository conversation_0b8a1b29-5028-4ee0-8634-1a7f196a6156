import { useState, useEffect } from 'react'

export interface IAssetCache {
  url: string
  data: string | null
  loading: boolean
  error: string | null
}

export function useAssetsCache(url: string): IAssetCache {
  const [data, setData] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!url) return

    setLoading(true)
    setError(null)

    // 模拟资源加载
    const loadAsset = async () => {
      try {
        // 这里可以添加实际的资源加载逻辑
        // 比如从缓存或网络加载资源
        await new Promise((resolve) => setTimeout(resolve, 100))
        setData(url) // 简单返回URL作为数据
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setLoading(false)
      }
    }

    loadAsset()
  }, [url])

  return {
    url,
    data,
    loading,
    error,
  }
}

// 异步资源缓存hook
export function useAsyncAssetsCache(url: string): IAssetCache {
  return useAssetsCache(url)
}
