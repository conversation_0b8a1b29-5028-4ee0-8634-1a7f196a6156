import { FC } from 'react'
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser'
import { IonButton } from '@ionic/react'
import {
  BrainCircuitIcon,
  FileTextIcon,
  GlobeIcon,
  PresentationIcon,
} from 'lucide-react'

interface ShowToolResultProps {
  url: string
  type: string
}

interface TypeConfig {
  [key: string]: { icon: typeof GlobeIcon; label: string } | undefined
}

// Mapping of types to icons and labels
const typeConfig: TypeConfig = {
  mindnode: { icon: BrainCircuitIcon, label: '思维导图' },
  website: { icon: GlobeIcon, label: '网页' },
  mdweb: { icon: FileTextIcon, label: '文档' },
  ppt: { icon: PresentationIcon, label: '演示文稿' },
}

export const ShowToolResult: FC<ShowToolResultProps> = ({ url, type }) => {
  const { icon: IconComponent, label } = typeConfig[type] || {
    icon: GlobeIcon,
    label: '网页',
  }

  return (
    <div className='my-4'>
      <IonButton onClick={() => Browser.open({ url })} color={'light'}>
        <IconComponent className='mr-1 h-4 w-4' />
        点击查看{label}
      </IonButton>
    </div>
  )
}
