import { FC, ReactNode } from 'react'
import { Alert, AlertDescription, AlertTitle } from '../ui/alert'

interface ServerErrorProps {
  errorcode: string
  children: ReactNode
}

export const ServerError: FC<ServerErrorProps> = ({ errorcode, children }) => {
  return (
    <div className='my-4'>
      <Alert variant='destructive'>
        <AlertTitle>{errorcode}</AlertTitle>
        <AlertDescription>{children}</AlertDescription>
      </Alert>
    </div>
  )
}
