import { memo, useEffect, useState, useRef } from 'react'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import { createPortal } from 'react-dom'

type Props = {
  content: string
  title: string
  isOpen: boolean
  onClose: () => void
}

const MarkmapViewer = ({ content, title, isOpen, onClose }: Props) => {
  const [isAnimating, setIsAnimating] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)
  const svgRef = useRef<SVGSVGElement>(null)
  const markmapRef = useRef<Markmap | null>(null)

  useEffect(() => {
    if (isOpen) {
      // 开始显示组件
      setShouldRender(true)
      // 禁止背景滚动
      document.body.style.overflow = 'hidden'
      // 短暂延迟后开始动画，让组件有时间渲染
      const timer = setTimeout(() => {
        setIsAnimating(true)
      }, 50)
      return () => clearTimeout(timer)
    } else {
      // 开始关闭动画
      setIsAnimating(false)
      // 动画结束后隐藏组件
      const timer = setTimeout(() => {
        setShouldRender(false)
        // 恢复背景滚动
        document.body.style.overflow = ''
      }, 350) // 稍微延长一点时间，确保动画完成
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  // 初始化 Markmap
  useEffect(() => {
    if (shouldRender && svgRef.current && content) {
      try {
        // 创建 transformer 实例
        const transformer = new Transformer()

        // 转换 markdown 内容为 markmap 数据
        const { root } = transformer.transform(content)

        // 创建 markmap 实例
        if (markmapRef.current) {
          markmapRef.current.destroy()
        }

        markmapRef.current = Markmap.create(svgRef.current)
        markmapRef.current.setData(root)

        // 适应容器大小
        setTimeout(() => {
          if (markmapRef.current) {
            markmapRef.current.fit()
          }
        }, 100)
      } catch (error) {
        console.error('Markmap 初始化失败:', error)
      }
    }
  }, [shouldRender, content])

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      document.body.style.overflow = ''
      if (markmapRef.current) {
        markmapRef.current.destroy()
        markmapRef.current = null
      }
    }
  }, [])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleBackdropClick = () => {
    onClose()
  }

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  const handleFitToScreen = () => {
    if (markmapRef.current) {
      markmapRef.current.fit()
    }
  }

  const handleZoomIn = () => {
    if (markmapRef.current) {
      markmapRef.current.rescale(1.2)
    }
  }

  const handleZoomOut = () => {
    if (markmapRef.current) {
      markmapRef.current.rescale(0.8)
    }
  }

  if (!shouldRender) return null

  return createPortal(
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ease-out ${
        isAnimating
          ? 'bg-black bg-opacity-50 backdrop-blur-sm'
          : 'bg-black bg-opacity-0'
      }`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div
        className={`max-h flex h-full w-full max-w-6xl transform flex-col rounded-lg bg-white shadow-2xl transition-all duration-300 ease-out ${
          isAnimating
            ? 'translate-y-0 scale-100 opacity-100'
            : 'translate-y-4 scale-50 opacity-50'
        }`}
        onClick={handleContentClick}
        style={{
          transformOrigin: 'center center',
        }}
      >
        {/* 头部 */}
        <div className='flex items-center justify-between rounded-t-lg border-b border-gray-200 bg-white p-4'>
          <div className='min-w-0 flex-1'>
            <h2 className='truncate text-lg font-medium text-gray-900'>
              {title}
            </h2>
            <p className='text-sm text-gray-500'>思维导图</p>
          </div>
          <div className='ml-4 flex items-center space-x-2'>
            {/* 放大按钮 */}
            <button
              onClick={handleZoomIn}
              className='rounded-full p-2 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
              aria-label='放大'
              title='放大'
            >
              <svg
                className='h-5 w-5'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                />
              </svg>
            </button>
            {/* 缩小按钮 */}
            <button
              onClick={handleZoomOut}
              className='rounded-full p-2 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
              aria-label='缩小'
              title='缩小'
            >
              <svg
                className='h-5 w-5'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M18 12H6'
                />
              </svg>
            </button>
            {/* 适应屏幕按钮 */}
            <button
              onClick={handleFitToScreen}
              className='rounded-full p-2 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
              aria-label='适应屏幕'
              title='适应屏幕'
            >
              <svg
                className='h-5 w-5'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4'
                />
              </svg>
            </button>
            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className='rounded-full p-2 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
              aria-label='关闭'
            >
              <svg
                className='h-6 w-6'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Markmap 内容区域 */}
        <div className='relative flex-1 overflow-hidden rounded-b-lg bg-gray-50'>
          <svg
            ref={svgRef}
            className='h-full w-full'
            style={{ background: 'white' }}
          />
        </div>
      </div>
    </div>,
    document.body,
  )
}

export default memo(MarkmapViewer)
