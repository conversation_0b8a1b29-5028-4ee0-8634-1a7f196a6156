import { memo, useEffect, useRef, useState } from 'react'
import 'github-markdown-css/github-markdown-light.css'
import 'highlight.js/styles/github.css'
import { createPortal } from 'react-dom'
import { App as cap } from '@capacitor/app'
import MarkdownRenderer from './MarkdownRenderer'
import { showToast } from '@/wechatComponents/Toast'

type Props = {
  content: string
  isOpen: boolean
  onClose: () => void
}

const MarkdownViewer = ({ content, isOpen, onClose }: Props) => {
  const viewerRef = useRef<HTMLDivElement>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)

  useEffect(() => {
    if (isOpen) {
      // 开始显示组件
      setShouldRender(true)
      // 禁止背景滚动
      document.body.style.overflow = 'hidden'
      // 设置全局标志，禁用默认返回键处理
      window.shouldHandleBackButton = false
      // 短暂延迟后开始动画，让组件有时间渲染
      const timer = setTimeout(() => {
        setIsAnimating(true)
      }, 50)
      return () => clearTimeout(timer)
    } else {
      // 开始关闭动画
      setIsAnimating(false)
      // 恢复默认返回键处理
      window.shouldHandleBackButton = true
      // 动画结束后隐藏组件
      const timer = setTimeout(() => {
        setShouldRender(false)
        // 恢复背景滚动
        document.body.style.overflow = ''
      }, 350) // 稍微延长一点时间，确保动画完成
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  // 添加移动端返回键监听器
  useEffect(() => {
    if (!isOpen) return

    let backButtonListener: any = null

    const setupListener = async () => {
      backButtonListener = await cap.addListener('backButton', () => {
        // 只有当 MarkdownViewer 打开时才处理返回键
        if (window.shouldHandleBackButton === false) {
          onClose()
        }
      })
    }

    setupListener()

    return () => {
      if (backButtonListener) {
        backButtonListener.remove()
      }
    }
  }, [isOpen, onClose])

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      document.body.style.overflow = ''
      window.shouldHandleBackButton = true
    }
  }, [])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleBackdropClick = () => {
    onClose()
  }

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
      showToast({ content: '复制成功', type: 'success' })
    } catch {
      showToast({ content: '复制失败', type: 'error' })
    }
  }

  if (!shouldRender) return null

  return createPortal(
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ease-out ${
        isAnimating
          ? 'bg-black bg-opacity-50 backdrop-blur-sm'
          : 'bg-black bg-opacity-0'
      }`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div
        ref={viewerRef}
        className={`relative  max-h-full w-full h-full transform overflow-hidden bg-white shadow-2xl transition-all duration-300 ease-out ${
          isAnimating
            ? 'translate-y-0 scale-100 opacity-100'
            : 'translate-y-4 scale-50 opacity-50'
        }`}
        onClick={handleContentClick}
        style={{
          transformOrigin: 'center center',
        }}
      >
        {/* 头部工具栏 */}
        <div className='flex items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-2'>
          <h2 className='text-lg font-semibold text-gray-800'>Markdown 内容</h2>
          <div className='flex items-center'>
            <button
              onClick={handleCopy}
              className='rounded-full p-2 transition-colors duration-200 hover:bg-gray-200 mr-2'
              aria-label='复制'
            >
              <svg
                className='h-5 w-5 text-gray-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <rect x='9' y='9' width='13' height='13' rx='2' ry='2' stroke='currentColor' strokeWidth='2' fill='none'/>
                <rect x='3' y='3' width='13' height='13' rx='2' ry='2' stroke='currentColor' strokeWidth='2' fill='none'/>
              </svg>
            </button>
            <button
              onClick={onClose}
              className='rounded-full p-2 transition-colors duration-200 hover:bg-gray-200'
              aria-label='关闭'
            >
              <svg
                className='h-5 w-5 text-gray-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Markdown 内容区域 */}
        <div className='max-h-[calc(99vh)] overflow-y-auto p-4 pb-20'>
          <MarkdownRenderer
            content={content}
            variant='full'
            className='markdown-body'
          />
        </div>
      </div>
    </div>,
    document.body,
  )
}

export default memo(MarkdownViewer)
