import React from 'react'

const LoadingPage = () => {
  return (
    <div
      className='relative mx-auto flex min-h-screen w-full max-w-md flex-col items-center justify-center'
      style={{
        backgroundColor: '#EDEDED',
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      }}
    >
      {/* Logo */}
      <div className='mb-8 text-center'>
        <div
          className='mb-6 inline-flex h-20 w-20 items-center justify-center rounded-2xl'
          style={{ backgroundColor: '#07C160' }}
        >
          <svg width='40' height='40' viewBox='0 0 24 24' fill='white'>
            <path d='M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.162 4.203 2.969 5.531-.171.469-.906 2.781-.906 2.781s2.375-.484 2.844-.688c.469.203 1.047.375 1.688.375.422 0 .828-.047 1.219-.125-.078-.266-.125-.547-.125-.844 0-3.844 3.234-6.953 7.234-6.953.422 0 .828.047 1.234.094C15.234 4.734 12.25 2.188 8.691 2.188zm-2.362 2.797c.609 0 1.125.516 1.125 1.125s-.516 1.125-1.125 1.125S6.204 6.719 6.204 6.11s.516-1.125 1.125-1.125zm4.5 0c.609 0 1.125.516 1.125 1.125s-.516 1.125-1.125 1.125-1.125-.516-1.125-1.125.516-1.125 1.125-1.125zm5.062 6.797c-3.187 0-5.75 2.328-5.75 5.203 0 2.875 2.563 5.203 5.75 5.203.469 0 .922-.047 1.375-.141.375.141 2.25.563 2.25.563s-.563-1.828-.719-2.219C23.328 18.453 24 17.313 24 15.985c0-2.875-2.563-5.203-5.75-5.203zm-3.844 2.531c.375 0 .688.313.688.688s-.313.688-.688.688-.688-.313-.688-.688.313-.688.688-.688zm3.844 0c.375 0 .688.313.688.688s-.313.688-.688.688-.688-.313-.688-.688.313-.688.688-.688z' />
          </svg>
        </div>

        <h1 className='mb-2 text-2xl font-normal' style={{ color: '#000000' }}>
          Tina
        </h1>
      </div>

      {/* 加载动画 */}
      <div className='mb-8 flex items-center justify-center'>
        <div className='flex space-x-1'>
          <div
            className='h-2 w-2 rounded-full'
            style={{
              backgroundColor: '#07C160',
              animation: 'wechatPulse 1.4s infinite ease-in-out 0ms',
            }}
          ></div>
          <div
            className='h-2 w-2 rounded-full'
            style={{
              backgroundColor: '#07C160',
              animation: 'wechatPulse 1.4s infinite ease-in-out 200ms',
            }}
          ></div>
          <div
            className='h-2 w-2 rounded-full'
            style={{
              backgroundColor: '#07C160',
              animation: 'wechatPulse 1.4s infinite ease-in-out 400ms',
            }}
          ></div>
        </div>
      </div>

      {/* 加载文字 */}
      <p
        className='text-center'
        style={{
          color: '#999999',
          fontSize: '14px',
          marginBottom: '60px',
        }}
      >
        正在加载...
      </p>

      {/* 底部版权信息 */}
      <div className='absolute bottom-8 left-0 right-0'>
        <div className='text-center'>
          <p
            style={{
              color: '#999999',
              fontSize: '12px',
              marginBottom: '4px',
            }}
          >
            Tina.Chat Demo
          </p>
          <p
            style={{
              color: '#999999',
              fontSize: '12px',
            }}
          >
            致敬经典设计
          </p>
        </div>
      </div>

      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes wechatPulse {
            0%, 70%, 100% {
              transform: scale(1);
              opacity: 1;
            }
            35% {
              transform: scale(1.3);
              opacity: 0.7;
            }
          }
        `,
        }}
      />
    </div>
  )
}

export default LoadingPage
