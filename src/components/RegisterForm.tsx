import React, { useState } from 'react'
import { signupAndLogin } from '@/tina/lib/auth'
import useAuth from '@/tina/stores/authStore'
import { Button, Form, Input } from 'antd'

interface RegisterFormProps {
  onSuccess: () => void
  onSwitchToLogin: () => void
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  onSuccess,
  onSwitchToLogin,
}) => {
  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const auth = useAuth()

  const handleSubmit = async (values: {
    username: string
    password: string
    confirmPassword: string
  }) => {
    if (!agreedToTerms) {
      setErrorMessage('请先同意微信软件许可及服务协议')
      return
    }

    setIsLoading(true)
    setErrorMessage('')

    try {
      // 检查是否已登录，如已登录则先清除登录状态
      if (auth.isLoggedIn()) {
        auth.reset()
        // 等待登录状态清除完成
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      // 调用API进行注册
      const { token, userId, user } = await signupAndLogin(
        values.username,
        values.password,
      )
      auth.setToken(token)
      auth.setUserId(userId)
      auth.setUser(user)

      onSuccess()
    } catch (e: any) {
      setErrorMessage(e.message || '注册失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className='mx-auto flex w-full max-w-md flex-col'
      style={{
        backgroundColor: 'transparent',
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      }}
    >
      {/* 下半部分 - 表单区域 */}
      <div className='flex-1 px-4 py-6'>
        {errorMessage && (
          <div
            className='mb-4 rounded-lg p-3 text-center'
            style={{
              backgroundColor: '#FFEBEE',
              color: '#C62828',
              fontSize: '14px',
            }}
          >
            {errorMessage}
          </div>
        )}

        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          size='large'
        >
          {/* 用户名输入框 */}
          <Form.Item
            name='username'
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
            ]}
            style={{ marginBottom: '20px' }}
          >
            <div
              className='flex items-center px-4 py-3'
              style={{
                backgroundColor: 'transparent',
                borderBottom: '1px solid #E5E5E5',
              }}
            >
              <span
                style={{
                  color: '#999999',
                  fontSize: '16px',
                  marginRight: '12px',
                  minWidth: '60px',
                }}
              >
                用户名
              </span>
              <Input
                placeholder='请输入用户名'
                variant='borderless'
                style={{
                  fontSize: '16px',
                  padding: 0,
                  backgroundColor: 'transparent',
                }}
              />
            </div>
          </Form.Item>

          {/* 密码输入框 */}
          <Form.Item
            name='password'
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
            style={{ marginBottom: '20px' }}
          >
            <div
              className='flex items-center px-4 py-3'
              style={{
                backgroundColor: 'transparent',
                borderBottom: '1px solid #E5E5E5',
              }}
            >
              <span
                style={{
                  color: '#999999',
                  fontSize: '16px',
                  marginRight: '12px',
                  minWidth: '60px',
                }}
              >
                密码
              </span>
              <Input.Password
                placeholder='请输入密码'
                variant='borderless'
                style={{
                  fontSize: '16px',
                  padding: 0,
                  backgroundColor: 'transparent',
                }}
              />
            </div>
          </Form.Item>

          {/* 确认密码输入框 */}
          <Form.Item
            name='confirmPassword'
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                },
              }),
            ]}
            style={{ marginBottom: '40px' }}
          >
            <div
              className='flex items-center px-4 py-3'
              style={{
                backgroundColor: 'transparent',
                borderBottom: '1px solid #E5E5E5',
              }}
            >
              <span
                style={{
                  color: '#999999',
                  fontSize: '16px',
                  marginRight: '12px',
                  minWidth: '80px',
                }}
              >
                确认密码
              </span>
              <Input.Password
                placeholder='请再次输入密码'
                variant='borderless'
                style={{
                  fontSize: '16px',
                  padding: 0,
                  backgroundColor: 'transparent',
                }}
              />
            </div>
          </Form.Item>

          {/* 协议勾选 */}
          <div className='mb-6 flex items-center'>
            <div
              className='mr-3 flex h-5 w-5 cursor-pointer items-center justify-center rounded-full'
              style={{
                backgroundColor: agreedToTerms ? '#000' : 'transparent',
                border: `2px solid ${agreedToTerms ? '#0173F9' : '#C8C8C8'}`,
              }}
              onClick={() => setAgreedToTerms(!agreedToTerms)}
            >
              {agreedToTerms && (
                <svg width='12' height='12' viewBox='0 0 24 24' fill='white'>
                  <path d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z' />
                </svg>
              )}
            </div>
            <span style={{ color: '#000000', fontSize: '14px' }}>
              已阅读并同意
            </span>
            <Button
              type='link'
              style={{
                color: '#576B95',
                fontSize: '14px',
                padding: 0,
                height: 'auto',
                textDecoration: 'none',
                marginLeft: '4px',
              }}
            >
              软件许可及服务协议
            </Button>
          </div>

          {/* 注册按钮 */}
          <Form.Item style={{ marginBottom: '20px' }}>
            <Button
              type='primary'
              htmlType='submit'
              loading={isLoading}
              block
              disabled={!agreedToTerms}
              style={{
                backgroundColor: agreedToTerms ? '#000' : '#C8C8C8',
                borderRadius: '8px',
                height: '50px',
                fontSize: '17px',
                fontWeight: '400',
                boxShadow: 'none',
              }}
            >
              {isLoading ? '注册中...' : '注册'}
            </Button>
          </Form.Item>

          {/* 登录链接 */}
          <div className='text-center'>
            <Button
              type='link'
              onClick={onSwitchToLogin}
              disabled={isLoading}
              style={{
                color: '#576B95',
                fontSize: '16px',
                padding: 0,
                height: 'auto',
                textDecoration: 'none',
              }}
            >
              已有账号？立即登录
            </Button>
          </div>
        </Form>
      </div>
    </div>
  )
}

export default RegisterForm
