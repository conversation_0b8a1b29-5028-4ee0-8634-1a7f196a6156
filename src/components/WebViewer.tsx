import { memo, useEffect, useState } from 'react'
import { createPortal } from 'react-dom'
import { App as cap } from '@capacitor/app'

type Props = {
  url: string
  title: string
  isOpen: boolean
  onClose: () => void
}

const WebViewer = ({ url, title, isOpen, onClose }: Props) => {
  const [isLoading, setIsLoading] = useState(true)
  const [isAnimating, setIsAnimating] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)

  useEffect(() => {
    if (isOpen) {
      // 重置加载状态
      setIsLoading(true)
      // 开始显示组件
      setShouldRender(true)
      // 禁止背景滚动
      document.body.style.overflow = 'hidden'
      // 设置全局标志，禁用默认返回键处理
      window.shouldHandleBackButton = false
      // 短暂延迟后开始动画，让组件有时间渲染
      const timer = setTimeout(() => {
        setIsAnimating(true)
      }, 50)
      return () => clearTimeout(timer)
    } else {
      // 开始关闭动画
      setIsAnimating(false)
      // 恢复默认返回键处理
      window.shouldHandleBackButton = true
      // 动画结束后隐藏组件
      const timer = setTimeout(() => {
        setShouldRender(false)
        // 恢复背景滚动
        document.body.style.overflow = ''
      }, 350) // 稍微延长一点时间，确保动画完成
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  // 添加移动端返回键监听器
  useEffect(() => {
    if (!isOpen) return

    let backButtonListener: any = null

    const setupListener = async () => {
      backButtonListener = await cap.addListener('backButton', () => {
        // 只有当 WebViewer 打开时才处理返回键
        if (window.shouldHandleBackButton === false) {
          onClose()
        }
      })
    }

    setupListener()

    return () => {
      if (backButtonListener) {
        backButtonListener.remove()
      }
    }
  }, [isOpen, onClose])

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      document.body.style.overflow = ''
      window.shouldHandleBackButton = true
    }
  }, [])

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleBackdropClick = () => {
    onClose()
  }

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  if (!shouldRender) return null

  return createPortal(
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ease-out ${
        isAnimating
          ? 'bg-black bg-opacity-50 backdrop-blur-sm'
          : 'bg-black bg-opacity-0'
      }`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div
        className={`flex h-full max-h-full w-full max-w-6xl transform flex-col bg-white shadow-2xl transition-all duration-300 ease-out ${
          isAnimating
            ? 'translate-y-0 scale-100 opacity-100'
            : 'translate-y-4 scale-50 opacity-50'
        }`}
        onClick={handleContentClick}
        style={{
          transformOrigin: 'center center',
        }}
      >
        {/* 头部 */}
        <div className='relative flex items-center justify-between rounded-t-lg border-b border-gray-200 bg-white px-4 py-2'>
          {/* 加载进度条背景 */}
          {isLoading && (
            <div className="absolute inset-0 overflow-hidden rounded-t-lg">
              <div className="h-full w-full bg-gradient-to-r from-transparent via-blue-50 to-transparent animate-pulse"></div>
              <div 
                className="absolute inset-0 bg-gradient-to-r from-blue-100 via-blue-200 to-blue-100 opacity-30"
                style={{
                  animation: 'loading-sweep 2s ease-in-out infinite',
                  backgroundSize: '200% 100%',
                }}
              ></div>
            </div>
          )}
          
          <div className='relative z-10 min-w-0 flex-1'>
            <div className='flex items-center space-x-3'>
              <div>
                <h2 className='truncate text-base font-medium text-gray-900'>
                  {title}
                </h2>
                <p className='truncate text-xs text-gray-500'>{url}</p>
              </div>
            
            </div>
          </div>
          <button
            onClick={onClose}
            className='relative z-10 ml-4 rounded-full p-1.5 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600'
            aria-label='关闭'
          >
            <svg
              className='h-5 w-5'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className='relative flex-1 overflow-hidden rounded-b-lg'>
          <iframe
            src={url}
            className='h-full w-full border-0'
            title={title}
            onLoad={handleIframeLoad}
            sandbox='allow-scripts allow-same-origin allow-forms allow-popups'
          />
        </div>
      </div>
      
      {/* CSS动画样式 */}
      <style jsx>{`
        @keyframes loading-sweep {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>,
    document.body,
  )
}

export default memo(WebViewer)
