import React, { useCallback, useEffect, useState } from 'react'
import { StreamMessage } from '@/tina/lib/EmotionMindClient.browser'
import { chatServiceManager } from '@/tina/services/chat-service-manager'
import WechatDialog from '@/components/WechatDialog'
import WechatInputDialog from '@/components/WechatInputDialog'

interface ConfirmationDialogManagerProps {
  // 可以通过 props 传入自定义的消息处理逻辑
  onConfirmMessage?: (userInput?: string) => Promise<void>
}

/**
 * 确认对话框管理器
 * 独立管理确认对话框的显示和处理逻辑
 * 监听全局的确认对话框事件
 */
export const ConfirmationDialogManager: React.FC<
  ConfirmationDialogManagerProps
> = ({ onConfirmMessage }) => {
  const [dialogState, setDialogState] = useState<{
    isOpen: boolean
    message: StreamMessage | null
    requireInput: boolean
  }>({
    isOpen: false,
    message: null,
    requireInput: false,
  })

  // 显示确认对话框
  const showDialog = useCallback(
    (message: StreamMessage, requireInput: boolean) => {
      console.log('🔔 [ConfirmationDialogManager] 显示对话框:', {
        xml_type: message.metadata?.xml_type,
        requireInput,
      })
      setDialogState({
        isOpen: true,
        message,
        requireInput,
      })
    },
    [dialogState],
  )

  // 隐藏确认对话框
  const hideDialog = useCallback(() => {
    setDialogState({
      isOpen: false,
      message: null,
      requireInput: false,
    })
  }, [])

  // 处理确认操作
  const handleConfirm = useCallback(
    async (userInput?: string) => {
      try {
        if (onConfirmMessage) {
          // 使用自定义的消息处理逻辑
          await onConfirmMessage(userInput)
        } else {
          // 默认的消息处理逻辑
          let confirmMessage = '通知 TinaTask 继续执行'
          if (userInput && userInput.trim()) {
            confirmMessage = `${userInput.trim()}\n通知 TinaTask 继续执行`
          }
          await chatServiceManager.sendMessage(confirmMessage)
        }

        hideDialog()
      } catch (error) {
        console.error('发送确认消息失败:', error)
      }
    },
    [onConfirmMessage, hideDialog],
  )

  // 处理取消操作
  const handleCancel = useCallback(() => {
    hideDialog()
  }, [hideDialog])

  // 获取对话框标题
  const getDialogTitle = useCallback((message: StreamMessage | null) => {
    if (!message?.metadata) return '确认操作'
    const metadata = message.metadata as any
    switch (metadata.xml_type) {
      case 'change_plan':
        return '计划变更确认'
      case 'ask_user_info':
        return '信息确认'
      default:
        return '确认操作'
    }
  }, [])

  // 获取对话框内容
  const getDialogContent = useCallback((message: StreamMessage | null) => {
    if (!message?.metadata) return ''
    const metadata = message.metadata as any

    if (metadata.xml_type === 'ask_user_info') {
      try {
        const parsed = JSON.parse(metadata.content)
        return parsed.question || metadata.content
      } catch (_error) {
        return metadata.content
      }
    }
    return metadata.content
  }, [])

  // 监听全局确认对话框事件
  useEffect(() => {
    const handleConfirmationDialogEvent = (event: CustomEvent) => {
      const { message, requireInput } = event.detail
      showDialog(message, requireInput)
    }

    // 注册全局事件监听器
    window.addEventListener(
      'showConfirmationDialog',
      handleConfirmationDialogEvent as EventListener,
    )

    return () => {
      window.removeEventListener(
        'showConfirmationDialog',
        handleConfirmationDialogEvent as EventListener,
      )
    }
  }, [showDialog])

  // 暴露全局方法供外部调用
  useEffect(() => {
    // 将显示对话框的方法挂载到全局
    ;(window as any).showConfirmationDialog = (
      message: StreamMessage,
      requireInput: boolean,
    ) => {
      const event = new CustomEvent('showConfirmationDialog', {
        detail: { message, requireInput },
      })
      window.dispatchEvent(event)
    }

    return () => {
      delete (window as any).showConfirmationDialog
    }
  }, [])

  return (
    <>
      {/* 确认对话框 */}
      {dialogState.requireInput ? (
        <WechatInputDialog
          isOpen={dialogState.isOpen}
          title={getDialogTitle(dialogState.message)}
          content={getDialogContent(dialogState.message)}
          placeholder='请输入您的回复...'
          primaryButtonText='确认'
          secondaryButtonText='取消'
          requireInput={true}
          onPrimaryClick={(input: string) => handleConfirm(input)}
          onSecondaryClick={handleCancel}
          onClose={handleCancel}
        />
      ) : (
        <WechatDialog
          isOpen={dialogState.isOpen}
          title={getDialogTitle(dialogState.message)}
          content={getDialogContent(dialogState.message)}
          primaryButtonText='确认'
          secondaryButtonText='取消'
          onPrimaryClick={() => handleConfirm()}
          onSecondaryClick={handleCancel}
          onClose={handleCancel}
        />
      )}
    </>
  )
}

export default ConfirmationDialogManager
