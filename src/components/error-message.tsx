import { memo } from 'react'
import { IonButton, IonIcon, IonText } from '@ionic/react'
import { refreshOutline, alertCircleOutline } from 'ionicons/icons'

interface ErrorMessageProps {
  message?: string
  onRetry?: () => void
  showRetry?: boolean
  type?: 'error' | 'warning' | 'info'
}

/**
 * 错误提示组件 - 微信风格
 */
export const ErrorMessage = memo(
  ({
    message = '加载失败，请重试',
    onRetry,
    showRetry = true,
    type = 'error',
  }: ErrorMessageProps) => {
    const getColor = () => {
      switch (type) {
        case 'warning':
          return '#FFC300'
        case 'info':
          return '#576B95'
        default:
          return '#FA5151'
      }
    }

    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '48px 16px',
          textAlign: 'center',
          backgroundColor: '#EDEDED',
          fontFamily:
            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        }}
      >
        <IonIcon
          icon={alertCircleOutline}
          style={{
            fontSize: '48px',
            color: getColor(),
            marginBottom: '16px',
          }}
        />

        <p
          style={{
            fontSize: '14px',
            color: '#888888',
            margin: '0 0 20px 0',
            lineHeight: '1.4',
            maxWidth: '240px',
          }}
        >
          {message}
        </p>

        {showRetry && onRetry && (
          <IonButton
            fill='clear'
            onClick={onRetry}
            size='small'
            style={{
              '--color': getColor(),
              '--background': 'transparent',
              '--background-hover': '#F5F5F5',
              '--border-radius': '20px',
              fontSize: '14px',
              fontWeight: '400',
              padding: '8px 16px',
              height: 'auto',
              minHeight: '36px',
            }}
          >
            <IonIcon
              icon={refreshOutline}
              slot='start'
              style={{ fontSize: '16px', marginRight: '4px' }}
            />
            重试
          </IonButton>
        )}
      </div>
    )
  },
)

ErrorMessage.displayName = 'ErrorMessage'
