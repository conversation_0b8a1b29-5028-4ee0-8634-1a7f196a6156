import React from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import { cn } from '../utils'
import { ServerError } from './markdown/server-error'
import { ShowToolResult } from './markdown/show-tool-result'
import { ToolCallBlock } from './tool-call-block'

interface MarkdownRendererProps {
  content: string
  variant?: 'preview' | 'full' | 'compact'
  className?: string
}

// 统一的 Markdown 组件配置
const getMarkdownComponents = (
  variant: 'preview' | 'full' | 'compact' = 'full',
) => {
  const isPreview = variant === 'preview'
  const isCompact = variant === 'compact'

  return {
    // 自定义工具组件
    toolcallblock: ToolCallBlock as any,
    servererror: ServerError as any,
    showtoolresult: ShowToolResult as any,
    // 自定义组件渲染 - 特殊处理包含块级元素的段落
    p: ({
      className: customClassName,
      children,
      ...props
    }: React.HTMLAttributes<HTMLParagraphElement>) => {
      const childrenArray = React.Children.toArray(children)

      const containsBlock = childrenArray.some((child) => {
        if (!React.isValidElement(child)) return false

        const type = child.type

        // 如果是 HTML 元素，比如 'div'、'pre'，就检测是否是 block 元素
        if (typeof type === 'string') {
          const blockTags = [
            'div',
            'section',
            'article',
            'table',
            'pre',
            'figure',
          ]
          return blockTags.includes(type)
        }
        // 自定义组件，默认视为 block
        return true
      })

      if (containsBlock) {
        return <>{children}</> // 不包裹 <p>
      }

      // 根据不同变体调整样式
      const className = isCompact
        ? 'mb-2 last:mb-0'
        : isPreview
          ? `text-sm mb-2 leading-6 text-gray-600`
          : `text-base mb-4 leading-relaxed text-gray-600`

      return (
        <p className={className} {...props}>
          {children}
        </p>
      )
    },
    h1: ({ children }: any) => (
      <h1
        className={
          isCompact
            ? 'mb-2 text-lg font-bold'
            : isPreview
              ? 'mb-2 text-lg font-semibold text-gray-800'
              : 'mb-4 text-2xl font-semibold text-gray-800'
        }
      >
        {children}
      </h1>
    ),
    h2: ({ children }: any) => (
      <h2
        className={
          isCompact
            ? 'mb-2 text-base font-bold'
            : isPreview
              ? 'mb-2 text-base font-semibold text-gray-700'
              : 'mb-3 text-xl font-semibold text-gray-700'
        }
      >
        {children}
      </h2>
    ),
    h3: ({ children }: any) => (
      <h3
        className={
          isCompact
            ? 'mb-1 text-sm font-bold'
            : isPreview
              ? 'mb-1 text-sm font-medium text-gray-700'
              : 'mb-2 text-lg font-medium text-gray-700'
        }
      >
        {children}
      </h3>
    ),
    h4: ({ children }: any) => (
      <h4
        className={`${isPreview ? 'text-sm' : 'text-base'} font-medium mb-${isPreview ? '1' : '2'} text-gray-600`}
      >
        {children}
      </h4>
    ),
    h5: ({ children }: any) => (
      <h5
        className={`${isPreview ? 'text-sm' : 'text-sm'} font-medium mb-${isPreview ? '1' : '2'} text-gray-600`}
      >
        {children}
      </h5>
    ),
    h6: ({ children }: any) => (
      <h6
        className={`${isPreview ? 'text-sm' : 'text-sm'} font-medium mb-${isPreview ? '1' : '2'} text-gray-600`}
      >
        {children}
      </h6>
    ),
    ul: ({ children }: any) => (
      <ul
        className={
          isCompact
            ? 'mb-2 pl-4 last:mb-0'
            : isPreview
              ? 'mb-2 list-disc pl-4 text-sm text-gray-600'
              : 'mb-4 list-disc pl-6 text-base text-gray-600'
        }
      >
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol
        className={
          isCompact
            ? 'mb-2 pl-4 last:mb-0'
            : isPreview
              ? 'mb-2 list-decimal pl-4 text-sm text-gray-600'
              : 'mb-4 list-decimal pl-6 text-base text-gray-600'
        }
      >
        {children}
      </ol>
    ),
    li: ({ children }: any) => (
      <li
        className={
          isCompact
            ? 'mb-1'
            : isPreview
              ? 'mb-1 text-sm leading-6'
              : 'mb-1 text-base leading-relaxed'
        }
      >
        {children}
      </li>
    ),
    blockquote: ({ children }: any) => (
      <blockquote
        className={
          isCompact
            ? 'border-l-4 border-gray-300 pl-4 italic'
            : isPreview
              ? 'mb-2 border-l-2 border-gray-300 pl-2 text-sm italic leading-6 text-gray-500'
              : 'mb-4 border-l-4 border-gray-300 pl-4 text-base italic leading-relaxed text-gray-500'
        }
      >
        {children}
      </blockquote>
    ),
    code: ({ children, className }: any) => {
      const match = /language-(\w+)/.exec(className || '')
      const isInline = !match

      if (isInline) {
        return (
          <code
            className={
              isCompact
                ? 'rounded bg-gray-100 px-1 py-0.5 text-sm'
                : 'rounded bg-gray-100 px-1.5 py-0.5 font-mono text-sm text-gray-700'
            }
          >
            {children}
          </code>
        )
      }

      return (
        <pre
          className={
            isCompact
              ? 'overflow-x-auto rounded bg-gray-100 p-2 text-sm'
              : isPreview
                ? 'mb-1 overflow-x-auto rounded-lg bg-gray-50 p-1 text-sm'
                : 'mb-1 overflow-x-auto rounded-lg bg-gray-50 p-2 text-sm'
          }
        >
          <code className={`${className} text-gray-700`}>{children}</code>
        </pre>
      )
    },
    table: ({ children }: any) => (
      <div
        className={
          isCompact
            ? 'mb-2 overflow-x-auto'
            : isPreview
              ? 'mb-2 overflow-x-auto'
              : 'mb-4 overflow-x-auto'
        }
      >
        <table
          className={
            isCompact
              ? 'min-w-full border-collapse border border-gray-200 text-sm'
              : isPreview
                ? 'min-w-full border-collapse border border-gray-200 text-sm'
                : 'min-w-full border-collapse border border-gray-300 text-base'
          }
        >
          {children}
        </table>
      </div>
    ),
    th: ({ children }: any) => (
      <th
        className={
          isCompact
            ? 'border border-gray-200 bg-gray-50 px-2 py-1 text-left text-xs font-medium text-gray-700'
            : isPreview
              ? 'border border-gray-200 bg-gray-50 px-3 py-2 text-left text-sm font-medium text-gray-700'
              : 'border border-gray-300 bg-gray-50 px-4 py-2 text-left text-base font-semibold text-gray-700'
        }
      >
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td
        className={
          isCompact
            ? 'border border-gray-200 px-2 py-1 text-xs text-gray-600'
            : isPreview
              ? 'border border-gray-200 px-3 py-2 text-sm text-gray-600'
              : 'border border-gray-300 px-4 py-2 text-base text-gray-600'
        }
      >
        {children}
      </td>
    ),
    thead: ({ children }: any) => (
      <thead className='bg-gray-50'>{children}</thead>
    ),
    tbody: ({ children }: any) => <tbody>{children}</tbody>,
    tr: ({ children }: any) => (
      <tr className='hover:bg-gray-50/50'>{children}</tr>
    ),
    a: ({ href, children }: any) => (
      <a
        href={href}
        className={`text-blue-${isPreview ? '500' : '600'} hover:text-blue-${isPreview ? '700' : '800'} underline ${isPreview ? 'text-sm' : 'text-base'}`}
        {...(!isPreview && { target: '_blank', rel: 'noopener noreferrer' })}
      >
        {children}
      </a>
    ),
    img: ({ src, alt }: any) => (
      <img
        src={src}
        alt={alt}
        className={`h-auto max-w-full rounded mb-${isPreview ? '2' : '4'} ${isPreview ? 'max-h-24' : 'max-h-96'} object-cover`}
      />
    ),
    strong: ({ children }: any) => (
      <strong className='font-semibold text-gray-700'>{children}</strong>
    ),
    em: ({ children }: any) => (
      <em className='italic text-gray-600'>{children}</em>
    ),
  }
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  variant = 'full',
  className = '',
}) => {
  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeHighlight]}
        skipHtml={false}
        components={getMarkdownComponents(variant)}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}

export default MarkdownRenderer
