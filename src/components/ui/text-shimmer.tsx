import { cn } from '../../utils'

interface TextShimmerProps {
  children: React.ReactNode
  duration?: number
  className?: string
}

export function TextShimmer({
  children,
  duration = 1.2,
  className,
}: TextShimmerProps) {
  return (
    <span
      className={cn(
        'inline-block animate-pulse bg-gradient-to-r from-gray-400 via-gray-600 to-gray-400 bg-clip-text text-transparent',
        className,
      )}
      style={{
        animationDuration: `${duration}s`,
      }}
    >
      {children}
    </span>
  )
}
