import { IonButton, IonIcon } from '@ionic/react'
import { isPlatform } from '@ionic/react'
import { arrowBackOutline } from 'ionicons/icons'
import { useHistory } from 'react-router-dom'

interface BackButtonProps {
  /** 自定义返回路径，如果不提供则使用智能返回逻辑 */
  to?: string
  /** 默认返回路径，当无法使用历史记录返回时的备选路径 */
  defaultHref?: string
  /** 按钮文本，默认为空（只显示图标） */
  text?: string
  /** 按钮样式 */
  fill?: 'clear' | 'outline' | 'solid'
  /** 按钮颜色 */
  color?: string
  /** 按钮大小 */
  size?: 'small' | 'default' | 'large'
  /** 自定义类名 */
  className?: string
  /** 点击回调 */
  onClick?: () => void
}

/**
 * 移动端页面头部返回按钮
 * 专门用于 IonHeader 中的返回按钮
 */
export function HeaderBackButton({
  to,
  defaultHref = '/',
  ...props
}: Omit<BackButtonProps, 'fill' | 'size'>) {
  const history = useHistory()

  const handleClick = () => {
    props.onClick?.()

    if (to) {
      // 如果指定了具体路径，直接跳转
      history.push(to)
    } else {
      const hasHistory = window.history.length > 1
      if (hasHistory) {
        history.goBack()
      } else {
        history.push(defaultHref)
      }
    }
  }

  return (
    <IonButton
      fill='clear'
      size='default'
      onClick={handleClick}
      className={props.className}
      style={{
        '--color': '#8E8E93',
        '--color-hover': '#6D6D80',
        '--color-focused': '#6D6D80',
      }}
    >
      <IonIcon icon={arrowBackOutline} style={{ fontSize: '20px' }} />
    </IonButton>
  )
}
