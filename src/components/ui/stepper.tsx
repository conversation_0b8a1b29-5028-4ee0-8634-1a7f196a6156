'use client'

import * as React from 'react'
import { createContext, useContext } from 'react'
import { CheckIcon } from '@radix-ui/react-icons'
import {
  LoaderCircle,
  ChevronRight,
  CheckCircle2,
  XCircle,
  Circle,
} from 'lucide-react'
import { cn } from '../../utils'
import MarkdownRenderer from '../MarkdownRenderer'
import { TextShimmer } from './text-shimmer'

// Types
type StepStatus = 'pending' | 'running' | 'success' | 'error'

type StepperContextValue = {
  activeStep: number
  setActiveStep: (step: number) => void
  orientation: 'horizontal' | 'vertical'
  expandedSteps: Set<number>
  toggleStep: (step: number) => void
  setExpandedSteps: React.Dispatch<React.SetStateAction<Set<number>>>
}

type StepItemContextValue = {
  step: number
  state: StepState
  isDisabled: boolean
  isLoading: boolean
  status: StepStatus
}

type StepState = 'active' | 'completed' | 'inactive' | 'loading'

// Contexts
const StepperContext = createContext<StepperContextValue | undefined>(undefined)
const StepItemContext = createContext<StepItemContextValue | undefined>(
  undefined,
)

const useStepper = () => {
  const context = useContext(StepperContext)
  if (!context) {
    throw new Error('useStepper must be used within a Stepper')
  }
  return context
}

const useStepItem = () => {
  const context = useContext(StepItemContext)
  if (!context) {
    throw new Error('useStepItem must be used within a StepperItem')
  }
  return context
}

// Components
interface StepperProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: number
  value?: number
  onValueChange?: (value: number) => void
  orientation?: 'horizontal' | 'vertical'
  expandedSteps?: Set<number>
  onExpandedStepsChange?: (steps: Set<number>) => void
  onToggleStep?: (step: number, expanded: boolean) => void
}

const Stepper = React.forwardRef<HTMLDivElement, StepperProps>(
  (
    {
      defaultValue = 0,
      value,
      onValueChange,
      orientation = 'horizontal',
      className,
      expandedSteps: controlledExpandedSteps,
      onExpandedStepsChange,
      onToggleStep,
      ...props
    },
    ref,
  ) => {
    const [activeStep, setInternalStep] = React.useState(defaultValue)
    const [expandedSteps, setExpandedSteps] = React.useState<Set<number>>(
      new Set([defaultValue]),
    )

    const setActiveStep = React.useCallback(
      (step: number) => {
        if (value === undefined) {
          setInternalStep(step)
        }
        onValueChange?.(step)
      },
      [value, onValueChange],
    )

    const toggleStep = React.useCallback(
      (step: number) => {
        if (controlledExpandedSteps !== undefined) {
          // 受控模式，调用外部 onToggleStep
          if (onToggleStep) {
            const isExpanded = controlledExpandedSteps.has(step)
            onToggleStep(step, !isExpanded)
          }
          return
        }
        // 非受控模式，本地切换
        setExpandedSteps((prev) => {
          const next = new Set(prev)
          if (next.has(step)) {
            next.delete(step)
          } else {
            next.add(step)
          }
          return next
        })
      },
      [controlledExpandedSteps, onToggleStep],
    )

    const currentStep = value ?? activeStep
    const currentExpandedSteps = controlledExpandedSteps ?? expandedSteps

    return (
      <StepperContext.Provider
        value={{
          activeStep: currentStep,
          setActiveStep,
          orientation,
          expandedSteps: currentExpandedSteps,
          toggleStep,
          setExpandedSteps,
        }}
      >
        <div
          ref={ref}
          className={cn(
            'group/stepper inline-flex data-[orientation=horizontal]:w-full data-[orientation=horizontal]:flex-row data-[orientation=vertical]:flex-col',
            className,
          )}
          data-orientation={orientation}
          {...props}
        />
      </StepperContext.Provider>
    )
  },
)
Stepper.displayName = 'Stepper'

// StepperItem
interface StepperItemProps extends React.HTMLAttributes<HTMLDivElement> {
  step: number
  completed?: boolean
  disabled?: boolean
  loading?: boolean
  status?: StepStatus
}

const StepperItem = React.forwardRef<HTMLDivElement, StepperItemProps>(
  (
    {
      step,
      completed = false,
      disabled = false,
      loading = false,
      status = 'pending',
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const { activeStep, expandedSteps } = useStepper()

    const state: StepState =
      completed || step < activeStep
        ? 'completed'
        : activeStep === step
          ? 'active'
          : 'inactive'

    const isLoading = loading && step === activeStep
    const isExpanded = expandedSteps.has(step)

    return (
      <StepItemContext.Provider
        value={{ step, state, isDisabled: disabled, isLoading, status }}
      >
        <div
          ref={ref}
          className={cn(
            'group/step flex items-center group-data-[orientation=horizontal]/stepper:flex-row group-data-[orientation=vertical]/stepper:flex-col',
            className,
          )}
          data-state={state}
          data-status={status}
          data-expanded={isExpanded}
          {...(isLoading ? { 'data-loading': true } : {})}
          {...props}
        >
          {children}
        </div>
      </StepItemContext.Provider>
    )
  },
)
StepperItem.displayName = 'StepperItem'

// StepperTrigger
interface StepperTriggerProps extends React.HTMLAttributes<HTMLDivElement> {
  asChild?: boolean
}

const StepperTrigger = React.forwardRef<HTMLDivElement, StepperTriggerProps>(
  ({ asChild = false, className, children, ...props }, ref) => {
    if (asChild) {
      return <div className={className}>{children}</div>
    }

    return (
      <div
        ref={ref}
        className={cn(
          'inline-flex w-full items-center gap-3 pb-6 text-left last:pb-0',
          className,
        )}
        {...props}
      >
        {children}
      </div>
    )
  },
)
StepperTrigger.displayName = 'StepperTrigger'

// StepperIndicator
interface StepperIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  asChild?: boolean
}

const StepperIndicator = React.forwardRef<
  HTMLDivElement,
  StepperIndicatorProps
>(({ asChild = false, className, children, ...props }, ref) => {
  const { state, step, isLoading, status } = useStepItem()

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <LoaderCircle className='h-4 w-4 animate-spin' />
      case 'success':
        return <CheckCircle2 className='h-4 w-4' />
      case 'error':
        return <XCircle className='h-4 w-4' />
      default:
        return <span className='text-xs font-medium'>{step}</span>
    }
  }

  return (
    <div
      ref={ref}
      className={cn(
        'relative flex size-6 shrink-0 items-center justify-center rounded-full',
        'bg-muted text-muted-foreground',
        'data-[status=running]:bg-blue-500 data-[status=running]:text-white',
        'data-[status=success]:bg-green-500 data-[status=success]:text-white',
        'data-[status=error]:bg-red-500 data-[status=error]:text-white',
        'data-[status=pending]:bg-muted data-[status=pending]:text-muted-foreground',
        className,
      )}
      data-state={state}
      data-status={status}
      {...props}
    >
      {asChild ? (
        children
      ) : (
        <>
          {getStatusIcon()}
          {isLoading && (
            <span className='absolute transition-all'>
              <LoaderCircle
                className='animate-spin'
                size={14}
                strokeWidth={2}
                aria-hidden='true'
              />
            </span>
          )}
        </>
      )}
    </div>
  )
})
StepperIndicator.displayName = 'StepperIndicator'

// StepperTitle
const StepperTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => {
  const { toggleStep, expandedSteps } = useStepper()
  const { step, status } = useStepItem()
  const isExpanded = expandedSteps.has(step)
  const titleRef = React.useRef<HTMLDivElement>(null)

  const handleToggle = React.useCallback(() => {
    toggleStep(step)
  }, [step, toggleStep])

  React.useEffect(() => {
    if (status === 'running' && titleRef.current) {
      titleRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [status])

  return (
    <div
      className='flex items-center gap-2'
      ref={titleRef}
      onClick={handleToggle}
    >
      <div
        className={cn(
          'cursor-pointer select-none text-base font-semibold',
          status === 'pending' && 'text-muted-foreground',
          className,
        )}
      >
        <h3 ref={ref} {...props}>
          {typeof children === 'string' && status === 'running' ? (
            <TextShimmer
              duration={1.2}
              className='[--base-color:#a1a1aa] [--base-gradient-color:#000] dark:[--base-color:#71717a] dark:[--base-gradient-color:#ffffff]'
            >
              {children}
            </TextShimmer>
          ) : (
            children
          )}
        </h3>
      </div>

      <ChevronRight
        className={cn(
          'text-muted-foreground h-4 w-4 flex-shrink-0 transition-transform duration-200',
          isExpanded && 'rotate-90',
        )}
      />
    </div>
  )
})
StepperTitle.displayName = 'StepperTitle'

// StepperDescription
const StepperDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { expandedSteps } = useStepper()
  const { step } = useStepItem()
  const isExpanded = expandedSteps.has(step)

  // 获取摘要内容
  const getSummary = (content: React.ReactNode) => {
    // 如果是 MarkdownRenderer 组件，获取其 content 属性
    if (React.isValidElement(content) && content.type === MarkdownRenderer) {
      const element = content as React.ReactElement<{ content: string }>
      const markdownContent = element.props.content
      // 移除 Markdown 标记，保留换行但移除多余空行
      const plainText = markdownContent
        .replace(/[#*`_~\[\]]/g, '') // 移除 Markdown 语法标记
        .replace(/\n\s*\n/g, '\n') // 将多个连续空行替换为单个换行
        .trim()
      return plainText
    }
    return ''
  }

  return (
    <>
      <div
        className={cn(
          'transition-all duration-200',
          isExpanded ? 'opacity-100' : 'max-h-0 overflow-hidden opacity-0',
        )}
      >
        <div
          ref={ref}
          className={cn('text-muted-foreground', className)}
          {...props}
        >
          {children}
        </div>
      </div>
    </>
  )
})
StepperDescription.displayName = 'StepperDescription'

// StepperSeparator
const StepperSeparator = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'm-0.5 group-data-[orientation=horizontal]/stepper:h-0.5 group-data-[orientation=vertical]/stepper:h-12 group-data-[orientation=horizontal]/stepper:w-full group-data-[orientation=vertical]/stepper:w-0.5 group-data-[orientation=horizontal]/stepper:flex-1',
        className,
      )}
      {...props}
    />
  )
})
StepperSeparator.displayName = 'StepperSeparator'

export {
  Stepper,
  StepperDescription,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
}
