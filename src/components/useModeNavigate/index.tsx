import { useCallback } from 'react'
import { getModeValueSnapshot } from '@/stateV2/mode'
import { showToast } from '@/wechatComponents/Toast'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router-dom'

type Options = {
  errorMsg?: string
  silence?: boolean
}

export default function useModeNavigate(options?: Options): History {
  const { t, i18n } = useTranslation()
  const { errorMsg = t('base.safeNavigateNotice'), silence = false } =
    options ?? {}
  const baseHistory = useHistory()

  const safePush = useCallback(
    (path: string, state?: any) => {
      if (getModeValueSnapshot() === 'edit') {
        !silence &&
          showToast({
            type: 'error',
            content: errorMsg,
          })
        return
      }
      return baseHistory.push(path, state)
    },
    [baseHistory, errorMsg, silence, i18n.language],
  )

  const safeReplace = useCallback(
    (path: string, state?: any) => {
      if (getModeValueSnapshot() === 'edit') {
        !silence &&
          showToast({
            type: 'error',
            content: errorMsg,
          })
        return
      }
      return baseHistory.replace(path, state)
    },
    [baseHistory, errorMsg, silence, i18n.language],
  )

  return {
    ...baseHistory,
    push: safePush,
    replace: safeReplace,
  }
}
