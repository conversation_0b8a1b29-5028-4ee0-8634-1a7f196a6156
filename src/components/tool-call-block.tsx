import React, { useState } from 'react'
import { ChevronDownIcon, ChevronUpIcon, CommandIcon } from 'lucide-react'
import { cn } from '../utils'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from './ui/collapsible'

export type ToolCallBlockProps = {
  icon?: React.ReactNode
  title: string
  summary?: React.ReactNode
  details?: React.ReactNode
  status?: 'pending' | 'running' | 'success' | 'error'
  children?: React.ReactNode
  className?: string
} & React.HTMLAttributes<HTMLDivElement>

function ToolCallBlock({
  icon,
  title,
  summary,
  details,
  children,
  status = 'pending',
  className,
  ...props
}: ToolCallBlockProps) {
  const [isOpen, setIsOpen] = useState(false)

  const statusClasses = {
    pending: 'border-muted-foreground/40',
    running: 'border-muted-foreground/50',
    success: 'border-muted-foreground/60',
    error: 'border-muted-foreground/40',
  }

  const iconPlaceholder = icon || (
    <CommandIcon className='text-muted-foreground h-4 w-4' />
  )

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className='inline-block w-fit space-y-2'
    >
      <div
        className={cn(
          'flex flex-col overflow-clip border',
          'border-border bg-card text-card-foreground rounded-xl',
          statusClasses[status],
          className,
        )}
        {...props}
      >
        {/* Header */}
        <div className='flex items-center space-x-3 px-3 py-1'>
          {/* Icon */}
          <div className='flex-shrink-0'>{iconPlaceholder}</div>

          {/* Title & Summary */}
          <div className='flex min-h-5 min-w-0 flex-grow items-center space-x-2'>
            {summary && (
              <div className='text-muted-foreground/90 overflow-clip break-all text-xs'>
                {summary}
              </div>
            )}
          </div>

          {/* Expand Button */}
          {children && (
            <CollapsibleTrigger asChild>
              {isOpen ? (
                <ChevronUpIcon className='h-6 w-6' />
              ) : (
                <ChevronDownIcon className='h-6 w-6' />
              )}
            </CollapsibleTrigger>
          )}
        </div>

        {/* Detailed Content (Collapsible) */}
        <CollapsibleContent className='text-muted-foreground data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down border-border overflow-hidden break-all border-t px-4 py-2'>
          {children && (
            <div className='break-all'>
              {React.Children.toArray(children).slice(0, 5)}
            </div>
          )}
        </CollapsibleContent>
      </div>
    </Collapsible>
  )
}

export { ToolCallBlock }
