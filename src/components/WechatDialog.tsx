import React from 'react'

interface WechatDialogProps {
  isOpen: boolean
  title: string
  content: string
  secondaryButtonText?: string
  primaryButtonText: string
  onSecondaryClick?: () => void
  onPrimaryClick: () => void
  onClose?: () => void
  loading?: boolean
}

const WechatDialog: React.FC<WechatDialogProps> = ({
  isOpen,
  title,
  content,
  secondaryButtonText = '取消',
  primaryButtonText,
  onSecondaryClick,
  onPrimaryClick,
  onClose,
  loading = false,
}) => {
  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && onClose) {
      onClose()
    }
  }

  return (
    <div
      className='fixed inset-0 flex items-center justify-center bg-black bg-opacity-50'
      style={{
        zIndex: 10000,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={handleBackdropClick}
    >
      <div
        className='mx-4 w-full max-w-xs overflow-hidden rounded-2xl bg-white shadow-lg'
        style={{
          backgroundColor: 'white',
          borderRadius: '16px',
          maxWidth: '300px',
          width: '100%',
          margin: '0 16px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
        }}
      >
        {/* 标题 */}
        <div className='px-6 pb-4 pt-6'>
          <h3 className='text-center text-lg font-medium text-gray-900'>
            {title}
          </h3>
        </div>

        {/* 内容 */}
        <div className='px-6 pb-6'>
          <p className='text-center text-sm leading-relaxed text-gray-600'>
            {content}
          </p>
        </div>

        {/* 按钮区域 */}
        <div className='border-t border-gray-200'>
          {onSecondaryClick ? (
            <div className='grid grid-cols-2'>
              <button
                onClick={onSecondaryClick}
                disabled={loading}
                className='border-r border-gray-200 px-6 py-4 text-base text-gray-900 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                {secondaryButtonText}
              </button>
              <button
                onClick={onPrimaryClick}
                disabled={loading}
                className='flex items-center justify-center px-6 py-4 text-base font-medium text-blue-600 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                {loading ? (
                  <div className='flex items-center'>
                    <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent'></div>
                    处理中...
                  </div>
                ) : (
                  primaryButtonText
                )}
              </button>
            </div>
          ) : (
            <button
              onClick={onPrimaryClick}
              disabled={loading}
              className='flex w-full items-center justify-center px-6 py-4 text-base font-medium text-blue-600 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
            >
              {loading ? (
                <div className='flex items-center'>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent'></div>
                  处理中...
                </div>
              ) : (
                primaryButtonText
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default WechatDialog
