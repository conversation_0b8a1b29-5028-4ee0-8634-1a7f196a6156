import { memo } from 'react'
import { IonSkeletonText, IonItem, IonLabel } from '@ionic/react'

interface LoadingSkeletonProps {
  count?: number
  type?: 'list' | 'card' | 'text'
}

/**
 * 加载骨架屏组件 - 微信风格
 */
export const LoadingSkeleton = memo(
  ({ count = 3, type = 'list' }: LoadingSkeletonProps) => {
    const items = Array.from({ length: count }, (_, index) => index)

    if (type === 'card') {
      return (
        <div style={{ backgroundColor: '#EDEDED' }}>
          {items.map((index) => (
            <div
              key={index}
              style={{
                backgroundColor: '#FFFFFF',
                borderTop: '0.5px solid #EEEEEE',
                borderBottom:
                  index === items.length - 1 ? '0.5px solid #EEEEEE' : 'none',
                padding: '12px 16px',
                display: 'flex',
                alignItems: 'center',
                minHeight: '56px',
              }}
            >
              {/* 图标占位 */}
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '4px',
                  backgroundColor: '#F5F5F5',
                  marginRight: '12px',
                  flexShrink: 0,
                }}
              />
              {/* 内容占位 */}
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '4px',
                  }}
                >
                  <IonSkeletonText
                    animated
                    style={{
                      width: '60%',
                      height: '17px',
                      borderRadius: '4px',
                    }}
                  />
                  <IonSkeletonText
                    animated
                    style={{
                      width: '20%',
                      height: '12px',
                      borderRadius: '4px',
                    }}
                  />
                </div>
                <IonSkeletonText
                  animated
                  style={{ width: '80%', height: '13px', borderRadius: '4px' }}
                />
              </div>
            </div>
          ))}
        </div>
      )
    }

    if (type === 'text') {
      return (
        <div style={{ padding: '16px', backgroundColor: '#EDEDED' }}>
          {items.map((index) => (
            <IonSkeletonText
              key={index}
              animated
              style={{
                width: `${Math.random() * 40 + 60}%`,
                height: '14px',
                marginBottom: '8px',
                borderRadius: '4px',
              }}
            />
          ))}
        </div>
      )
    }

    // 默认列表类型 - 微信聊天列表风格
    return (
      <div style={{ backgroundColor: '#EDEDED' }}>
        {/* 分组标题骨架 */}
        <div
          style={{
            backgroundColor: '#FFFFFF',
            borderTop: '0.5px solid #EEEEEE',
            borderBottom: '0.5px solid #EEEEEE',
            padding: '12px 16px',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              backgroundColor: '#F5F5F5',
              marginRight: '12px',
            }}
          />
          <IonSkeletonText
            animated
            style={{ width: '30%', height: '17px', borderRadius: '4px' }}
          />
          <div style={{ flex: 1 }} />
          <IonSkeletonText
            animated
            style={{
              width: '10%',
              height: '14px',
              marginRight: '12px',
              borderRadius: '4px',
            }}
          />
          <div
            style={{
              width: '16px',
              height: '16px',
              borderRadius: '50%',
              backgroundColor: '#F5F5F5',
            }}
          />
        </div>

        {/* 任务列表项骨架 */}
        <div style={{ backgroundColor: '#FFFFFF' }}>
          {items.map((index) => (
            <div
              key={index}
              style={{
                padding: '12px 16px',
                display: 'flex',
                alignItems: 'center',
                borderBottom:
                  index === items.length - 1 ? 'none' : '0.5px solid #EEEEEE',
                minHeight: '56px',
              }}
            >
              {/* 状态图标占位 */}
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '4px',
                  backgroundColor: '#F5F5F5',
                  marginRight: '12px',
                  flexShrink: 0,
                }}
              />
              {/* 任务内容占位 */}
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '4px',
                  }}
                >
                  <IonSkeletonText
                    animated
                    style={{
                      width: '60%',
                      height: '17px',
                      borderRadius: '4px',
                    }}
                  />
                  <IonSkeletonText
                    animated
                    style={{
                      width: '20%',
                      height: '12px',
                      borderRadius: '4px',
                    }}
                  />
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <IonSkeletonText
                    animated
                    style={{
                      width: '50%',
                      height: '13px',
                      borderRadius: '4px',
                    }}
                  />
                  <IonSkeletonText
                    animated
                    style={{
                      width: '15%',
                      height: '12px',
                      borderRadius: '10px',
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  },
)

LoadingSkeleton.displayName = 'LoadingSkeleton'
