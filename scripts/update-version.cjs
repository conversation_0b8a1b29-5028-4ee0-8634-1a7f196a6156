#!/usr/bin/env node
/**
 * 自动递增版本号并同步配置、生成 update.json
 * 适配 Mac 环境，兼容 interface 写法
 */
const fs = require('fs');
const path = require('path');

const updateConfigPath = path.resolve(__dirname, '../src/data/updateConfig.ts');
const buildGradlePath = path.resolve(__dirname, '../android/app/build.gradle');
const distDir = path.resolve(__dirname, '../dist/version');
const apkDir = path.join(distDir, 'apks');
const updateJsonPath = path.join(distDir, 'update.json');

// 1. 读取 updateConfig.ts
const configText = fs.readFileSync(updateConfigPath, 'utf-8');
// 只匹配 export const updateConfig = { ... } 部分，忽略 interface
const configMatch = configText.match(/export const updateConfig\s*:?\s*UpdateConfig\s*=\s*({[\s\S]*?})/);
if (!configMatch) {
  console.error('未找到 updateConfig 对象');
  process.exit(1);
}
let configObjText = configMatch[1];
// 用 eval 解析对象（仅限受控环境）
let updateConfig = {};
try {
  // 替换 TS 字段为 JS
  configObjText = configObjText.replace(/([a-zA-Z0-9_]+):/g, '"$1":');
  updateConfig = eval('(' + configObjText + ')');
} catch (e) {
  console.error('解析 updateConfig 失败', e);
  process.exit(1);
}

// 2. 递增 versionCode
const oldVersionCode = updateConfig.versionCode;
const newVersionCode = oldVersionCode + 1;
updateConfig.versionCode = newVersionCode;

// 3. 写回 updateConfig.ts（只替换对象里的 versionCode，不动 interface）
const newConfigText = configText.replace(
  /(export const updateConfig[\s\S]*?versionCode: ?)\d+/,
  `$1${newVersionCode}`
);
fs.writeFileSync(updateConfigPath, newConfigText, 'utf-8');
console.log('updateConfig.ts 已更新 versionCode:', newVersionCode);

// 4. 同步 build.gradle
let gradleText = fs.readFileSync(buildGradlePath, 'utf-8');
gradleText = gradleText.replace(/versionCode \d+/, `versionCode ${newVersionCode}`);
gradleText = gradleText.replace(/versionName "[^"]*"/, `versionName "${updateConfig.versionName}"`);
fs.writeFileSync(buildGradlePath, gradleText, 'utf-8');
console.log('build.gradle 已同步 versionCode/versionName');

// 5. 生成 update.json
if (!fs.existsSync(apkDir)) fs.mkdirSync(apkDir, { recursive: true });
const apkName = `tina-chat-v${newVersionCode}.apk`;
const apkUrl = updateConfig.baseUrl.replace(/\/?$/, '/') + 'apks/' + apkName;
const updateJson = {
  versionCode: newVersionCode,
  versionName: updateConfig.versionName,
  updateNotes: updateConfig.updateNotes,
  apkUrl
};
fs.writeFileSync(updateJsonPath, JSON.stringify(updateJson, null, 2), 'utf-8');
console.log('update.json 已生成:', updateJsonPath);

// 6. 复制 APK
const apkSrc = path.resolve(__dirname, '../android/app/build/outputs/apk/release/app-release.apk');
const apkDest = path.join(apkDir, apkName);
if (fs.existsSync(apkSrc)) {
  fs.copyFileSync(apkSrc, apkDest);
  console.log('APK 已复制到:', apkDest);
} else {
  console.warn('未找到 APK 文件:', apkSrc);
} 