#!/bin/bash

# 显示执行的命令
set -e
set -x

# 清理之前的构建文件
echo "====== 清理构建文件 ======"
rm -rf dist

# 确保输出目录存在
mkdir -p dist-android

# 构建Web应用
echo "====== 构建Web应用 ======"
pnpm build:android

# 备份当前配置
echo "====== 备份开发配置 ======"
if [ ! -f "capacitor.config.dev.ts" ]; then
  cp capacitor.config.ts capacitor.config.dev.ts
fi

# 应用生产配置
echo "====== 应用生产配置 ======"
cp capacitor.config.prod.ts capacitor.config.ts

# 清理Android项目缓存
echo "====== 清理Android项目缓存 ======"
cd android
./gradlew clean
cd ..

# 同步到Android项目
echo "====== 同步到Android项目 ======"
npx cap sync android --deployment

# 生成签名密钥（如果不存在）
echo "====== 检查签名密钥 ======"
KEYSTORE_PATH="android/app/tina-app-key.keystore"
if [ ! -f "$KEYSTORE_PATH" ]; then
  echo "====== 生成签名密钥 ======"
  keytool -genkeypair \
    -alias tina-app-key \
    -keyalg RSA \
    -keysize 2048 \
    -validity 10000 \
    -keystore "$KEYSTORE_PATH" \
    -storepass tinachat2024 \
    -keypass tinachat2024 \
    -dname "CN=Tina Chat,O=Tina,L=Beijing,C=CN"
else
  echo "签名密钥已存在，跳过生成步骤"
fi

# 恢复开发配置
echo "====== 恢复开发配置 ======"
cp capacitor.config.dev.ts capacitor.config.ts

# 构建签名APK
echo "====== 构建签名APK ======"
cd android
./gradlew assembleRelease

# 检查APK是否构建成功
if [ ! -f "app/build/outputs/apk/release/app-release.apk" ]; then
  echo "APK构建失败！"
  exit 1
fi

# 复制APK到输出目录
echo "====== 复制APK到输出目录 ======"
VERSION=$(grep '"version":' ../package.json | cut -d '"' -f 4)
DATE=$(date +%Y-%m-%d)
APK_NAME="Tina-Chat-v${VERSION}-${DATE}.apk"

cp app/build/outputs/apk/release/app-release.apk "../dist-android/${APK_NAME}"

# 显示结果
echo ""
echo "====== 构建完成! ======"
echo "APK已生成: $(cd .. && pwd)/dist-android/${APK_NAME}"
echo ""

# 显示APK文件大小
APK_SIZE=$(du -h "../dist-android/${APK_NAME}" | cut -f1)
echo "APK文件大小: ${APK_SIZE}"
echo "" 