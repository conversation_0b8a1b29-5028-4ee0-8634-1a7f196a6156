# 用户引导页面（Onboarding Page）开发文档

## 概述

用户引导页面是一个完整的用户入门体验流程，包括欢迎界面、问答流程、AI思考过程展示和完成引导功能。页面采用聊天应用的设计规范，提供一致的用户体验。

## 最新更新（UI和交互流程改进）

### UI布局改进
1. **页面结构调整**：
   - 添加顶部 AppBar（OnboardingHeader）
   - 添加底部 Footer（OnboardingFooter），使用 `onboarding_bottom_text.png` 图片
   - 移除居中布局，改为从上到下的垂直流式布局
   - 内容区域使用 `space-y-4` 间距

2. **聊天气泡样式**：
   - AI回复使用聊天气泡样式（ChatBubble 组件）
   - 白色背景，圆角边框，带阴影
   - 气泡尖角指向头像
   - 机器人头像只在第一条消息显示

### 交互流程改进
3. **欢迎界面流程**：
   - 移除"遇见您独属的 Tina"确认按钮
   - 欢迎文字显示完成后自动进入问答阶段
   - 欢迎文字使用聊天气泡样式

4. **问答状态保持**：
   - 用户选择选项后，AI回复保留在界面上
   - 添加消息历史记录功能
   - 不清除之前的提示文字和AI回复

5. **"稍后再说"处理**：
   - 移除"稍后再说"选项按钮
   - 改为可点击的灰色文字，位于选项按钮下方
   - 具有完整的点击交互功能

## 功能特性

### 1. 欢迎界面
- 显示机器人头像
- 打字机效果的欢迎文字
- 渐进式文字显示
- 动画按钮出现

### 2. 问答流程
- 4个静态问题（年龄、生活节奏、兴趣、交流风格）
- 多选项按钮界面
- AI模拟回复
- 清屏切换效果
- 防重复点击保护

### 3. 思考过程展示
- 头像位置动画移动
- 思考文字流式显示
- 自动内容折叠
- 平滑过渡动画

### 4. 完成引导
- 欢迎完成文字
- 进入聊天按钮
- 页面跳转功能

## 技术实现

### 核心组件
- `OnboardingPage`: 主页面组件
- `OnboardingHeader`: 顶部导航栏，包含返回按钮和标题
- `OnboardingFooter`: 底部文字区域，显示 slogan 图片
- `ChatBubble`: 聊天气泡组件，用于显示AI消息
- `TypewriterText`: 打字机效果组件
- `questionData.ts`: 问答数据结构

### 状态管理
- 使用 React Hooks 管理组件状态
- 阶段切换：WELCOME → QUESTIONS → THINKING → COMPLETE
- 问答流程状态控制

### 样式设计
- 复用聊天页面的设计规范
- 使用 Tailwind CSS 样式
- 微信主题色彩搭配
- 响应式设计

## 路由配置

```typescript
// App.tsx
<Route exact path='/onboarding' component={OnboardingPage} />
```

## 测试方式

### 1. 直接访问
访问 `/onboarding` 路径直接测试引导流程

### 2. 登录注册后自动跳转
- 登录成功后自动跳转到引导页面
- 注册成功后自动跳转到引导页面

### 3. 完整流程测试
1. 欢迎界面 → 点击"遇见您独属的 Tina"
2. 问答流程 → 依次回答4个问题
3. 思考过程 → 观察AI思考动画
4. 完成引导 → 点击"开始聊天"进入主界面

## 数据结构

### 问题结构
```typescript
interface Question {
  id: string
  text: string
  options: QuestionOption[]
  introduction?: string
}
```

### AI回复结构
```typescript
interface AIResponse {
  summary: string
  nextIntroduction: string
}
```

## 优化特性

1. **防重复点击**: 添加处理状态防止用户快速点击
2. **状态重置**: 问题切换时自动重置相关状态
3. **错误处理**: 边界情况处理和容错机制
4. **动画优化**: 平滑的过渡动画和视觉反馈
5. **资源优化**: 正确的图片导入和路径处理

## 文件结构

```
src/pages/onboarding/
├── index.tsx              # 主页面组件
├── questionData.ts        # 问答数据配置
├── OnboardingHeader.tsx   # 顶部导航栏组件
├── OnboardingFooter.tsx   # 底部文字组件
└── ChatBubble.tsx         # 聊天气泡组件

src/components/
└── TypewriterText.tsx     # 打字机效果组件

src/assets/
└── onboarding_bottom_text.png  # 底部文字图片

docs/
└── onboarding-page-guide.md    # 本文档
```

## 注意事项

1. 确保机器人头像资源路径正确
2. 问答数据可根据需求调整
3. 动画时间可根据用户体验优化
4. 样式与主应用保持一致
5. 测试各种设备和屏幕尺寸

## 后续扩展

1. 可添加更多问题类型
2. 支持动态问题配置
3. 添加数据收集和分析
4. 支持多语言
5. 个性化推荐算法集成
