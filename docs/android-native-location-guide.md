# Android 原生定位实现指南

## 概述

本项目实现了 Android 原生定位功能，绕过 Google Play Services，适用于没有 Google Play Services 的设备（如华为手机等）。

## 实现方案

### 1. 技术架构

- **Capacitor 插件**: 创建自定义插件 `NativeLocationPlugin`
- **Android 原生**: 使用 `LocationManager` 直接调用系统定位服务
- **Web 后备**: 在 Web 环境下使用 `navigator.geolocation`

### 2. 文件结构

```
src/plugins/
├── NativeLocation.ts              # 插件接口定义
└── web/
    └── NativeLocationWeb.ts       # Web 端实现

android/app/src/main/java/tina/chat/
└── NativeLocationPlugin.java     # Android 原生实现

src/utils/
└── locationService.ts             # 定位服务封装

src/pages/
├── LocationTest.tsx               # 定位功能测试页面
└── onboarding/
    └── ThinkingStage.tsx          # 集成定位的引导页面
```

### 3. 核心功能

#### 权限管理
- 自动检查 `ACCESS_FINE_LOCATION` 和 `ACCESS_COARSE_LOCATION` 权限
- 动态请求权限，支持权限回调处理
- 处理权限被拒绝的情况

#### 定位服务
- 优先使用 GPS 提供者（高精度）
- GPS 不可用时自动切换到网络定位
- 支持超时控制和错误处理
- 检查位置服务是否启用

#### 多重策略
- **策略1**: Android 原生定位（主要方案）
- **策略2**: Capacitor Geolocation（备用方案）
- **策略3**: Navigator Geolocation（Web 环境）

## 使用方法

### 1. 基本使用

```typescript
import { getCurrentLocationNative } from '../utils/locationService'

const getLocation = async () => {
  const result = await getCurrentLocationNative()
  
  if (result.success && result.location) {
    console.log('定位成功:', result.location)
    console.log(`纬度: ${result.location.latitude}`)
    console.log(`经度: ${result.location.longitude}`)
    console.log(`精度: ${result.location.accuracy}米`)
  } else {
    console.error('定位失败:', result.error?.message)
  }
}
```

### 2. 完整流程

```typescript
import { getLocationWithPermission } from '../utils/locationService'

const handleLocationRequest = async () => {
  const result = await getLocationWithPermission()
  
  if (result.success && result.location) {
    console.log('定位成功!')
    console.log(formatLocationInfo(result.location))
  } else {
    console.log('定位失败:', result.error?.message)
    console.log('权限状态:', result.permissionStatus)
  }
}
```

### 3. 在引导页面中集成

在 `ThinkingStage.tsx` 中，定位流程被集成到消息显示流程中：

```typescript
const handleTextComplete = async (index: number) => {
  if (index < completionTexts.length - 1) {
    // 在倒数第二条消息完成后执行定位流程
    if (index === completionTexts.length - 2) {
      console.log('开始定位流程...')
      setLocationProcessing(true)
      await handleLocationRequest()  // 阻塞执行
      setLocationProcessing(false)
      console.log('定位流程完成，显示最后一条消息')
    }
    setTimeout(() => setCurrentTextIndex((prev) => prev + 1), 1000)
  } else {
    // 最后一条消息完成后显示按钮
    setTimeout(() => setShowButton(true), 1500)
  }
}
```

## 测试方法

### 1. Web 环境测试

```bash
pnpm dev
# 访问 http://localhost:5173/new_mvp/#/location-test
```

### 2. Android 环境测试

```bash
pnpm build
npx cap sync
npx cap run android
```

### 3. 测试页面功能

- **检查权限状态**: 查看当前定位权限状态
- **请求定位权限**: 主动请求定位权限
- **获取当前位置 (Capacitor)**: 使用 Capacitor Geolocation 插件
- **获取当前位置 (Android 原生)**: 使用自定义原生插件
- **完整定位流程**: 权限检查 → 权限请求 → 获取位置

## 错误处理

### 常见错误码

- `GEOLOCATION_NOT_SUPPORTED`: 浏览器不支持地理位置API
- `LOCATION_DISABLED`: 位置服务未启用
- `PERMISSION_DENIED`: 定位权限被拒绝
- `NATIVE_LOCATION_ERROR`: 原生定位失败
- `LOCATION_FLOW_ERROR`: 定位流程异常

### 错误处理示例

```typescript
const result = await getCurrentLocationNative()

if (!result.success) {
  switch (result.error?.code) {
    case 'LOCATION_DISABLED':
      // 提示用户开启位置服务
      break
    case 'PERMISSION_DENIED':
      // 提示用户授予定位权限
      break
    case 'NATIVE_LOCATION_ERROR':
      // 尝试其他定位方案
      break
    default:
      // 通用错误处理
      break
  }
}
```

## 配置说明

### Android 权限配置

在 `android/app/src/main/AndroidManifest.xml` 中已添加：

```xml
<!-- Geolocation Plugin -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-feature android:name="android.hardware.location.gps" />
```

### 插件注册

在 `MainActivity.java` 中已注册：

```java
// 注册原生定位插件
registerPlugin(NativeLocationPlugin.class);
```

## 优势特点

1. **无依赖 Google Play Services**: 适用于华为等设备
2. **多重后备方案**: 确保在各种环境下都能工作
3. **权限自动管理**: 自动处理权限检查和请求
4. **错误处理完善**: 详细的错误码和错误信息
5. **阻塞式集成**: 支持在业务流程中阻塞执行定位

## 注意事项

1. **权限申请**: 首次使用需要用户授予定位权限
2. **位置服务**: 需要用户在系统设置中开启位置服务
3. **网络环境**: 网络定位需要网络连接
4. **精度差异**: GPS 定位精度高但耗时长，网络定位快但精度低
5. **电量消耗**: 高精度定位会消耗更多电量

## 后续优化

1. 添加位置缓存机制
2. 支持连续定位监听
3. 优化定位精度和速度平衡
4. 添加位置服务状态监听
5. 支持更多定位提供者选择
