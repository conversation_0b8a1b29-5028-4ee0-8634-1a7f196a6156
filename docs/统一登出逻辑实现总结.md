# 统一登出逻辑实现总结

## 概述

本次实现了一个统一的用户登出逻辑，当遇到以下情况时会自动执行登出操作：
- Token为空或无效
- 用户未登录状态
- HTTP响应状态码为400（Bad Request）
- HTTP响应状态码为401（Unauthorized）
- HTTP响应状态码为403（Forbidden）
- HTTP响应状态码为429（Too Many Requests）

## 实现的文件和功能

### 1. 核心工具函数 (`src/tina/lib/auth.ts`)

#### `performUnifiedLogout(reason?: string)`
统一登出逻辑的核心函数，执行以下步骤：
1. 断开聊天服务连接 (`chatServiceManager.disconnect()`)
2. 重置聊天服务管理器 (`chatServiceManager.reset()`)
3. 清除认证状态 (`auth.reset()`)
4. 等待操作完成
5. 跳转到登录页面 (`/new_mvp/`)

#### `shouldPerformUnifiedLogout(statusCode?: number, error?: any)`
检查是否需要执行统一登出的判断函数：
- 检查HTTP状态码（400, 401, 403, 429）
- 检查错误消息中的认证相关关键词
- 检查当前认证状态

### 2. HTTP错误处理增强 (`src/tina/lib/fetch.ts`)

- 增强了 `HttpError` 类，添加了 `isAuthError()` 和 `shouldLogout()` 方法
- 在 `handleResponse` 函数中集成了统一登出检查
- 当检测到需要登出的HTTP错误时，异步执行统一登出逻辑

### 3. EmotionMindClient错误处理 (`src/tina/lib/EmotionMindClient.browser.ts`)

- 在 `fetchWithRetry` 方法中添加了统一登出检查
- 在SSE连接错误处理中添加了统一登出检查
- 对于认证相关错误，停止重试机制

### 4. Session API错误处理 (`src/tina/lib/session.ts`)

- 为 `sessionAxios` 添加了响应拦截器
- 自动检测认证失败和特定HTTP状态码
- 异步执行统一登出，不阻塞原始错误抛出

### 5. ConversationAPI错误处理 (`src/pages/wechat/conversation/context.tsx`)

- 在 `handleError` 回调中集成了统一登出检查
- 在 `loadHistoryMessages` 错误处理中添加了统一登出检查
- 确保聊天相关的认证错误能触发统一登出

### 6. 设置页面登出 (`src/pages/settings/index.tsx`)

- 将 `handleLogoutConfirm` 函数简化为使用统一登出逻辑
- 移除了重复的登出代码，统一使用 `performUnifiedLogout`

## 错误处理策略

### 触发条件
1. **HTTP状态码**: 400, 401, 403, 429
2. **错误关键词**: unauthorized, token, authentication, forbidden, invalid, 未登录, 认证失败, 令牌, 权限
3. **认证状态**: 用户未登录状态

### 执行流程
1. **检测阶段**: 各个API调用点检测到错误
2. **判断阶段**: 使用 `shouldPerformUnifiedLogout` 判断是否需要登出
3. **执行阶段**: 异步调用 `performUnifiedLogout` 执行登出
4. **清理阶段**: 断开连接、清除状态、跳转页面

### 容错机制
- 即使登出过程中出现错误，也会确保认证状态被清除
- 强制跳转到登录页面，确保用户不会停留在需要认证的页面
- 异步执行登出，不阻塞当前错误的正常抛出和处理

## 使用示例

### 手动触发登出
```typescript
import { performUnifiedLogout } from '@/tina/lib/auth'

// 手动登出
await performUnifiedLogout('用户手动登出')
```

### 检查是否需要登出
```typescript
import { shouldPerformUnifiedLogout } from '@/tina/lib/auth'

// 检查HTTP错误
if (shouldPerformUnifiedLogout(401)) {
  // 需要登出
}

// 检查错误对象
const error = new Error('Token expired')
if (shouldPerformUnifiedLogout(undefined, error)) {
  // 需要登出
}
```

## 测试覆盖

创建了测试文件 `src/tina/lib/__tests__/auth.test.ts`，覆盖：
- `shouldPerformUnifiedLogout` 的各种场景
- `performUnifiedLogout` 的正常和异常流程
- 错误处理和容错机制

## 优势

1. **统一性**: 所有认证相关错误都使用相同的处理逻辑
2. **完整性**: 确保所有用户数据和连接都被正确清理
3. **容错性**: 即使部分清理操作失败，也能确保基本的登出流程
4. **可维护性**: 集中管理登出逻辑，便于后续维护和修改
5. **用户体验**: 自动处理认证失败，用户无需手动刷新页面

## 注意事项

1. 统一登出是异步执行的，不会阻塞当前错误的处理
2. 页面跳转使用 `window.location.href`，会触发完整的页面刷新
3. 所有相关的状态和连接都会被清理，确保没有残留数据
4. 错误日志使用统一的前缀 `🚪 [UnifiedLogout]` 便于调试和监控
