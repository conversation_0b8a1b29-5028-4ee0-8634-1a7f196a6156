# Android 原生定位插件测试指南

## 问题修复

已修复 `"NativeLocation" plugin is not implemented on android` 错误：

### 修复内容
1. **编译错误修复**: 修复了 `NativeLocationPlugin.java` 中的方法名冲突
2. **插件注册**: 确保插件在 `MainActivity.java` 中正确注册
3. **测试方法**: 添加了 `testPlugin()` 方法用于验证插件连接

### 修复的文件
- `android/app/src/main/java/tina/chat/NativeLocationPlugin.java`
- `android/app/src/main/java/tina/chat/MainActivity.java`
- `src/plugins/NativeLocation.ts`
- `src/plugins/web/NativeLocationWeb.ts`
- `src/pages/LocationTest.tsx`

## 测试步骤

### 1. Web 环境测试

```bash
# 启动开发服务器
pnpm dev

# 访问测试页面
http://localhost:5173/new_mvp/#/location-test
```

**测试项目**:
- 🔧 测试插件连接 - 验证插件是否正常加载
- 检查权限状态 - 查看定位权限状态
- 请求定位权限 - 申请定位权限
- 获取当前位置 (Capacitor) - 使用 Capacitor Geolocation
- 获取当前位置 (Android 原生) - 使用自定义原生插件
- 完整定位流程 - 完整的权限+定位流程

### 2. Android 设备测试

```bash
# 构建项目
pnpm build

# 同步到 Android
npx cap sync

# 在 Android 设备上运行
npx cap run android
```

### 3. 引导页面测试

访问 `/onboarding` 页面，在思考阶段会自动执行定位流程。

## 预期结果

### Web 环境
- ✅ 插件测试: "NativeLocation plugin is working on web!"
- ✅ 定位功能: 使用 `navigator.geolocation` API

### Android 环境
- ✅ 插件测试: "NativeLocation plugin is working!"
- ✅ 权限申请: 自动弹出定位权限申请对话框
- ✅ 原生定位: 使用 Android LocationManager 获取位置
- ✅ 无需 Google Play Services

## 错误排查

### 常见问题

1. **插件未实现错误**
   ```
   "NativeLocation" plugin is not implemented on android
   ```
   **解决方案**: 已修复，确保重新构建和同步

2. **权限被拒绝**
   ```
   定位失败: 定位权限被拒绝
   ```
   **解决方案**: 在设备设置中手动授予定位权限

3. **位置服务未启用**
   ```
   定位失败: 位置服务未启用，请在设置中开启位置服务
   ```
   **解决方案**: 在 Android 设置中开启位置服务

4. **定位超时**
   ```
   定位失败: Location request timeout
   ```
   **解决方案**: 确保设备有良好的 GPS 信号或网络连接

## 调试信息

### 控制台日志
在浏览器开发者工具或 Android Studio Logcat 中查看详细日志：

```javascript
// 成功日志
console.log('插件测试成功:', result)
console.log('原生定位成功:', locationInfo)

// 错误日志
console.error('插件测试失败:', err)
console.error('原生定位失败:', error)
```

### Android Logcat
```bash
# 查看应用日志
adb logcat | grep "tina.chat"

# 查看 Capacitor 日志
adb logcat | grep "Capacitor"
```

## 技术细节

### 插件架构
- **TypeScript 接口**: `src/plugins/NativeLocation.ts`
- **Web 实现**: `src/plugins/web/NativeLocationWeb.ts`
- **Android 实现**: `android/app/src/main/java/tina/chat/NativeLocationPlugin.java`

### 定位策略
1. **Android 原生**: 优先使用 GPS，回退到网络定位
2. **Web 环境**: 使用 `navigator.geolocation`
3. **权限管理**: 自动检查和请求权限

### 集成方式
- **阻塞式**: 在引导页面中阻塞执行定位流程
- **非阻塞式**: 在测试页面中独立测试各项功能

## 下一步

1. **验证功能**: 在真实 Android 设备上测试所有功能
2. **性能优化**: 根据测试结果优化定位精度和速度
3. **错误处理**: 完善各种边界情况的处理
4. **用户体验**: 添加定位过程中的加载提示

## 成功标志

当您看到以下日志时，表示插件工作正常：

```
插件测试: NativeLocation plugin is working! (时间戳: 1234567890)
原生定位成功: {latitude: 39.9042, longitude: 116.4074, accuracy: 10, timestamp: 1234567890}
```

现在您可以在没有 Google Play Services 的 Android 设备上正常使用定位功能了！
