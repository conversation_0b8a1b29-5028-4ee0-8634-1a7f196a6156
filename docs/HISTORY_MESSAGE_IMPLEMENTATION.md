# 历史消息转换实现说明

## 概述

本文档说明了历史消息转换功能的实现，该功能将服务器返回的历史消息格式转换为前端对话组件可以使用的 `TConversationItem` 格式。

## 实现的功能

### 1. 用户消息处理
- ✅ 用户消息是完整的，不需要拼接
- ✅ 自动清理 `<send_time>` 标签
- ✅ 转换为 `EConversationType.text` 类型
- ✅ 角色映射：`user` → `mine`

### 2. 助手消息处理
- ✅ 检测并处理 `tool_calls`，显示为工具调用通知
- ✅ 处理 `metadata.user_maybe_say`，转换为用户建议组件
- ✅ 普通文本消息转换
- ✅ 角色映射：`assistant` → `friend`

### 3. 工具消息处理
- ✅ 根据工具名称显示不同的通知图标和文本
- ✅ 支持的工具类型：
  - `weather_query` - 天气查询
  - `search`/`web_search` - 搜索工具
  - `newsNow_hotspot_query` - 热点查询
  - `calculator` - 计算器
  - `img_generate` - 图片生成
  - `image_search` - 图片搜索
  - `tina_task_*` - TinaTask 相关工具
- ✅ 特殊处理 `tina_task_query_status` 的 `change_plan` 结果

### 4. 数据处理
- ✅ 时间戳解析和转换
- ✅ 消息ID生成和处理
- ✅ 空值和异常情况处理
- ✅ 混合结构体的安全访问

## 代码结构

### 主要函数

#### `convertHistoryMessageToTConversationItem(data: HistoryMessage, index: number): TConversationItem`
将单个历史消息转换为对话项目格式。

#### `cleanHistoryMessageContent(content: string): string`
清理历史消息内容，移除 `<send_time>` 标签。

#### `extractSendTimeFromContent(content: string): string | null`
提取历史消息中的发送时间（预留功能）。

### HistoryMessageService 类
- `loadHistoryMessages()` - 加载历史消息
- `hasMoreHistory()` - 检查是否有更多历史消息
- `isLoadingHistory()` - 检查是否正在加载
- `reset()` - 重置状态

## 示例转换

### 用户消息
```typescript
// 输入
{
  "id": "msg_user_123",
  "content": "最近还有什么好玩的游戏推荐吗？\n\n<send_time>2025-06-18T09:45:53+08:00</send_time>",
  "role": "user",
  "timestamp": "2025-06-18T01:45:53Z"
}

// 输出
{
  "id": "msg_user_123",
  "type": "text",
  "role": "mine",
  "textContent": [{ "text": "最近还有什么好玩的游戏推荐吗？" }],
  "sendTimestamp": 1718676353000
}
```

### 工具调用消息
```typescript
// 输入
{
  "id": "msg_assistant_tool",
  "content": "",
  "role": "assistant",
  "tool_calls": [
    {
      "id": "call_123",
      "name": "newsNow_hotspot_query",
      "parameters": { "limit_per_source": 3 }
    }
  ]
}

// 输出
{
  "id": "msg_assistant_tool",
  "type": "notification",
  "role": "friend",
  "notificationId": "msg_assistant_tool",
  "text": "正在调用 newsNow_hotspot_query...",
  "icon": "fa-solid fa-screwdriver-wrench"
}
```

## 待实现功能（TODO）

以下功能已在代码中标记为 TODO，需要后续实现：

1. **多工具调用支持** - 当一个消息包含多个 tool_calls 时的处理
2. **工具调用详细信息** - 根据 tool_calls 的具体内容显示更详细的信息
3. **特殊格式处理** - 历史消息中的 XML 卡片、markdown 等格式
4. **媒体内容处理** - 图片、视频、文件等媒体内容的转换
5. **其他角色类型** - 处理可能的其他消息角色
6. **发送时间使用** - 使用历史消息中的 `<send_time>` 标签

## 使用方法

```typescript
import { historyMessageService } from '@/tina/services/history-message'

// 加载历史消息
const messages = await historyMessageService.loadHistoryMessages(20)

// 检查是否有更多消息
const hasMore = historyMessageService.hasMoreHistory()

// 重置状态（用户切换时）
historyMessageService.reset()
```

## 注意事项

1. **空值处理** - 历史消息结构是混合结构体，大多数字段可能为空，代码中已做好空值检查
2. **类型安全** - 使用 TypeScript 确保类型安全，特别是 `tool_result` 的类型处理
3. **性能考虑** - 转换过程是同步的，对于大量消息可能需要考虑分批处理
4. **错误处理** - 服务类中包含完整的错误处理和日志记录

## 集成说明

该实现已集成到 `ConversationAPIProvider` 中的 `loadHistoryMessages` 函数，可以通过 `useConversationAPI` hook 使用。
