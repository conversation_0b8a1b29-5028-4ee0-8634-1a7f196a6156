# Ionic Framework 集成测试指南

## 已完成的迁移工作

### 1. 依赖管理
- ✅ 降级 React Router 从 v6 到 v5
- ✅ 安装 Ionic React 相关包：
  - `@ionic/react`
  - `@ionic/react-router`
  - `@ionic/core`

### 2. 路由系统迁移
- ✅ 将 `HashRouter` 替换为 `IonReactHashRouter`
- ✅ 将 `Routes`/`Route` 替换为 `IonRouterOutlet`/`Route`
- ✅ 更新所有页面组件使用 `IonPage` 和 `IonContent`

### 3. 组件更新
- ✅ HomePage: 使用 `IonPage` + `IonContent`
- ✅ TestPage: 使用 `IonPage` + `IonContent`
- ✅ Conversation: 使用 `IonPage` + `IonContent`
- ✅ 更新所有 `useNavigate` 为 `useHistory`

### 4. CSS 和主题
- ✅ 导入 Ionic CSS 核心文件
- ✅ 导入 Ionic 主题变量
- ✅ 配置 `setupIonicReact()`

## 测试项目

### 基本路由功能
1. **首页访问**: http://localhost:5173/new_mvp/
2. **测试页面**: http://localhost:5173/new_mvp/#/test
3. **对话页面**: http://localhost:5173/new_mvp/#/conversation/1

### 移动端特性测试
1. **页面栈管理**
   - 从首页导航到对话页面
   - 使用浏览器返回键
   - 检查页面历史是否正确维护

2. **硬件返回键**（在移动设备或模拟器中）
   - 测试 Android 返回键响应
   - 验证双击退出功能

3. **页面转场动画**
   - 观察页面切换时的动画效果
   - 验证 Ionic 的原生转场体验

### 功能验证
1. **导航功能**
   - 测试页面中的 Link 组件
   - 验证程序化导航（history.push）
   - 检查路由参数传递

2. **页面生命周期**
   - 验证页面加载和卸载
   - 检查状态保持

## 启动测试

```bash
# 开发模式
pnpm dev

# 构建测试
pnpm build

# 移动端测试（需要 Capacitor）
pnpm ionic:dev
```

## 预期结果

1. ✅ 所有路由正常工作
2. ✅ 页面切换流畅
3. ✅ 移动端体验优化
4. ✅ 硬件返回键响应
5. ✅ 页面栈管理正确

## 已知问题和解决方案

### 问题1: React Router v6 兼容性
- **问题**: Ionic React Router 不支持 React Router v6
- **解决**: 降级到 React Router v5

### 问题2: API 差异
- **问题**: `useNavigate` 在 v5 中不存在
- **解决**: 使用 `useHistory` 替代

### 问题3: 路由语法变化
- **问题**: v6 的 `element` 属性在 v5 中不支持
- **解决**: 使用 `component` 或 `render` 属性

## 下一步建议

1. **性能优化**: 考虑代码分割减少包大小
2. **移动端测试**: 在真实设备上测试
3. **动画定制**: 根据需要定制页面转场动画
4. **主题配置**: 根据应用设计调整 Ionic 主题
