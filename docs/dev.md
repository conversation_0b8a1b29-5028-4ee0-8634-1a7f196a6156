# Tina-Chat 开发文档

## 项目概述

Tina-Chat 是一个基于 React + Ionic + Capacitor 构建的跨平台聊天应用，模拟微信界面和交互体验。项目采用现代化的前端技术栈，支持 Web 和 Android 平台。

### 核心特性

- 🎨 **微信风格界面**: 高度还原微信的视觉设计和交互体验
- 📱 **跨平台支持**: 支持 Web 浏览器和 Android 原生应用
- 💬 **丰富消息类型**: 支持文本、图片、语音、视频、转账、红包等多种消息类型
- 🔄 **实时状态管理**: 基于 Jotai 的响应式状态管理
- 🌐 **国际化支持**: 内置 i18n 多语言支持
- 🎭 **模拟数据系统**: 完整的 Faker.js 模拟数据生成
- 🧪 **完善测试**: Vitest + Testing Library 测试框架

## 技术架构

### 技术栈

#### 核心框架
- **React 19.1.0**: 前端 UI 框架
- **Ionic 8.6.2**: 跨平台 UI 组件库
- **Capacitor 7.4.0**: 原生应用打包和插件系统
- **TypeScript 5.8.3**: 类型安全的 JavaScript 超集

#### 状态管理
- **Jotai 2.12.5**: 原子化状态管理
- **Zustand 5.0.6**: 轻量级状态管理（辅助）

#### 样式和 UI
- **Tailwind CSS 3.4.17**: 原子化 CSS 框架
- **Ant Design 5.18.3**: 企业级 UI 组件库
- **Emotion**: CSS-in-JS 解决方案
- **Animate.css**: CSS 动画库

#### 构建工具
- **Vite 6.3.5**: 现代化构建工具
- **SWC**: 高性能 JavaScript/TypeScript 编译器
- **PNPM**: 高效的包管理器

#### 开发工具
- **Vitest**: 单元测试框架
- **Prettier**: 代码格式化
- **ESLint**: 代码质量检查

### 项目结构

```
tina-webchat/
├── android/                    # Android 原生项目
├── docs/                      # 项目文档
├── public/                    # 静态资源
├── src/                       # 源代码
│   ├── assets/               # 静态资源文件
│   ├── components/           # 通用组件
│   │   ├── conversation/     # 对话相关组件
│   │   ├── ui/              # UI 基础组件
│   │   └── ...
│   ├── data/                # 模拟数据
│   ├── faker/               # 数据生成器
│   ├── hooks/               # 自定义 Hooks
│   ├── i18n/                # 国际化配置
│   ├── pages/               # 页面组件
│   │   ├── auth/            # 认证页面
│   │   ├── onboarding/      # 引导页面
│   │   ├── settings/        # 设置页面
│   │   ├── wechat/          # 微信主界面
│   │   └── ...
│   ├── stateV2/             # 状态管理
│   │   ├── conversation/    # 对话状态
│   │   ├── profile/         # 用户资料状态
│   │   └── ...
│   ├── tina/                # Tina AI 相关功能
│   ├── utils/               # 工具函数
│   └── wechatComponents/    # 微信风格组件
├── scripts/                 # 构建脚本
├── package.json            # 项目配置
├── vite.config.ts          # Vite 配置
├── capacitor.config.ts     # Capacitor 配置
└── tailwind.config.js      # Tailwind 配置
```

## 核心模块详解

### 1. 状态管理系统 (stateV2)

项目采用 Jotai 作为主要状态管理方案，提供原子化的状态管理能力。

#### 核心特性
- **原子化状态**: 每个状态单元都是独立的 atom
- **类型安全**: 完整的 TypeScript 类型定义
- **持久化存储**: 自动同步到本地存储
- **响应式更新**: 状态变化自动触发组件重渲染

#### 主要模块

**对话状态管理 (conversation/)**
```typescript
// 对话列表状态
export const conversationListAtom = atomFamily((id: IStateProfile['id']) => {
  return atomWithStorage<TStateConversationList>(
    `conversationList-${id}`,
    MOCK_INIT_CONVERSATION_LIST,
  )
})

// 输入框状态
export const inputterValueAtom = atom<Descendant[]>(SLATE_INITIAL_VALUE)
```

**用户资料状态 (profile/)**
```typescript
// 当前用户资料
export const currentProfileAtom = atomWithStorage<IStateProfile>(
  'currentProfile',
  DEFAULT_PROFILE
)

// 好友列表
export const friendListAtom = atomWithStorage<IStateProfile[]>(
  'friendList',
  MOCK_FRIEND_LIST
)
```

### 2. 消息类型系统

支持多种消息类型，每种类型都有完整的类型定义和组件实现。

#### 消息类型枚举
```typescript
export enum EConversationType {
  text = 'text',                    // 文本消息
  image = 'image',                  // 图片消息
  video = 'video',                  // 视频消息
  voice = 'voice',                  // 语音消息
  transfer = 'transfer',            // 转账消息
  redPacket = 'redPacket',          // 红包消息
  personalCard = 'personalCard',    // 个人名片
  news = 'news',                    // 新闻消息
  markdown = 'markdown',            // Markdown 消息
  centerText = 'centerText',        // 居中文本
}
```

#### 消息接口定义
```typescript
// 基础消息接口
export interface IConversationItemBase {
  id: string
  upperText?: string
  sendTimestamp?: number
  role: TConversationRole // 'mine' | 'friend'
}

// 文本消息
export interface IConversationTypeText extends IConversationItemBase {
  type: EConversationType.text
  textContent: Descendant[] // Slate 编辑器内容
  referenceId?: string      // 引用消息 ID
}

// 新闻消息
export interface IConversationTypeNews extends IConversationItemBase {
  type: EConversationType.news
  title: string
  description: string
  imageUrl: string
  source: string
  url: string
}
```

### 3. 组件系统

#### 通用组件 (components/)

**CommonBlock**: 所有消息的通用布局组件
- 提供统一的头像、气泡、时间显示
- 支持自定义样式和交互
- 自动处理消息方向（左右布局）

**WebViewer**: 全屏网页查看器
- 支持 iframe 嵌入外部网页
- 流畅的展开/收起动画
- 安全的沙盒模式

#### 微信风格组件 (wechatComponents/)

**SlateText**: 富文本编辑器组件
- 基于 Slate.js 的富文本编辑
- 支持表情、@提及等功能
- 完整的编辑器工具栏

**Toast**: 消息提示组件
- 微信风格的 Toast 提示
- 支持多种提示类型
- 自动消失和手动关闭

### 4. 页面路由系统

基于 Ionic React Router 的路由管理：

```typescript
// 主要路由配置
<IonRouterOutlet>
  <Route exact path='/' component={SplashPage} />
  <Route exact path='/home' component={HomePage} />
  <Route exact path='/onboarding' component={OnboardingPage} />
  <Route exact path='/conversation/:id' component={Conversation} />
  <Route exact path='/settings' component={MobileSettings} />
</IonRouterOutlet>
```

#### 主要页面

- **SplashPage**: 启动页面，应用初始化
- **HomePage**: 主页面，聊天列表和底部导航
- **Conversation**: 对话页面，消息展示和输入
- **OnboardingPage**: 引导页面，首次使用指导
- **Settings**: 设置页面，应用配置

## 开发指南

### 环境搭建

#### 系统要求
- Node.js >= 18
- PNPM >= 10.11.0
- Android Studio (Android 开发)
- Java 17+ (Android 构建)

#### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd tina-webchat

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

#### 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
# VITE_API_URL=http://localhost:8380
```

### 开发流程

#### 1. 创建新页面

```typescript
// src/pages/NewPage.tsx
import { IonContent, IonPage } from '@ionic/react'

const NewPage: React.FC = () => {
  return (
    <IonPage>
      <IonContent>
        <div className="p-4">
          <h1>新页面</h1>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default NewPage
```

#### 2. 添加路由

```typescript
// src/App.tsx
import NewPage from './pages/NewPage'

// 在 IonRouterOutlet 中添加路由
<Route exact path='/new-page' component={NewPage} />
```

#### 3. 创建新的消息类型

参考 [对话组件开发指南](./对话组件开发指南.md) 了解详细步骤。

#### 4. 状态管理

```typescript
// 创建新的 atom
export const newFeatureAtom = atomWithStorage<NewFeatureType>(
  'newFeature',
  defaultValue
)

// 在组件中使用
const [newFeature, setNewFeature] = useAtom(newFeatureAtom)
```

### 样式开发

#### Tailwind CSS 使用

项目使用 Tailwind CSS 进行样式开发，配置了微信风格的颜色系统：

```javascript
// tailwind.config.js
theme: {
  extend: {
    colors: {
      wechatBrand: {
        1: '#069A4D',
        2: '#06AE57',
        3: '#07C160',
        4: '#39CD80',
        5: '#B4ECCF',
      },
      wechatBG: {
        1: '#000',
        2: '#333',
        3: '#EDEDED',
        4: '#F7F7F7',
        5: '#fff',
      },
    }
  }
}
```

#### 样式规范

- 使用 Tailwind 原子类进行样式编写
- 遵循微信设计规范的颜色和间距
- 优先使用预定义的设计 token
- 避免使用内联样式

### 测试开发

#### 单元测试

```typescript
// src/components/__tests__/Component.test.tsx
import { render, screen } from '@testing-library/react'
import Component from '../Component'

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />)
    expect(screen.getByText('Hello')).toBeInTheDocument()
  })
})
```

#### 运行测试

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行测试 UI
pnpm test:ui
```

## 构建和部署

### Web 构建

```bash
# 开发构建
pnpm build

# 生产构建
BUILD_ENV=release pnpm build

# 预览构建结果
pnpm preview
```

### Android 构建

```bash
# 同步 Capacitor
pnpm ionic:sync

# 构建 APK
pnpm android:apk

# 简化构建流程
pnpm android:simple

# 自动更新版本号
pnpm android:version-auto
```

### 构建配置

#### Vite 配置 (vite.config.ts)

```typescript
export default defineConfig(({ mode }) => {
  const isRelease = process.env.BUILD_ENV === 'release'
  const basePath = isRelease ? './' : '/new_mvp/'

  return {
    base: basePath,
    plugins: [
      react({
        jsxImportSource: '@emotion/react',
        plugins: [['@swc/plugin-emotion', {}]],
      }),
      svgr({
        include: '**/*.svg?react',
      }),
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            react: ['react', 'react-dom', 'react-router-dom'],
            antd: ['antd', '@ant-design/icons', 'dayjs'],
            slate: ['slate', 'slate-history', 'slate-react'],
          },
        },
      },
    },
  }
})
```

#### Capacitor 配置 (capacitor.config.ts)

```typescript
const config: CapacitorConfig = {
  appId: 'tina.chat.dev',
  appName: 'Tina-Chat Dev',
  webDir: 'dist',
  server: {
    androidScheme: 'http',
    cleartext: true,
    url: 'http://********:5173' // 开发模式
  },
  android: {
    overrideUserAgent: 'Tina Chat Android App',
    webContentsDebuggingEnabled: true
  }
}
```

## 性能优化

### 代码分割

项目配置了智能的代码分割策略：

```typescript
// vite.config.ts
manualChunks: {
  react: ['react', 'react-dom', 'react-router-dom'],
  antd: ['antd', '@ant-design/icons', 'dayjs'],
  slate: ['slate', 'slate-history', 'slate-react'],
  faker: ['@faker-js/faker'],
  i18n: ['i18next', 'i18next-browser-languagedetector', 'react-i18next'],
}
```

### 组件优化

- 使用 `React.memo` 避免不必要的重渲染
- 使用 `useCallback` 和 `useMemo` 优化计算
- 懒加载大型组件和页面

### 状态优化

- 使用 `atomFamily` 避免重复创建 atom
- 使用 `focusAtom` 进行精确状态更新
- 合理使用状态持久化

## 调试和故障排除

### 开发工具

- **React DevTools**: 组件状��调试
- **Jotai DevTools**: 状态管理调试
- **Chrome DevTools**: 网络和性能调试

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
rm -rf node_modules .swc dist
pnpm install

# 检查 Node.js 版本
node --version # 应该 >= 18
```

#### 2. Android 构建失败
```bash
# 检查 Java 版本
java --version # 应该是 Java 17+

# 清理 Android 构建缓存
cd android
./gradlew clean

# 重新同步
pnpm ionic:sync
```

#### 3. 样式不生效
- 检查 Tailwind 类名是否正确
- 确认 `preflight: false` 配置
- 检查 CSS 加载顺序

#### 4. 状态不更新
- 检查 atom 的使用是否正确
- 确认状态更新是否为不可变操作
- 使用 Jotai DevTools 调试状态变化

### 日志调试

```typescript
// 开发环境日志
if (import.meta.env.DEV) {
  console.log('Debug info:', data)
}

// 状态变化监听
const unsubscribe = mainStore.sub(someAtom, () => {
  console.log('State changed:', mainStore.get(someAtom))
})
```

## 最佳实践

### 代码规范

#### TypeScript 使用
- 严格的类型定义，避免使用 `any`
- 使用接口定义复杂数据结构
- 利用联合类型和泛型提高代码复用性

#### 组件设计
- 单一职责原则，每个组件只负责一个功能
- 使用 Props 接口定义组件属性
- 合理使用 `memo` 优化性能

#### 状态管理
- 保持状态的不可变性
- 使用 atom 进行状态分割
- 避免在组件中直接修改全局状态

### 性能最佳实践

- 使用 `React.lazy` 进行路由级别的代码分割
- 图片资源使用 WebP 格式
- 合理使用缓存策略
- 避免在渲染函数中创建对象和函数

### 安全最佳实践

- 对用户输入进行验证和清理
- 使用 CSP 防止 XSS 攻击
- iframe 使用沙盒模式
- 敏感信息不要存储在前端

## 贡献指南

### 提交规范

使用 Conventional Commits 规范：

```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或辅助工具的变动
```

### 开发流程

1. Fork 项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交代码：`git commit -m "feat: add new feature"`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 代码审查

- 确保代码通过所有测试
- 遵循项目的代码规范
- 添加必要的文档和注释
- 考虑性能和安全影响

## 常用命令

### 开发命令
```bash
pnpm dev                    # 启动开发服务器
pnpm build                  # 构建项目
pnpm preview               # 预览构建结果
pnpm test                  # 运行测试
pnpm format                # 格式化代码
```

### Android 命令
```bash
pnpm ionic:sync            # 同步到 Android
pnpm ionic:open            # 打开 Android Studio
pnpm android:apk           # 构建 APK
pnpm android:simple        # 简化构建流程
```

### 工具命令
```bash
pnpm format:check          # 检查代码格式
pnpm test:coverage         # 生成测试覆盖率
pnpm test:ui               # 启动测试 UI
```

## 相关资源

### 官方文档
- [React 官方文档](https://react.dev/)
- [Ionic React 文档](https://ionicframework.com/docs/react)
- [Capacitor 文档](https://capacitorjs.com/docs)
- [Jotai 文档](https://jotai.org/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

### 社区资源
- [Ionic Community](https://ionic.io/community)
- [React Community](https://reactjs.org/community/support.html)
- [Capacitor Community](https://github.com/capacitor-community)

### 工具和插件
- [React DevTools](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
- [Jotai DevTools](https://github.com/jotaijs/jotai-devtools)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

---

## 更新日志

### v1.3.0 (当前版本)
- 完善的消息类型系统
- 优化的状态管理架构
- 改进的构建和部署流程
- 增强的测试覆盖率

---

*本文档会随着项目的发展持续更新，如有疑问或建议，请提交 Issue 或 Pull Request。*