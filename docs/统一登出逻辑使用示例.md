# 统一登出逻辑使用示例

## 自动触发场景

### 1. HTTP API调用错误

当使用 `fetchGet` 或 `fetchPost` 进行API调用时，如果返回以下状态码会自动触发登出：

```typescript
import { fetchPost } from '@/tina/lib/fetch'

try {
  const result = await fetchPost('/api/user/profile', { userId: '123' })
  // 正常处理结果
} catch (error) {
  // 如果是401、403、400、429等状态码，会自动触发统一登出
  // 这里只需要处理其他类型的错误
  console.error('API调用失败:', error)
}
```

### 2. EmotionMindClient调用错误

聊天服务相关的API调用也会自动检测认证错误：

```typescript
import { chatServiceManager } from '@/tina/services/chat-service-manager'

try {
  await chatServiceManager.sendMessage('Hello')
} catch (error) {
  // 认证相关错误会自动触发登出
  console.error('发送消息失败:', error)
}
```

### 3. Session API调用错误

会话相关的API调用通过axios拦截器自动处理：

```typescript
import { getUserSessions } from '@/tina/lib/session'

try {
  const sessions = await getUserSessions(token)
} catch (error) {
  // 认证失败会自动触发登出
  console.error('获取会话失败:', error)
}
```

## 手动触发场景

### 1. 用户主动登出

```typescript
import { performUnifiedLogout } from '@/tina/lib/auth'

const handleLogout = async () => {
  try {
    await performUnifiedLogout('用户主动登出')
  } catch (error) {
    console.error('登出失败:', error)
    // 即使失败，用户也会被重定向到登录页面
  }
}
```

### 2. 检测到异常状态时手动触发

```typescript
import { shouldPerformUnifiedLogout, performUnifiedLogout } from '@/tina/lib/auth'

const checkAndLogout = async (error: any) => {
  if (shouldPerformUnifiedLogout(undefined, error)) {
    console.log('检测到需要登出的错误')
    await performUnifiedLogout(`异常状态: ${error.message}`)
  }
}
```

## 错误处理最佳实践

### 1. 在组件中处理错误

```typescript
import { useCallback } from 'react'
import { shouldPerformUnifiedLogout, performUnifiedLogout } from '@/tina/lib/auth'

const MyComponent = () => {
  const handleApiError = useCallback(async (error: any) => {
    console.error('API错误:', error)
    
    // 检查是否需要登出（通常不需要手动检查，因为已经自动处理）
    if (shouldPerformUnifiedLogout(undefined, error)) {
      // 可以在这里添加用户提示
      console.log('检测到认证错误，即将登出...')
    }
    
    // 处理其他类型的错误
    // ...
  }, [])

  return (
    // 组件内容
  )
}
```

### 2. 在服务层处理错误

```typescript
class ApiService {
  async callApi(endpoint: string, data: any) {
    try {
      const response = await fetchPost(endpoint, data)
      return response
    } catch (error) {
      // 认证错误会自动处理，这里只需要处理业务错误
      if (error instanceof HttpError && !error.shouldLogout()) {
        // 处理非认证相关的HTTP错误
        throw new Error(`业务错误: ${error.message}`)
      }
      
      // 重新抛出原始错误
      throw error
    }
  }
}
```

## 调试和监控

### 1. 查看登出日志

统一登出逻辑使用特定的日志前缀，便于调试：

```
🚪 [UnifiedLogout] 开始统一登出流程 原因: HTTP 401: Unauthorized
🚪 [UnifiedLogout] 断开聊天服务连接
🚪 [UnifiedLogout] 重置聊天服务管理器
🚪 [UnifiedLogout] 清除认证状态
🚪 [UnifiedLogout] 跳转到登录页面
```

### 2. 监控登出触发

可以在各个检测点添加监控：

```typescript
// 在fetch.ts中
if (shouldPerformUnifiedLogout(response.status, httpError)) {
  console.log('🚪 [fetch] 检测到需要登出的HTTP错误:', response.status)
  // 可以在这里添加监控代码
  // analytics.track('auto_logout_triggered', { source: 'fetch', status: response.status })
}
```

## 注意事项

### 1. 异步执行
统一登出是异步执行的，不会阻塞当前的错误处理流程：

```typescript
// 错误仍然会正常抛出，登出在后台执行
try {
  await fetchPost('/api/data', {})
} catch (error) {
  // 这里的错误处理会正常执行
  // 如果是认证错误，登出会在后台异步执行
  console.error('请求失败:', error)
}
```

### 2. 页面跳转
登出完成后会强制跳转到登录页面，这会触发完整的页面刷新：

```typescript
// 登出完成后
window.location.href = '/new_mvp/'  // 强制跳转，清除所有状态
```

### 3. 容错机制
即使登出过程中出现错误，也会确保基本的清理和跳转：

```typescript
try {
  // 执行完整的登出流程
  await chatServiceManager.disconnect()
  await chatServiceManager.reset()
  auth.reset()
} catch (error) {
  // 即使出错，也要确保认证状态被清除
  auth.reset()
} finally {
  // 无论如何都要跳转到登录页面
  window.location.href = '/new_mvp/'
}
```

## 测试建议

### 1. 模拟认证失败
```typescript
// 在开发环境中测试
const testAuthError = async () => {
  const error = new Error('Token expired')
  if (shouldPerformUnifiedLogout(undefined, error)) {
    await performUnifiedLogout('测试认证失败')
  }
}
```

### 2. 模拟HTTP错误
```typescript
// 测试不同的HTTP状态码
const testHttpError = async () => {
  for (const status of [400, 401, 403, 429]) {
    if (shouldPerformUnifiedLogout(status)) {
      console.log(`状态码 ${status} 会触发登出`)
    }
  }
}
```
