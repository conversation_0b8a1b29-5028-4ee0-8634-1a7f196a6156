# 对话组件开发指南

## 概述

本指南基于 News 组件的实现，详细说明如何在微信对话系统中开发新的消息类型组件。

## 架构概览

### 整体架构
```
ConversationAPIProvider (context.tsx)
├── ConversationList (index.tsx)
│   └── ConversationItem (index.tsx)
│       ├── News.tsx
│       ├── Text.tsx
│       ├── PersonalCard.tsx
│       └── ... 其他消息类型组件
└── ConversationFooter
    └── Input (index.tsx)
```

### 数据流向
1. **状态管理**: 使用 Jotai 管理对话列表状态
2. **类型定义**: 在 `typing.ts` 中定义消息类型接口
3. **上下文传递**: 通过 `ConversationAPIContext` 传递发送消息的方法
4. **组件渲染**: 在 `ConversationItem/index.tsx` 中根据消息类型渲染对应组件

## 开发新组件的步骤

### 1. 定义类型接口

在 `src/stateV2/conversation/typing.ts` 中添加新的消息类型：

```typescript
// 1. 在枚举中添加新类型
export enum EConversationType {
  // ... 现有类型
  news = "news",
  yourNewType = "yourNewType", // 添加新类型
}

// 2. 定义接口
export interface IConversationTypeYourNewType extends IConversationItemBase {
  type: EConversationType.yourNewType;
  // 添加特定字段
  title: string;
  content: string;
  // ... 其他字段
}

// 3. 添加到联合类型
export type TConversationItem =
  | IConversationTypeText
  | IConversationTypeNews
  | IConversationTypeYourNewType // 添加新类型
  | ... // 其他类型
```

### 2. 创建组件文件

在 `src/pages/wechat/conversation/ConversationList/ConversationItem/` 目录下创建新组件：

```typescript
// YourNewType.tsx
import { h } from "@/components/HashAssets";
import type { IStateProfile } from "@/stateV2/profile";
import { memo } from "react";
import CommonBlock from "./CommonBlock";

type Props = {
  // 从类型接口中提取需要的字段
  title: string;
  content: string;
  upperText: string;
  senderId: IStateProfile["id"];
};

const YourNewType = ({ title, content, upperText, senderId }: Props) => {
  return (
    <CommonBlock
      upperText={upperText}
      senderId={senderId}
      blockClassName="w-4/5" // 控制整体宽度
      innerBlockClassName="w-full bg-white before:bg-white p-0" // 控制内容块样式
    >
      {/* 组件内容 */}
      <div className="p-3">
        <h3 className="font-medium text-black text-sm">{title}</h3>
        <p className="text-gray-500 text-xs mt-1">{content}</p>
      </div>
    </CommonBlock>
  );
};

export default memo(YourNewType);
```

### 3. 在 ConversationItem 中注册组件

在 `src/pages/wechat/conversation/ConversationList/ConversationItem/index.tsx` 中：

```typescript
// 1. 导入新组件
import YourNewType from "./YourNewType";

// 2. 在 switch 语句中添加 case
const ConversationItem = ({ data }: Props) => {
  // ... 现有代码
  
  switch (data.type) {
    // ... 现有 case
    case EConversationType.yourNewType:
      return (
        <YourNewType
          title={data.title}
          content={data.content}
          upperText={upperText || ""}
          senderId={senderId}
        />
      );
    // ... 其他 case
  }
};
```

### 4. 添加发送方法

在 `src/pages/wechat/conversation/context.tsx` 中添加发送方法：

```typescript
// 1. 在接口中添加方法定义
interface IConversationAPIContext {
  // ... 现有方法
  sendYourNewType: (title: string, content: string) => void;
}

// 2. 实现发送方法
const sendYourNewType = useCallback((title: string, content: string) => {
  setConversationList((prev) => {
    const newMessage: TConversationItem = {
      type: EConversationType.yourNewType,
      role: "friend", // 或 "mine"
      title,
      content,
      id: `yourNewType-${Date.now()}`,
      sendTimestamp: Date.now(),
      upperText: fromLastGenerateUpperText(prev),
    };
    return [...prev, newMessage];
  });
  scrollConversationListToBtm();
}, [setConversationList, scrollConversationListToBtm]);

// 3. 添加到 value 对象
const value: IConversationAPIContext = useMemo(() => {
  return {
    // ... 现有方法
    sendYourNewType,
  };
}, [/* 依赖项 */]);
```

### 5. 在输入组件中使用

在 `src/pages/wechat/conversation/ConversationFooter/Input/index.tsx` 中：

```typescript
// 1. 从 context 中获取发送方法
const { sendYourNewType } = useConversationAPI();

// 2. 在模拟回复中添加新类型
const generateMockReply = useCallback((userMessage: string) => {
  const firstChar = userMessage.trim().charAt(0);
  
  switch (firstChar) {
    // ... 现有 case
    case '8': // 新的触发条件
      sendYourNewType("示例标题", "示例内容");
      break;
    // ... 其他 case
  }
}, [/* 依赖项 */]);
```

## CommonBlock 组件详解

`CommonBlock` 是所有对话消息的通用布局组件，提供以下功能：

### 主要特性
- **头像显示**: 自动根据 `senderId` 显示用户头像
- **消息气泡**: 提供标准的微信消息气泡样式
- **方向控制**: 自动根据 `role` (mine/friend) 调整布局方向
- **上方文本**: 显示时间戳等信息
- **交互功能**: 支持点击头像查看用户信息，双击拍一拍

### 重要属性
```typescript
interface Props {
  upperText: string;           // 上方显示的文本（通常是时间）
  senderId: string;           // 发送者ID，用于获取头像
  blockClassName?: string;    // 整体容器样式类
  innerBlockClassName?: string; // 内容块样式类
  blockStyle?: CSSProperties; // 自定义样式
  extraElement?: ReactNode;   // 额外元素
  hideAvatar?: boolean;       // 是否隐藏头像
  onClick?: MouseEventHandler; // 点击事件
}
```

### 样式约定
- `blockClassName`: 控制整体宽度和布局，如 `"w-4/5"` 表示占父容器 80% 宽度
- `innerBlockClassName`: 控制消息气泡样式，包括：
  - 背景色：`bg-white` (朋友消息) 或 `bg-[#8CE97F]` (自己消息)
  - 气泡尖角：`before:bg-white` 或 `before:bg-[#8CE97F]`
  - 内边距：`p-0`, `p-3` 等
  - 边框：`border-t border-gray-100` 等

## 样式设计规范

### 颜色规范
- **朋友消息背景**: `bg-white`
- **自己消息背景**: `bg-[#8CE97F]`
- **主要文本**: `text-black`
- **次要文本**: `text-gray-500`
- **辅助文本**: `text-gray-400`
- **分割线**: `border-gray-100`

### 尺寸规范
- **头像**: `h-10 w-10` (40x40px)
- **消息最大宽度**: `max-w-[85%]`
- **内容宽度**: `w-4/5` 或 `w-full`
- **文字大小**: `text-sm` (主要), `text-xs` (次要)

### 间距规范
- **组件间距**: `space-y-4`
- **内容内边距**: `p-3`, `px-3 py-2`
- **元素间距**: `mt-1`, `mb-2`, `ml-2`

## News 组件实现分析

### 组件结构
```typescript
const News = ({ title, description, imageUrl, source, url, upperText, senderId }: Props) => {
  const [isWebViewerOpen, setIsWebViewerOpen] = useState(false);

  const handleClick = () => {
    setIsWebViewerOpen(true);
  };

  const handleCloseWebViewer = () => {
    setIsWebViewerOpen(false);
  };

  return (
    <>
      <CommonBlock
        upperText={upperText}
        senderId={senderId}
        blockClassName="w-4/5"
        innerBlockClassName="w-full bg-white before:bg-white p-0 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={handleClick}
      >
        {/* 标题区域 */}
        <div className="px-3 pt-3 pb-2">
          <div className="font-medium text-black text-sm line-clamp-2 leading-tight">
            {title}
          </div>
        </div>
        
        {/* 内容区域：描述 + 图片 */}
        <div className="flex px-3 pb-2">
          <div className="flex-1 pr-3">
            <div className="text-gray-500 text-xs line-clamp-3 leading-tight">
              {description}
            </div>
          </div>
          <div className="w-12 h-12 flex-shrink-0">
            <h.img src={imageUrl} className="w-full h-full object-cover" />
          </div>
        </div>
        
        {/* 底部来源 */}
        <div className="border-t border-gray-100 px-3 py-1.5 bg-gray-50">
          <div className="flex items-center text-xs text-gray-400">
            <span>{source}</span>
          </div>
        </div>
      </CommonBlock>

      {/* 全屏网页查看器 */}
      <WebViewer
        url={url}
        title={title}
        isOpen={isWebViewerOpen}
        onClose={handleCloseWebViewer}
      />
    </>
  );
};
```

### 设计特点
1. **三段式布局**: 标题、内容、来源
2. **图文混排**: 左侧文字描述，右侧缩略图
3. **视觉层次**: 通过字体大小、颜色区分重要性
4. **文本截断**: 使用 `line-clamp-2` 和 `line-clamp-3` 控制显示行数
5. **交互功能**: 点击新闻卡片打开全屏网页查看器
6. **视觉反馈**: 添加 `cursor-pointer` 和 `hover:bg-gray-50` 提供交互提示

### 全屏网页查看器 (WebViewer)
News 组件集成了全屏网页查看器功能，具有以下特性：

#### 主要功能
- **全屏显示**: 使用 Portal 渲染到 document.body，实现全屏覆盖
- **iframe 嵌入**: 安全地加载外部网页内容
- **加载状态**: 显示加载动画，提升用户体验
- **键盘支持**: 支持 ESC 键关闭
- **点击遮罩关闭**: 点击背景区域关闭查看器
- **滚动锁定**: 打开时禁止背景页面滚动

#### 动画效果
WebViewer 实现了流畅的展开和收起动画，提升用户体验：

**展开动画**:
- News 组件点击时先放大到 200% (`scale-200`)，提供强烈的视觉反馈
- 200ms 后打开 WebViewer，背景遮罩从透明渐变到半透明
- WebViewer 从 50% 缩放到 100%，同时透明度从 0.5 到 1，向上位移 4px
- 形成从 News 组件极度放大状态到 WebViewer 的戏剧性过渡
- 添加背景模糊效果 (`backdrop-blur-sm`) 增强层次感

**收起动画**:
- 关闭时先让 News 组件放大到 200%，提供强烈的视觉连接
- WebViewer 反向播放展开动画，从 100% 缩小到 50% 并淡出
- 350ms 动画时长确保流畅过渡
- 动画完成后 News 组件恢复正常大小，WebViewer 移除 DOM 元素

**加载动画**:
- iframe 加载时显示旋转加载器
- 加载完成后 iframe 淡入显示
- 加载状态有独立的透明度过渡

#### 安全特性
- **沙盒模式**: iframe 使用 `sandbox` 属性限制权限
- **允许的权限**: `allow-scripts allow-same-origin allow-forms allow-popups`

#### 样式特点
- **响应式设计**: 最大宽度 `max-w-6xl`，最大高度 `max-h-[90vh]`
- **现代 UI**: 圆角、阴影、过渡动画
- **清晰的层次**: 头部显示标题和 URL，主体显示网页内容
- **动画优化**: 使用 `transform` 和 `opacity` 实现硬件加速

### 类型定义更新
```typescript
export interface IConversationTypeNews extends IConversationItemBase {
  type: EConversationType.news;
  title: string;
  description: string;
  imageUrl: string;
  source: string;
  url: string; // 新增：网页链接
}
```

### 发送方法更新
```typescript
sendFriendNews: (
  title: string, 
  description: string, 
  imageUrl: string, 
  source: string, 
  url: string // 新增参数
) => void;
```

## 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一种消息类型
- **可复用性**: 使用 CommonBlock 提供统一的布局和交互
- **类型安全**: 严格定义 TypeScript 接口
- **性能优化**: 使用 `memo` 避免不必要的重渲染

### 2. 样式编写建议
- **使用 Tailwind CSS**: 保持样式一致性
- **响应式设计**: 考虑移动端适配
- **语义化类名**: 使用有意义的类名组合
- **避免内联样式**: 优先使用 Tailwind 类

### 3. 状态管理
- **使用 Jotai**: 保持状态管理的一致性
- **不可变更新**: 使用扩展运算符更新状态
- **类型安全**: 确保状态更新的类型正确性

### 4. 错误处理
- **默认值**: 为可选属性提供合理的默认值
- **边界情况**: 处理空数据、长文本等情况
- **用户体验**: 提供加载状态和错误提示

## 测试建议

### 单元测试
- 测试组件渲染是否正确
- 测试属性传递是否正确
- 测试交互行为是否符合预期

### 集成测试
- 测试消息发送流程
- 测试状态更新是否正确
- 测试组件间的协作

### 视觉测试
- 测试不同屏幕尺寸下的显示效果
- 测试长文本的处理
- 测试图片加载失败的情况

## 调试技巧

### 开发工具
- 使用 React DevTools 查看组件状态
- 使用 Jotai DevTools 调试状态管理
- 使用浏览器开发者工具调试样式

### 常见问题
1. **样式不生效**: 检查 Tailwind 类名是否正确
2. **状态不更新**: 检查 Jotai atom 的使用是否正确
3. **类型错误**: 检查接口定义和实际使用是否匹配

## 示例：完整的新组件开发流程

假设我们要开发一个 "位置分享" 组件：

### 1. 定义类型
```typescript
// typing.ts
export enum EConversationType {
  location = "location",
}

export interface IConversationTypeLocation extends IConversationItemBase {
  type: EConversationType.location;
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
}
```

### 2. 创建组件
```typescript
// Location.tsx
import { h } from "@/components/HashAssets";
import type { IStateProfile } from "@/stateV2/profile";
import { memo } from "react";
import CommonBlock from "./CommonBlock";

type Props = {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
  upperText: string;
  senderId: IStateProfile["id"];
};

const Location = ({ latitude, longitude, address, name, upperText, senderId }: Props) => {
  return (
    <CommonBlock
      upperText={upperText}
      senderId={senderId}
      blockClassName="w-4/5"
      innerBlockClassName="w-full bg-white before:bg-white p-0"
    >
      {/* 地图缩略图 */}
      <div className="h-32 bg-gray-200 relative overflow-hidden">
        <h.img 
          src={`https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/${longitude},${latitude},15,0/320x128?access_token=YOUR_TOKEN`}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 bg-red-500 rounded-full border-2 border-white"></div>
        </div>
      </div>
      
      {/* 位置信息 */}
      <div className="p-3">
        {name && (
          <div className="font-medium text-black text-sm mb-1">{name}</div>
        )}
        <div className="text-gray-500 text-xs">{address}</div>
      </div>
    </CommonBlock>
  );
};

export default memo(Location);
```

### 3. 注册和使用
按照前面的步骤在相应文件中注册和使用新组件。

## 组件概述

本项目包含多种对话组件类型，每种组件都有特定的用途和展示方式。

## 组件类型

### 1. News 组件

News 组件用于展示新闻内容，包含标题、描述、图片和来源信息。

#### 特性
- 支持点击展开全屏查看
- 具有精美的动画效果
- 响应式设计

#### 动画效果

**展开动画 (Expand Animation)**:
- News 组件点击时会放大到 200%，提供强烈的视觉反馈
- 200ms 后，WebViewer 打开，背景遮罩从透明过渡到半透明
- WebViewer 从 50% 缩放到 100%，同时透明度从 0 过渡到 1，向上移动 4px，创造从放大的 News 组件到 WebViewer 的戏剧性过渡
- 添加背景模糊效果 (`backdrop-blur-sm`) 增强层次感

**收起动画 (Collapse Animation)**:
- 关闭时，News 组件再次放大到 200%，形成强烈的视觉连接
- WebViewer 反向执行展开动画，从 100% 缩放到 50% 并淡出
- 动画持续时间为 350ms，确保平滑过渡，News 组件恢复正常大小，WebViewer 从 DOM 中移除

### 2. Markdown 组件

Markdown 组件用于展示 Markdown 格式的内容，支持富文本显示和全屏查看。

#### 特性
- 支持完整的 Markdown 语法
- 代码高亮显示
- 表格、链接、图片等元素的美化
- 点击展开全屏查看
- 与 News 组件相同的动画效果

#### 组件结构
- **预览区域**: 显示 Markdown 图标和内容前几行预览
- **简介区域**: 显示简介文本，如果为空则显示"点击查看详细"
- **全屏查看器**: MarkdownViewer 组件，支持完整的 Markdown 渲染

#### 动画效果
- **展开动画**: Markdown 组件点击时放大到 200%，然后打开 MarkdownViewer
- **收起动画**: 关闭时先显示放大的 Markdown 组件，然后恢复正常大小
- 动画时长和效果与 News 组件保持一致

#### Markdown 功能支持
- **语法高亮**: 支持多种编程语言的代码高亮
- **表格**: 美化的表格样式，支持横向滚动
- **链接**: 自动在新窗口打开外部链接
- **图片**: 支持图片显示
- **GFM 扩展**: 支持 GitHub Flavored Markdown

#### 使用示例
```typescript
{
  type: EConversationType.markdown,
  markdownContent: "# 标题\n\n这是一段 **粗体** 文本。",
  summary: "技术文档", // 可选，为空时显示"点击查看详细"
  // ... 其他通用属性
}
```

## 开发规范

### 动画一致性
- 所有可展开组件都应使用相同的动画参数
- 点击时放大到 200% (scale-200)
- 展开动画持续 350ms
- 使用 `ease-out` 缓动函数

### 样式规范
- 使用 Tailwind CSS 进行样式设计
- 保持组件间视觉一致性
- 响应式设计支持

### 性能优化
- 使用 `memo` 包装组件避免不必要的重渲染
- 懒加载大型内容
- 合理使用 `useCallback` 和 `useMemo`

## 总结

通过遵循本指南，您可以快速开发出符合系统架构和设计规范的新对话组件。关键要点：

1. **遵循类型定义规范**
2. **使用 CommonBlock 统一布局**
3. **保持样式一致性**
4. **注重用户体验**
5. **确保代码质量**

如有疑问，可以参考现有组件的实现，或查阅相关技术文档。 