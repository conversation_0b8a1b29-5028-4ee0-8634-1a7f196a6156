# 测试指南

本项目使用 Vitest 作为测试框架，提供快速、现代的测试体验。

## 安装和配置

项目已经配置好了 Vitest 和相关的测试工具：

- **Vitest**: 快速的单元测试框架
- **@testing-library/react**: React 组件测试工具
- **@testing-library/jest-dom**: 额外的 DOM 断言
- **jsdom**: 浏览器环境模拟

## 运行测试

### 基本命令

```bash
# 运行所有测试
pnpm test

# 运行测试一次（CI 模式）
pnpm test:run

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 启动测试 UI 界面
pnpm test:ui
```

### 监视模式

在开发过程中，推荐使用监视模式：

```bash
pnpm test
```

这会启动 Vitest 的监视模式，当文件发生变化时自动重新运行相关测试。

### 运行特定测试

```bash
# 运行特定文件的测试
pnpm test src/utils/procText.test.ts

# 运行匹配特定模式的测试
pnpm test -t "应该正确处理纯文本内容"
```

## 测试文件结构

测试文件应该放在与源文件相同的目录中，并以 `.test.ts` 或 `.test.tsx` 结尾：

```
src/
  utils/
    TextStreamParser.ts
    procText.test.ts      # TextStreamParser 的测试
  components/
    Button.tsx
    Button.test.tsx       # Button 组件的测试
```

## 编写测试

### 基本测试结构

```typescript
import { describe, it, expect, beforeEach } from 'vitest'
import { MyClass } from './MyClass'

describe('MyClass', () => {
  let instance: MyClass

  beforeEach(() => {
    instance = new MyClass()
  })

  describe('基础功能', () => {
    it('应该正确初始化', () => {
      expect(instance).toBeDefined()
    })
  })
})
```

### React 组件测试

```typescript
import { render, screen } from '@testing-library/react'
import { Button } from './Button'

describe('Button', () => {
  it('应该渲染按钮文本', () => {
    render(<Button>点击我</Button>)
    expect(screen.getByText('点击我')).toBeInTheDocument()
  })
})
```

## 测试最佳实践

1. **描述性的测试名称**: 使用中文描述测试的预期行为
2. **AAA 模式**: Arrange（准备）、Act（执行）、Assert（断言）
3. **独立性**: 每个测试应该独立运行，不依赖其他测试
4. **覆盖边界情况**: 测试正常情况、边界情况和错误情况

## 示例：TextStreamParser 测试

项目中的 `src/utils/procText.test.ts` 是一个完整的测试示例，展示了如何测试复杂的文本解析逻辑：

- 基础功能测试
- Markdown 处理测试
- Voice 处理测试
- XML 处理测试
- 复杂场景测试
- 边界情况测试

## 调试测试

### 使用 console.log

```typescript
it('调试测试', () => {
  const result = someFunction()
  console.log('结果:', result)
  expect(result).toBe(expected)
})
```

### 使用 VS Code 调试器

1. 在测试文件中设置断点
2. 运行 "Debug: JavaScript Debug Terminal"
3. 在终端中运行 `pnpm test`

## 持续集成

测试会在以下情况自动运行：

- 提交代码时（pre-commit hook）
- 推送到远程仓库时（CI/CD pipeline）
- 创建 Pull Request 时

确保所有测试都通过后再提交代码。
