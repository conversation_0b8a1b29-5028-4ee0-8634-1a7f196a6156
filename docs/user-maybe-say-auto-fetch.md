# User Maybe Say 主动获取功能实现

## 功能概述

在 `handleLLMResponse` 函数中添加了主动获取 "user maybe say" 消息的功能。当消息状态变为 `finished` 且消息中缺少 "user maybe say" 内容时，系统会自动调用 API 获取用户可能说的建议。

## 实现文件

### 1. API 接口文件
**文件路径**: `src/tina/lib/user-maybe-say.ts`

**主要功能**:
- `generateUserMaybeSay(userId: string)`: 调用后端 API 获取 user maybe say 内容
- `parseUserMaybeSayContent(content: string)`: 解析 XML 格式的建议内容为数组

**API 规范**:
- **接口地址**: `POST {Gateway}/emotionmind/api/v1/message/generate-user-maybe-say`
- **请求参数**: `{ "user_id": "{{userId}}" }`
- **响应格式**: 
  ```json
  {
    "code": 200,
    "message": "success", 
    "data": {
      "user_maybe_say": "<user_maybe_say>\n继续这个话题\n还有其他相关的吗？\n今天天气怎么样？\n</user_maybe_say>",
      "generated": true
    }
  }
  ```

### 2. 核心逻辑修改
**文件路径**: `src/pages/wechat/conversation/context.tsx`

**新增函数**:
- `checkForUserMaybeSay(outputs: any[]): boolean`: 检查消息输出中是否包含 user maybe say 内容
- `fetchUserMaybeSay(messageId: string, timestamp: string)`: 主动获取并添加 user maybe say 消息

**修改的函数**:
- `handleLLMResponse`: 在消息 `finished` 处理逻辑中添加检测和获取逻辑

## 工作流程

1. **消息完成检测**: 当收到 `finished: true` 的消息时
2. **内容解析**: 调用 `parser.finalize()` 处理剩余内容
3. **检测 User Maybe Say**: 使用 `checkForUserMaybeSay()` 检查是否包含建议内容
4. **主动获取**: 如果缺少建议内容，调用 `fetchUserMaybeSay()` 主动获取
5. **API 调用**: 使用当前用户ID调用后端 API
6. **内容解析**: 解析返回的 XML 格式建议内容
7. **消息添加**: 创建 `userMaybeSay` 类型消息并添加到对话列表

## 错误处理

- **用户未登录**: 静默跳过，记录警告日志
- **API 调用失败**: 静默处理，不影响主要功能
- **解析失败**: 返回空数组，不添加建议消息

## 测试

### 单元测试
**文件路径**: `src/tina/lib/__tests__/user-maybe-say.test.ts`

**测试覆盖**:
- ✅ XML 内容解析功能
- ✅ API 调用成功场景
- ✅ 用户未登录错误处理
- ✅ API 返回错误处理
- ✅ 空行过滤功能

### 功能测试页面
**文件路径**: `src/pages/test-user-maybe-say.tsx`

**访问路径**: `http://localhost:5174/new_mvp/#/test-user-maybe-say`

**测试功能**:
- 手动输入用户ID测试
- 使用当前登录用户测试
- API 响应查看
- 建议内容解析验证

## 注意事项

1. **服务端 Bug**: 同一条消息的 `message_id` 可能多次触发 `finished` 状态，但无需特殊处理
2. **性能考虑**: API 调用是异步的，不会阻塞主要消息处理流程
3. **用户体验**: 获取失败时静默处理，不显示错误提示
4. **认证**: 使用当前用户的认证 token 进行 API 调用

## 使用方法

功能已自动集成到现有的消息处理流程中，无需额外配置。当 AI 回复消息完成且缺少用户建议时，系统会自动获取并显示建议选项。

## 相关文件

- `src/tina/lib/user-maybe-say.ts` - API 接口实现
- `src/pages/wechat/conversation/context.tsx` - 核心逻辑集成
- `src/tina/lib/__tests__/user-maybe-say.test.ts` - 单元测试
- `src/pages/test-user-maybe-say.tsx` - 功能测试页面
- `src/App.tsx` - 路由配置
- `src/pages/TestPage.tsx` - 测试页面链接
