package tina.chat;

import android.content.Context;
import android.webkit.JavascriptInterface;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.AudioFormat;
import android.os.Handler;
import android.os.Looper;
import android.webkit.WebView;

import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;
import org.json.JSONObject;

public class FunASRManager {
    private Context context;
    private WebView webView;
    private AudioRecord audioRecord;
    private boolean isRecording = false;
    private boolean isConnected = false;
    private ExecutorService recordingExecutor;
    private WebSocket webSocket;
    private OkHttpClient httpClient;
    private Handler mainHandler;
    
    // 音频配置
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int CHUNK_SIZE = 960; // 对应 [5,10,5] 配置
    
    public FunASRManager(Context context, WebView webView) {
        this.context = context;
        this.webView = webView;
        this.recordingExecutor = Executors.newSingleThreadExecutor();
        this.httpClient = new OkHttpClient();
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    @JavascriptInterface
    public void connect(String serverUrl, String callbackName) {
        if (isConnected) {
            executeCallback(callbackName, "{ \"success\": true, \"message\": \"已连接\" }");
            return;
        }
        
        Request request = new Request.Builder()
            .url(serverUrl)
            .build();
            
        webSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                isConnected = true;
                executeCallback(callbackName, "{ \"success\": true, \"message\": \"连接成功\" }");
                executeStatusCallback("connected");
            }
            
            @Override
            public void onMessage(WebSocket webSocket, String text) {
                executeResultCallback(text);
            }
            
            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                isConnected = false;
                executeErrorCallback("WebSocket连接失败: " + t.getMessage());
                executeCallback(callbackName, 
                    "{ \"success\": false, \"message\": \"连接失败: " + t.getMessage() + "\" }");
            }
            
            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                isConnected = false;
                executeStatusCallback("disconnected");
            }
        });
    }
    
    @JavascriptInterface
    public void startRecording(String hotwords, String callbackName) {
        if (isRecording) {
            executeCallback(callbackName, "{ \"success\": false, \"message\": \"正在录音中\" }");
            return;
        }
        
        if (!isConnected) {
            executeCallback(callbackName, "{ \"success\": false, \"message\": \"未连接到服务器\" }");
            return;
        }
        
        // 检查权限
        if (!XXPermissions.isGranted(context, Permission.RECORD_AUDIO)) {
            executeCallback(callbackName, "{ \"success\": false, \"message\": \"麦克风权限未授权\" }");
            return;
        }
        
        try {
            // 发送开始命令
            JSONObject startCommand = new JSONObject();
            startCommand.put("command", "start");
            startCommand.put("hotwords", hotwords);
            webSocket.send(startCommand.toString());
            
            // 初始化AudioRecord
            int bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
            bufferSize = Math.max(bufferSize, CHUNK_SIZE * 4); // 确保缓冲区足够大
            
            audioRecord = new AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            );
            
            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                executeCallback(callbackName, "{ \"success\": false, \"message\": \"音频录制初始化失败\" }");
                return;
            }
            
            isRecording = true;
            audioRecord.startRecording();
            executeStatusCallback("recording");
            
            // 在后台线程中录音
            recordingExecutor.execute(this::recordAudio);
            
            executeCallback(callbackName, "{ \"success\": true, \"message\": \"开始录音\" }");
            
        } catch (Exception e) {
            executeCallback(callbackName, 
                "{ \"success\": false, \"message\": \"启动录音失败: " + e.getMessage() + "\" }");
        }
    }
    
    @JavascriptInterface
    public void stopRecording(String callbackName) {
        if (!isRecording) {
            executeCallback(callbackName, "{ \"success\": false, \"message\": \"未在录音\" }");
            return;
        }
        
        try {
            isRecording = false;
            
            if (audioRecord != null) {
                audioRecord.stop();
                audioRecord.release();
                audioRecord = null;
            }
            
            // 发送停止命令
            if (webSocket != null && isConnected) {
                JSONObject stopCommand = new JSONObject();
                stopCommand.put("command", "stop");
                webSocket.send(stopCommand.toString());
            }
            
            executeStatusCallback("stopped");
            executeCallback(callbackName, "{ \"success\": true, \"message\": \"停止录音\" }");
            
        } catch (Exception e) {
            executeCallback(callbackName, 
                "{ \"success\": false, \"message\": \"停止录音失败: " + e.getMessage() + "\" }");
        }
    }
    
    @JavascriptInterface
    public void disconnect(String callbackName) {
        try {
            if (isRecording) {
                stopRecording("temp");
            }
            
            if (webSocket != null) {
                webSocket.close(1000, "用户断开连接");
                webSocket = null;
            }
            
            isConnected = false;
            executeStatusCallback("disconnected");
            executeCallback(callbackName, "{ \"success\": true, \"message\": \"已断开连接\" }");
            
        } catch (Exception e) {
            executeCallback(callbackName, 
                "{ \"success\": false, \"message\": \"断开连接失败: " + e.getMessage() + "\" }");
        }
    }
    
    @JavascriptInterface
    public void getStatus(String callbackName) {
        String result = String.format(
            "{ \"isRecording\": %s, \"isConnected\": %s, \"hasPermission\": %s }",
            isRecording,
            isConnected,
            XXPermissions.isGranted(context, Permission.RECORD_AUDIO)
        );
        executeCallback(callbackName, result);
    }
    
    private void recordAudio() {
        byte[] buffer = new byte[CHUNK_SIZE * 2]; // 16位音频，每个样本2字节
        short[] sampleBuffer = new short[CHUNK_SIZE * 4]; // 更大的缓冲区用于积累数据
        int sampleBufferIndex = 0;
        
        while (isRecording && audioRecord != null) {
            int bytesRead = audioRecord.read(buffer, 0, buffer.length);
            
            if (bytesRead > 0) {
                // 将字节转换为short samples
                int samplesRead = bytesRead / 2;
                for (int i = 0; i < samplesRead; i++) {
                    // Little-endian格式转换 (与Web版本一致)
                    short sample = (short) ((buffer[i * 2] & 0xFF) | ((buffer[i * 2 + 1] & 0xFF) << 8));
                    
                    // 添加到缓冲区
                    if (sampleBufferIndex < sampleBuffer.length) {
                        sampleBuffer[sampleBufferIndex] = sample;
                        sampleBufferIndex++;
                    }
                    
                    // 当缓冲区积累了960个样本时发送 (与Web版本一致)
                    if (sampleBufferIndex >= CHUNK_SIZE) {
                        sendAudioChunk(sampleBuffer, CHUNK_SIZE);
                        
                        // 移动剩余数据到缓冲区开头
                        int remaining = sampleBufferIndex - CHUNK_SIZE;
                        if (remaining > 0) {
                            System.arraycopy(sampleBuffer, CHUNK_SIZE, sampleBuffer, 0, remaining);
                        }
                        sampleBufferIndex = remaining;
                    }
                }
                
                // 处理音频数据用于波形显示
                processAudioForWaveform(buffer, bytesRead);
            }
        }
        
        // 录音结束时发送剩余数据
        if (sampleBufferIndex > 0) {
            sendAudioChunk(sampleBuffer, sampleBufferIndex);
        }
    }
    
    private void sendAudioChunk(short[] samples, int length) {
        if (webSocket != null && isConnected) {
            // 创建ArrayBuffer格式的数据 (与Web版本一致)
            ByteBuffer audioBuffer = ByteBuffer.allocate(length * 2);
            audioBuffer.order(ByteOrder.LITTLE_ENDIAN); // 与Web版本一致
            
            for (int i = 0; i < length; i++) {
                audioBuffer.putShort(samples[i]);
            }
            
            ByteString audioData = ByteString.of(audioBuffer.array());
            webSocket.send(audioData);
        }
    }
    
    private void processAudioForWaveform(byte[] buffer, int length) {
        // 将音频数据分成15段，与Web版本保持一致
        int samplesCount = length / 2; // 16位音频，每两个字节一个样本
        int segmentSampleSize = samplesCount / 15; // 每段包含的样本数
        StringBuilder audioLevels = new StringBuilder("[");
        
        for (int i = 0; i < 15; i++) {
            int start = i * segmentSampleSize * 2; // 转换为字节偏移
            int end = Math.min(start + segmentSampleSize * 2, length - 1);
            
            // 计算该段的RMS
            double sum = 0;
            int samples = 0;
            
            for (int j = start; j < end; j += 2) {
                if (j + 1 < length) {
                    // 将两个字节转换为16位整数 (Little-endian)
                    short sample = (short) ((buffer[j] & 0xFF) | ((buffer[j + 1] & 0xFF) << 8));
                    // 归一化到 -1.0 到 1.0 范围
                    double normalized = sample / 32768.0;
                    sum += normalized * normalized;
                    samples++;
                }
            }
            
            int level;
            if (samples > 0) {
                double rms = Math.sqrt(sum / samples);
                // 转换为0-100的范围，并应用与Web版本相同的放大系数
                level = (int) Math.min(100, Math.max(20, rms * 3000));
            } else {
                level = 20; // 默认最小值
            }
            
            audioLevels.append(level);
            
            if (i < 14) {
                audioLevels.append(",");
            }
        }
        audioLevels.append("]");
        
        // 发送音频级别数据给JS
        mainHandler.post(() -> {
            executeAudioDataCallback(audioLevels.toString());
        });
    }
    
    private void executeResultCallback(String result) {
        String script = String.format("if(window.androidFunASRResultCallback) { window.androidFunASRResultCallback(%s); }", result);
        mainHandler.post(() -> {
            webView.evaluateJavascript(script, null);
        });
    }
    
    private void executeErrorCallback(String error) {
        String script = String.format("if(window.androidFunASRErrorCallback) { window.androidFunASRErrorCallback(\"%s\"); }", error);
        mainHandler.post(() -> {
            webView.evaluateJavascript(script, null);
        });
    }
    
    private void executeStatusCallback(String status) {
        String script = String.format("if(window.androidFunASRStatusCallback) { window.androidFunASRStatusCallback(\"%s\"); }", status);
        mainHandler.post(() -> {
            webView.evaluateJavascript(script, null);
        });
    }
    
    private void executeAudioDataCallback(String audioLevels) {
        String script = String.format("if(window.androidFunASRAudioDataCallback) { window.androidFunASRAudioDataCallback(%s); }", audioLevels);
        webView.evaluateJavascript(script, null);
    }
    
    private void executeCallback(String callbackName, String result) {
        String script = String.format("if(window.%s) { window.%s(%s); }", 
            callbackName, callbackName, result);
        
        mainHandler.post(() -> {
            webView.evaluateJavascript(script, null);
        });
    }
    
    // 清理资源
    public void cleanup() {
        if (isRecording) {
            stopRecording("cleanup");
        }
        
        if (recordingExecutor != null && !recordingExecutor.isShutdown()) {
            recordingExecutor.shutdown();
        }
        
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
        }
    }
} 