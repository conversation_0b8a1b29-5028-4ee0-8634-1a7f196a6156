# StateV2 功能说明和开发指南

## 概述

StateV2 是基于 Jotai 状态管理库构建的微信聊天应用状态管理系统。它提供了完整的对话管理、输入处理、数据持久化等功能。

## 架构设计

### 核心组件

```
stateV2/
├── conversation/           # 对话相关状态管理
│   ├── index.ts           # 统一导出
│   ├── consts.ts          # 常量定义
│   ├── typing.ts          # 类型定义
│   ├── core.ts            # 核心状态逻辑
│   ├── inputter.ts        # 输入框状态
│   └── helpers.ts         # 辅助函数
├── atomWithStorage.ts     # 自定义存储atom
└── store.ts              # 主store配置
```

## 详细功能分析

### 1. 对话类型系统 (typing.ts)

#### 消息类型枚举

```typescript
export enum EConversationType {
  text = 'text', // 文本消息
  image = 'image', // 图片消息
  video = 'video', // 视频消息
  voice = 'voice', // 语音消息
  centerText = 'centerText', // 居中文本
  transfer = 'transfer', // 转账消息
  redPacket = 'redPacket', // 红包消息
  redPacketAcceptedReply = 'redPacketAcceptedReply', // 红包领取回复
  personalCard = 'personalCard', // 个人名片
}
```

#### 消息角色

```typescript
export type TConversationRole = 'mine' | 'friend'
```

#### 消息基础接口

```typescript
export interface IConversationItemBase {
  id: string // 消息唯一标识
  upperText?: string // 上方显示的时间文本
  sendTimestamp?: number // 发送时间戳
  role: TConversationRole // 消息角色（我的/朋友的）
}
```

#### 具体消息类型

**文本消息**

```typescript
export interface IConversationTypeText extends IConversationItemBase {
  type: EConversationType.text
  textContent: Descendant[] // Slate编辑器内容
  referenceId?: string // 引用消息ID（回复功能）
}
```

**转账消息**

```typescript
export interface IConversationTypeTransfer extends IConversationItemBase {
  type: EConversationType.transfer
  originalSender: TConversationRole // 原始发送者
  transferStatus: 'awaiting' | 'accepted' | 'rejected' | 'expired'
  amount: string // 转账金额
  note?: string // 转账备注
}
```

**红包消息**

```typescript
export interface IConversationTypeRedPacket extends IConversationItemBase {
  type: EConversationType.redPacket
  originalSender: TConversationRole
  redPacketStatus: 'awaiting' | 'accepted' | 'expired'
  amount: string
  note?: string
}
```

**语音消息**

```typescript
export interface IConversationTypeVoice extends IConversationItemBase {
  type: EConversationType.voice
  duration: number // 语音时长
  isRead?: boolean // 是否已读
  showStt?: boolean // 是否显示语音转文字
  stt?: string // 语音转文字内容
}
```

### 2. 核心状态管理 (core.ts)

#### 对话列表状态

```typescript
// 使用atomFamily为每个用户创建独立的对话列表
export const conversationListAtom = atomFamily((id: IStateProfile['id']) => {
  return atomWithStorage<TStateConversationList>(
    `conversationList-${id}`,
    MOCK_INIT_CONVERSATION_LIST,
  )
})
```

#### 状态操作函数

```typescript
// 获取对话列表快照
export const getConversationListValueSnapshot = (id: IStateProfile['id']) =>
  mainStore.get(conversationListAtom(id))

// 设置对话列表值
export const setConversationListValue = (
  id: IStateProfile['id'],
  params: SetStateAction<TStateConversationList>,
) => mainStore.set(conversationListAtom(id), params)
```

#### 消息引用状态

```typescript
// 用于消息回复功能的引用状态
export const conversationItemReferenceAtom = atomFamily(
  (params: {
    friendId: IStateProfile['id']
    conversationId: TConversationItem['id']
  }) =>
    focusAtom(conversationListAtom(params.friendId), (optic) =>
      optic.find((v) => v.id === params.conversationId),
    ),
  dequal,
)
```

### 3. 输入框状态管理 (inputter.ts)

#### 输入框配置

```typescript
export type TStateConversationInputterConfig = {
  sendRole: TConversationRole // 发送者角色
}

export const inputterConfigAtom = atom<TStateConversationInputterConfig>({
  sendRole: 'mine',
})
```

#### 输入内容状态

```typescript
// 输入框内容（Slate编辑器格式）
export const inputterValueAtom = atom<Descendant[]>(SLATE_INITIAL_VALUE)

// 最近使用的表情
export const recentUsedEmojiAtom = atomWithStorage<string[]>(
  'recentUsedEmoji',
  [],
)
```

### 4. 数据持久化 (atomWithStorage.ts)

#### 模拟存储系统

```typescript
// 从JSON文件读取初始数据
import localStorageData from '../data/localStorage.json'

// 创建模拟存储
const createMockStorage = (): Pick<
  Storage,
  'getItem' | 'setItem' | 'removeItem'
> => {
  const data = { ...localStorageData }

  return {
    getItem: (key: string): string | null => {
      const value = data[key as keyof typeof data]
      return value ? JSON.stringify(value) : null
    },
    setItem: (key: string, value: string): void => {
      // 更新内存数据并触发事件
    },
    removeItem: (key: string): void => {
      delete (data as any)[key]
    },
  }
}
```

#### 存储事件系统

```typescript
export const storageEventEmitter = new EventTarget()
export const STORAEG_UPDATE_KEY = 'STORAEG_UPDATE'

// 在数据更新时触发事件
storageEventEmitter.dispatchEvent(
  new CustomEvent(STORAEG_UPDATE_KEY, { detail: [key, stringValue] }),
)
```

### 5. 辅助函数 (helpers.ts)

#### 时间文本生成

```typescript
export const fromLastGenerateUpperText = (list: TConversationItem[]) => {
  const last = list[list.length - 1]
  let upperText: undefined | string

  // 如果没有上一条消息或超过4分钟，显示当前时间
  if (!last || !last.sendTimestamp) upperText = dayjs().format('HH:mm')
  if (last?.sendTimestamp && dayjs().diff(last.sendTimestamp, 'minute') >= 4) {
    upperText = dayjs().format('HH:mm')
  }

  return upperText
}
```

## 开发指南

### 1. 添加新的消息类型

#### 步骤1：定义类型

在 `typing.ts` 中添加新的消息类型：

```typescript
// 1. 在枚举中添加新类型
export enum EConversationType {
  // ... 现有类型
  newMessageType = "newMessageType",
}

// 2. 定义接口
export interface IConversationTypeNewMessage extends IConversationItemBase {
  type: EConversationType.newMessageType;
  customField: string;
  // 其他自定义字段
}

// 3. 添加到联合类型
export type TConversationItem =
  | IConversationTypeText
  | IConversationTypeNewMessage  // 新增
  | /* 其他类型 */;
```

#### 步骤2：添加标签

在 `consts.ts` 中添加类型标签：

```typescript
export const ConversationTypeLabel = {
  // ... 现有标签
  [EConversationType.newMessageType]: '新消息类型',
}
```

#### 步骤3：添加模拟数据

在 `consts.ts` 的 `MOCK_INIT_CONVERSATION_LIST` 中添加示例：

```typescript
{
  id: "new-message-1",
  type: EConversationType.newMessageType,
  role: "mine",
  customField: "示例数据",
  upperText: "18:30",
}
```

### 2. 状态操作最佳实践

#### 读取状态

```typescript
// 在组件中使用
const [conversationList] = useAtom(conversationListAtom(conversationId))

// 在非组件中获取快照
const snapshot = getConversationListValueSnapshot(conversationId)
```

#### 更新状态

```typescript
// 在组件中更新
const [, setConversationList] = useAtom(conversationListAtom(conversationId))

setConversationList((prev) => [...prev, newMessage])

// 在非组件中更新
setConversationListValue(conversationId, (prev) => [...prev, newMessage])
```

#### 条件更新

```typescript
setConversationList((prev) =>
  prev.map((item) =>
    item.id === targetId ? { ...item, status: 'updated' } : item,
  ),
)
```

### 3. 自定义存储

#### 创建新的存储atom

```typescript
import atomWithStorage from '../atomWithStorage'

export const customDataAtom = atomWithStorage<CustomType>(
  'customData', // 存储键名
  defaultValue, // 默认值
)
```

#### 监听存储变化

```typescript
import { storageEventEmitter, STORAEG_UPDATE_KEY } from '../atomWithStorage'

storageEventEmitter.addEventListener(STORAEG_UPDATE_KEY, (event) => {
  const [key, value] = event.detail
  console.log(`存储更新: ${key} = ${value}`)
})
```

### 4. 性能优化建议

#### 使用atomFamily避免重复创建

```typescript
// ✅ 好的做法
export const conversationListAtom = atomFamily((id: string) =>
  atomWithStorage(`conversationList-${id}`, []),
)

// ❌ 避免这样做
export const createConversationListAtom = (id: string) =>
  atomWithStorage(`conversationList-${id}`, [])
```

#### 使用focusAtom进行精确更新

```typescript
// 只更新特定消息，而不是整个列表
export const messageAtom = atomFamily(
  (params: { friendId: string; messageId: string }) =>
    focusAtom(conversationListAtom(params.friendId), (optic) =>
      optic.find((v) => v.id === params.messageId),
    ),
)
```

#### 避免不必要的重新渲染

```typescript
// 使用dequal进行深度比较
import { dequal } from "dequal/lite";

export const optimizedAtom = atomFamily(
  (params: ComplexParams) => /* atom logic */,
  dequal  // 深度比较参数
);
```

### 5. 调试技巧

#### 状态快照调试

```typescript
// 在开发工具中查看当前状态
console.log('当前对话列表:', getConversationListValueSnapshot(conversationId))
console.log('输入框配置:', getInputterConfigValueSnapshot())
```

#### 状态变化监听

```typescript
// 监听特定atom的变化
const unsubscribe = mainStore.sub(conversationListAtom(conversationId), () => {
  console.log('对话列表已更新')
})

// 记得在适当时机取消订阅
unsubscribe()
```

## 常见问题

### Q: 如何处理异步数据加载？

A: 使用Jotai的异步atom或在组件中处理：

```typescript
export const asyncDataAtom = atom(async (get) => {
  const response = await fetch('/api/data')
  return response.json()
})
```

### Q: 如何在多个组件间共享状态？

A: 使用相同的atom实例：

```typescript
// 在多个组件中使用相同的atom
const Component1 = () => {
  const [data] = useAtom(sharedAtom)
  // ...
}

const Component2 = () => {
  const [data, setData] = useAtom(sharedAtom)
  // ...
}
```

### Q: 如何处理复杂的状态更新逻辑？

A: 创建自定义的更新函数：

```typescript
export const addMessage = (
  conversationId: string,
  message: TConversationItem,
) => {
  setConversationListValue(conversationId, (prev) => {
    const upperText = fromLastGenerateUpperText(prev)
    return [...prev, { ...message, upperText }]
  })
}
```

## 总结

StateV2 提供了一个完整、类型安全、高性能的状态管理解决方案。通过合理使用atomFamily、focusAtom等高级特性，可以构建出响应迅速、易于维护的聊天应用。

关键优势：

- **类型安全**：完整的TypeScript类型定义
- **模块化**：清晰的模块划分和职责分离
- **持久化**：自动的数据持久化和恢复
- **性能优化**：精确的状态更新和最小化重渲染
- **可扩展性**：易于添加新功能和消息类型

```

```
